spring:
  datasource:
    url: ******************************************************************************************************************
    username: sys_network_simulation_rw
    password: FS9jz%6KO5RRdcAA
    driver-class-name: org.postgresql.Driver

aliyun:
  oss:
    endpoint: oss-cn-shenzhen.aliyuncs.com
    accessKeyId: LTAI5tDny4o7JfBbHXxw7wKJ
    accessKeySecret: ******************************
    bucketName: ruijie-nse
    urlPrefix: https://oss-cn-shenzhen.aliyuncs.com

nse:
  ehcache:
    storage-path: ./workspace/ehcache-storage-cloud
    heap: 50
    offheap: 100
    disk: 1024
    jwt-expire: 1440
    service-expire: 30
  jwt:
    expire: 7200
  pdf:
    dir: ./workspace/license-pdf
  license:
    generator:
      output-dir: ./workspace/license-output
      issuer: Ruijie
      format: NSE-LICENSE-1.0
      file-extension: lic
      product-version: 1.0.0
      modules:
        - sys
        - mgr


logging:
  level:
    com:
      ruijie:
        nse:
          cloud: debug

mybatis-plus:
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl