package com.ruijie.nse.cloud.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ruijie.nse.common.entity.DataEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("lic_activation_code")
public class LicenseCode extends DataEntity {
    /**
     * 授权码
     */
    private String code;
    /**
     * 销售订单编号
     */
    private String salesOrderNo;
    /**
     * 销售项目名称
     */
    private String salesProjectName;
    /**
     * 最终客户
     */
    private String finalCustomerName;
    /**
     * 项目销售人员
     */
    private String sellerName;
    /**
     * 有效期开始日期
     */
    private Date validFrom;
    /**
     * 有效期截止日期
     */
    private Date validTo;
    /**
     * 授权类型：新项目授权、变更授权、测试授权
     */
    private String type;
    /**
     * 未使用,已使用,已过期,已撤销
     */
    private String status;
    /**
     * 终端许可数量（管理员）
     */
    private Integer permitMgrCnt;
    /**
     * 终端许可数量（普通用户）
     */
    private Integer permitUserCnt;
    /**
     * 证明文件，只能一份。OSS地址
     */
    private String proveFile;
    /**
     * 证明文件名称
     */
    private String proveFileName;
    /**
     * 授权书，PDF。OSS地址
     */
    private String activationFile;
    /**
     * 授权产品信息
     */
    private String productInfo;
    /**
     * 有效期类型，永久、1年、2年、3年
     */
    private String validType;
}