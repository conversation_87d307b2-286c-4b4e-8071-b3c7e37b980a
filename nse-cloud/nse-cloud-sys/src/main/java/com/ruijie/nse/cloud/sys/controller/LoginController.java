package com.ruijie.nse.cloud.sys.controller;


import com.ruijie.nse.cloud.sys.dto.login.LoginDto;
import com.ruijie.nse.cloud.sys.service.LoginService;
import com.ruijie.nse.common.annotation.Anonymous;
import com.ruijie.nse.common.dto.R;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/api/auth")
@RequiredArgsConstructor
@Anonymous
public class LoginController {


    private final LoginService loginService;


    @PostMapping("/login")
    public R<LoginDto.Resp> login(@Valid @RequestBody LoginDto.Req req) {
        return R.success(loginService.loginByAccount(req.getAccount(), req.getPassword()));
    }

    @DeleteMapping("/logout")
    public R<LoginDto.Resp> logout() {
        return R.success();
    }



}
