package com.ruijie.nse.cloud.sys.service;


import com.ruijie.nse.cloud.sys.dto.login.LoginDto;
import com.ruijie.nse.common.config.security.UserPrincipal;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.enctry.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;


/**
 * 登录服务
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginService {

    private final AuthenticationManager authenticationManager;
    private final EhcacheService ehcacheService;

    /**
     * 账号密码登录
     * @param account
     * @param password
     * @return
     */
    public LoginDto.Resp loginByAccount(String account, String password) {
        log.info("用户登录: {}", account);

        try {
            // 创建认证令牌
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    account, password
            );

            // 进行认证
            Authentication authentication = authenticationManager.authenticate(authToken);

            // 获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            // 生成JWT令牌
            String accessToken = JwtUtil.generateAccessToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername(),
                    userPrincipal.getAuthoritiesString()
            );

            String refreshToken = JwtUtil.generateRefreshToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername()
            );

            // 构建登录结果
            LoginDto.Resp.UserInfo userInfo = LoginDto.Resp.UserInfo.builder()
                    .userId(userPrincipal.getUserId())
                    .username(userPrincipal.getUsername())
                    .build();

            LoginDto.Resp result = LoginDto.Resp.builder()
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType(CommonConstant.Jwt.TOKEN_TYPE)
                    .expiresIn(CommonConstant.Jwt.EXPIRATION / 1000)
                    .userInfo(userInfo)
                    .build();

            // 将token存储到Ehcache
            ehcacheService.put(CacheConstants.JWT_TOKEN_CACHE, userPrincipal.getUserId(), accessToken);

            log.info("用户登录成功: {}", result.toString());
            return result;

        } catch (BadCredentialsException e) {
            log.warn("用户登录失败，用户名或密码错误: {}", account);
            throw new RuntimeException("用户名或密码错误");
        } catch (Exception e) {
            log.error("用户登录失败: {}, 错误: {}", account, e.getMessage(), e);
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }

}
