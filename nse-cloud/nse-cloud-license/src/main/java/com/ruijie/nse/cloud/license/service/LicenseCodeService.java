package com.ruijie.nse.cloud.license.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.cloud.common.utils.pdf.HtmlToPdfUtils;
import com.ruijie.nse.cloud.repository.entity.LicenseCode;
import com.ruijie.nse.cloud.repository.mapper.LicenseCodeDao;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseCodeDto;
import com.ruijie.nse.cloud.license.pojo.dto.LicenseCodeQueryDto;
import com.ruijie.nse.cloud.license.pojo.vo.LicenseCodeInfoVo;
import com.ruijie.nse.cloud.license.pojo.vo.LicenseCodeVo;
import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.utils.bean.BeanCopierUtils;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.tomcat.util.http.fileupload.IOUtils;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.date.DateFormatPool;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.io.file.FileUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class LicenseCodeService extends ServiceImpl<LicenseCodeDao, LicenseCode> {

//    @Autowired
//    private OssService ossService;

    @Value("${spring.profiles.active}")
    private String activeProfile;
    @Value("${nse.pdf.dir}")
    private String pdfDir;

    private static final String NSE_CLOUD_LICENSE_CODE_PATH = "nse-cloud/license-code";


    /**
     * 分页查询授权码记录列表
     *
     * @param queryDto  查询参数
     * @param pageInput 分页参数
     * @return PageOutput<List < LicenseCodeVo>>
     */
    public PageOutput<LicenseCodeVo> listPageQuery(LicenseCodeQueryDto queryDto, PageInput pageInput) {
        Page<LicenseCode> pageInfo = lambdaQuery()
                .like(CharSequenceUtil.isNotBlank(queryDto.getSalesOrderNo()), LicenseCode::getSalesOrderNo, queryDto.getSalesOrderNo())
                .like(CharSequenceUtil.isNotBlank(queryDto.getSalesProjectName()), LicenseCode::getSalesProjectName, queryDto.getSalesProjectName())
                .like(CharSequenceUtil.isNotBlank(queryDto.getFinalCustomerName()), LicenseCode::getFinalCustomerName, queryDto.getFinalCustomerName())
                .like(CharSequenceUtil.isNotBlank(queryDto.getSellerName()), LicenseCode::getSellerName, queryDto.getSellerName())
                .eq(CharSequenceUtil.isNotBlank(queryDto.getLicenseType()), LicenseCode::getType, queryDto.getLicenseType())
                .like(CharSequenceUtil.isNotBlank(queryDto.getLicenseCodeInfo()), LicenseCode::getCode, queryDto.getLicenseCodeInfo())
                .orderByDesc(LicenseCode::getCreatedDate)
                .page(Page.of(pageInput.getPageNumber(), pageInput.getPageSize()));

        List<LicenseCode> records = pageInfo.getRecords();

        List<LicenseCodeVo> licenseCodeVos = BeanCopierUtils.copyListProperties(records, LicenseCodeVo::new, (s, t) -> {
            t.setLicenseType(s.getType());
            t.setSalesProjectName(s.getSalesProjectName());
            t.setSellerName(s.getSellerName());
            t.setLicenseProductInfo(s.getProductInfo());
            t.setLicenseCodeInfo(s.getCode());
            t.setTerminalLicenseCount(CharSequenceUtil.format("普通用户{}+管理员{}", s.getPermitUserCnt(), s.getPermitMgrCnt()));
            t.setLicenseType(s.getType());
            t.setDocumentation(s.getProveFileName());
            t.setDocumentationUrl(s.getProveFile());
            t.setAuthStatus(s.getStatus());
            t.setApplicant(s.getCreatedBy());
            t.setApplyDate(s.getCreatedDate());
        });
        return new PageOutput<>(pageInfo.getTotal(), licenseCodeVos);
    }

    /**
     * 授权码申请
     *
     * @return 主键id
     */
    public LicenseCodeVo apply(LicenseCodeDto licenseCodeDto) throws IOException {
        DecimalFormat df = new DecimalFormat("00");
        String id = IdUtil.getSnowflakeNextIdStr();
        Date now = new Date();
        MultipartFile documentation = licenseCodeDto.getDocumentation();
        LicenseCode licenseCode = BeanCopierUtils.copy(licenseCodeDto, LicenseCode::new, (s, t) -> {
            t.setId(id);
            t.setType(s.getLicenseType());
            t.setPermitUserCnt(s.getPermitUserCnt());
            t.setPermitMgrCnt(s.getPermitMgrCnt());
            t.setValidFrom(now);
            t.setValidTo(calcExpiryDate(now, s.getValidType()));
            t.setProductInfo(CharSequenceUtil.format("RG-NSE-License-{}-{}", df.format(s.getPermitUserCnt()), df.format(s.getPermitMgrCnt())));
            t.setCode(CharSequenceUtil.format("LIC-NSE-License-{}{}{}", df.format(s.getPermitUserCnt()), df.format(s.getPermitMgrCnt()), IdUtil.nanoId()));
            t.setProveFileName(Optional.ofNullable(documentation).map(MultipartFile::getOriginalFilename).orElse(null));
        });

        if (documentation != null) {
            String filePath = CharSequenceUtil.format("{}/{}/{}/{}/{}",
                    activeProfile,
                    NSE_CLOUD_LICENSE_CODE_PATH,
                    DateUtil.format(now, DateFormatPool.NORM_DATE_PATTERN),
                    id, licenseCode.getId() + "_" + documentation.getOriginalFilename());
            // String ossUrl = ossService.uploadFile(documentation, filePath);
            File file = FileUtil.file(filePath);
            licenseCode.setProveFile(file.getAbsolutePath());
            FileUtil.writeFromStream(documentation.getInputStream(), file);
        }
        save(licenseCode);

        return BeanCopierUtils.copy(licenseCode, LicenseCodeVo::new, (s, t) -> {
            t.setId(s.getId());
            t.setTerminalLicenseCount(CharSequenceUtil.format("普通用户{}+管理员{}", s.getPermitUserCnt(), s.getPermitMgrCnt()));
            t.setLicenseType(s.getType());
            t.setLicenseProductInfo(s.getProductInfo());
            t.setLicenseCodeInfo(s.getCode());
            t.setApplicant(s.getCreatedBy());
            t.setApplyDate(s.getCreatedDate());
        });
    }


    /**
     * 计算过期日期
     *
     * @param startDate    开始日期
     * @param validityType 有效期类型 ("1年", "2年", "3年", "30天")
     * @return 过期日期
     */
    public static Date calcExpiryDate(Date startDate, String validityType) {
        LocalDate localDate = startDate.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();

        localDate = switch (validityType) {
            case "1年" -> localDate.plusYears(1);
            case "2年" -> localDate.plusYears(2);
            case "3年" -> localDate.plusYears(3);
            case "30天" -> localDate.plusDays(30);
            case "永久" -> LocalDate.of(294276, 12, 31);
            default -> throw BusinessException.error("", "未知有效期类型");
        };

        return Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 【授权绑定申请】通过授权码获取授权码销售信息回显
     *
     * @param licenseCode 授权码
     * @return LicenseCodeVo
     */
    public LicenseCodeInfoVo salesInfoByCode(String licenseCode) {
        LicenseCode codeInfo = lambdaQuery().eq(LicenseCode::getCode, licenseCode)
                .select(LicenseCode::getSalesOrderNo, LicenseCode::getSalesProjectName,
                        LicenseCode::getFinalCustomerName, LicenseCode::getSellerName)
                .one();
        return Optional.ofNullable(codeInfo)
                .map(it -> BeanCopierUtils.copy(it, LicenseCodeInfoVo::new))
                .orElse(null);
    }

    public void downloadLicenseFile(String id, HttpServletResponse response) throws IOException {
        LicenseCode licenseCode = lambdaQuery().eq(LicenseCode::getId, id)
                .select(LicenseCode::getPermitUserCnt, LicenseCode::getPermitMgrCnt, LicenseCode::getCode)
                .one();

        Map<String, Object> data = new HashMap<>();
        data.put("permitUserCnt", licenseCode.getPermitUserCnt());
        data.put("permitMgrCnt", licenseCode.getPermitMgrCnt());
        data.put("licenseCode", licenseCode.getCode());

        long startTime = System.currentTimeMillis();

        // pdf文件存储相对路径
        Path path = Paths.get(pdfDir, "软件使用授权书.pdf");

        // 微软雅黑在windows系统里的位置如下，linux系统直接拷贝该文件放在linux目录下即可
        // String fontPath = "src/main/resources/fonts/msyh.ttc,0";
        HtmlToPdfUtils.htmlToPdf("license-file.html", data, path.toString(), "ruijie", null);
        log.info("转换结束，耗时：{}ms", System.currentTimeMillis() - startTime);

        // 下载文件
        response.setHeader("content-disposition", "attachment; filename=软件使用授权书.pdf");
        response.setContentType("application/octet-stream");
        try (ServletOutputStream outputStream = response.getOutputStream()) {
            IOUtils.copy(Files.newInputStream(path), outputStream);
        }
    }
}
