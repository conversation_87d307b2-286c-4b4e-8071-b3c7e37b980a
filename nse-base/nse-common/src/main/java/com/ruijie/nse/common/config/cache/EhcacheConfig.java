package com.ruijie.nse.common.config.cache;

import com.ruijie.nse.common.constant.CacheConstants;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.ehcache.config.builders.CacheConfigurationBuilder;
import org.ehcache.config.builders.CacheManagerBuilder;
import org.ehcache.config.builders.ExpiryPolicyBuilder;
import org.ehcache.config.builders.ResourcePoolsBuilder;
import org.ehcache.config.units.MemoryUnit;
import org.ehcache.jsr107.EhcacheCachingProvider;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cache.jcache.JCacheCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.cache.Caching;
import java.io.File;
import java.time.Duration;


/**
 * ehcache缓存。根据cachename区分是否持久化。提供两种缓存
 * jwtcache会进行持久化，servicecache只是内存缓存
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(EhcacheProperties.class)
public class EhcacheConfig {

    @Resource
    private EhcacheProperties ehcacheProperties;
    private javax.cache.CacheManager cacheManager;


    @Bean
    public JCacheCacheManager jCacheCacheManager() {
        // 获取 JCache Provider
        EhcacheCachingProvider provider = (EhcacheCachingProvider) Caching.getCachingProvider("org.ehcache.jsr107.EhcacheCachingProvider");

        // 定义 Ehcache XML 配置的替代方案，通过编程方式配置
        org.ehcache.config.Configuration ehcacheConfig = CacheManagerBuilder.newCacheManagerBuilder()
                .with(CacheManagerBuilder.persistence(new File(ehcacheProperties.getStoragePath())))

                // jwt缓存
                .withCache(CacheConstants.JWT_TOKEN_CACHE,
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(String.class, String.class,
                                ResourcePoolsBuilder.newResourcePoolsBuilder()
                                        .heap(ehcacheProperties.getHeap(), MemoryUnit.MB)
                                        .offheap(ehcacheProperties.getOffheap(), MemoryUnit.MB)
                                        .disk(ehcacheProperties.getDisk(), MemoryUnit.MB, true))
                                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofMinutes(ehcacheProperties.getJwtExpire()))))

                // 服务缓存
                .withCache(CacheConstants.SERVICE_CACHE,
                        CacheConfigurationBuilder.newCacheConfigurationBuilder(String.class, Object.class,
                                ResourcePoolsBuilder.heap(10000))
                                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofMinutes(ehcacheProperties.getServiceExpire()))))
                .build().getRuntimeConfiguration();

        cacheManager = provider.getCacheManager(provider.getDefaultURI(), ehcacheConfig);
        log.info("Ehcache 缓存创建成功 '{}' 和 '{}'", CacheConstants.JWT_TOKEN_CACHE, CacheConstants.SERVICE_CACHE);
        return new JCacheCacheManager(cacheManager);
    }

    @PreDestroy
    public void destroy() {
        if (cacheManager != null) {
            cacheManager.close();
            log.info("Ehcache 缓存管理器已关闭，数据已持久化");
        }
    }
}
