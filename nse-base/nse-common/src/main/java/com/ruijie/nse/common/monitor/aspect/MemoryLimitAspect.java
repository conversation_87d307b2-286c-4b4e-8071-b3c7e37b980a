package com.ruijie.nse.common.monitor.aspect;

import com.ruijie.nse.common.monitor.ResourceMonitor;
import com.ruijie.nse.common.monitor.annotation.MemoryLimit;
import com.ruijie.nse.common.monitor.exception.ResourceExhaustedException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.*;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 内存限制切面
 * <p>
 * 处理@MemoryLimit注解，仅检查内存使用情况
 * </p>
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class MemoryLimitAspect {

    private final ResourceMonitor resourceMonitor;

    /**
     * 环绕通知：处理@MemoryLimit注解
     *
     * @param joinPoint 连接点
     * @return 方法执行结果
     * @throws Throwable 方法执行异常
     */
    @Around("@annotation(com.ruijie.nse.common.monitor.annotation.MemoryLimit)")
    public Object handleMemoryLimit(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        MemoryLimit memoryLimit = method.getAnnotation(MemoryLimit.class);
        
        String methodName = method.getName();
        String className = method.getDeclaringClass().getSimpleName();
        String fullMethodName = className + "." + methodName;
        
        try {
            // 检查内存使用情况
            checkMemoryUsage(memoryLimit, fullMethodName);
            
            // 记录执行前资源使用情况
            if (memoryLimit.logUsage()) {
                resourceMonitor.logResourceUsage(fullMethodName, memoryLimit.checkSystem(), "前");
            }
            
            // 执行原方法
            Object result = joinPoint.proceed();
            
            // 记录执行后资源使用情况
            if (memoryLimit.logUsage()) {
                resourceMonitor.logResourceUsage(fullMethodName, memoryLimit.checkSystem(), "后");
            }
            
            return result;
            
        } catch (ResourceExhaustedException e) {
            // 资源耗尽异常直接抛出
            throw e;
        } catch (Exception e) {
            log.error("执行 {} 时发生异常: {}", fullMethodName, e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 检查内存使用率
     *
     * @param memoryLimit 内存限制配置
     * @param methodName  方法名
     * @throws ResourceExhaustedException 内存耗尽异常
     */
    private void checkMemoryUsage(MemoryLimit memoryLimit, String methodName) {
        double currentMemory = resourceMonitor.getMemoryUsagePercent(memoryLimit.checkSystem());
        double memoryThreshold = memoryLimit.threshold();
        String memoryType = resourceMonitor.getMemoryTypeDescription(memoryLimit.checkSystem());
        
        if (currentMemory > memoryThreshold) {
            String errorMessage = buildErrorMessage(
                    memoryLimit.errorMessagePrefix(), "系统内存使用率过高，请关闭一些不必要的应用后重试"
            );
            
            log.error(errorMessage);
            throw ResourceExhaustedException.memoryExhausted(errorMessage);
        }
    }

    /**
     * 构建错误消息
     *
     * @param prefix      自定义前缀
     * @param baseMessage 基础消息
     * @return 完整错误消息
     */
    private String buildErrorMessage(String prefix, String baseMessage) {
        if (StrUtil.isNotBlank(prefix)) {
            return prefix + ": " + baseMessage;
        }
        return baseMessage;
    }
}