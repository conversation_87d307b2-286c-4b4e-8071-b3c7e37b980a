# 代码评审提示词集合

## 1. 全面代码评审提示词

```
请对以下代码进行全面的代码评审，从以下维度进行分析：

### 代码质量维度
1. **代码结构与设计**
   - 类的职责是否单一，是否符合SOLID原则
   - 方法的粒度是否合适，是否过于复杂
   - 代码的可读性和可维护性
   - 设计模式的使用是否恰当

2. **安全性分析**
   - 是否存在安全漏洞（SQL注入、XSS、敏感信息泄露等）
   - 输入验证是否充分
   - 权限控制是否合理
   - 加密和签名实现是否安全

3. **性能与效率**
   - 算法复杂度是否合理
   - 是否存在性能瓶颈
   - 内存使用是否优化
   - 并发安全性

4. **错误处理**
   - 异常处理是否完善
   - 错误信息是否友好且安全
   - 日志记录是否合理

5. **代码规范**
   - 命名规范是否一致
   - 注释是否充分且准确
   - 代码格式是否规范

### 输出格式
请按照以下格式输出评审报告：
- 🟢 优点：列出代码的亮点
- 🟡 改进建议：列出可以优化的地方
- 🔴 严重问题：列出必须修复的问题
- 📊 评分：给出1-10分的综合评分
- 🛠️ 具体修改建议：提供可执行的改进方案
```

## 2. 安全性专项评审提示词

```
请专门从安全角度评审以下代码：

### 安全检查清单
1. **输入验证**
   - 是否对所有外部输入进行验证
   - 是否防范注入攻击
   - 参数边界检查是否完善

2. **敏感信息处理**
   - 是否有硬编码的密钥或密码
   - 敏感信息是否正确加密存储
   - 日志中是否泄露敏感信息

3. **加密与签名**
   - 加密算法选择是否安全
   - 密钥管理是否规范
   - 签名验证是否严格

4. **权限控制**
   - 是否存在权限绕过风险
   - 访问控制是否细粒度

### 输出要求
- 列出发现的安全风险等级（高/中/低）
- 提供具体的安全加固建议
- 给出安全合规性评估
```

## 3. 性能优化评审提示词

```
请从性能角度评审以下代码：

### 性能分析维度
1. **时间复杂度**
   - 算法效率是否最优
   - 是否存在不必要的循环或递归

2. **空间复杂度**
   - 内存使用是否合理
   - 是否存在内存泄漏风险

3. **I/O操作**
   - 文件操作是否高效
   - 网络请求是否优化

4. **并发性能**
   - 线程安全性
   - 锁的使用是否合理

### 输出要求
- 识别性能瓶颈点
- 提供优化建议和预期收益
- 给出性能测试建议
```

## 4. 架构设计评审提示词

```
请从架构设计角度评审以下代码：

### 架构评估维度
1. **设计原则**
   - 是否遵循SOLID原则
   - 高内聚低耦合程度
   - 依赖关系是否合理

2. **可扩展性**
   - 代码是否易于扩展
   - 是否支持插件化
   - 配置化程度

3. **可测试性**
   - 是否易于单元测试
   - 依赖注入使用是否合理
   - Mock友好程度

4. **可维护性**
   - 代码结构是否清晰
   - 文档是否完善
   - 版本兼容性

### 输出要求
- 评估架构合理性
- 提供重构建议
- 给出长期维护建议
```

## 5. 业务逻辑评审提示词

```
请从业务逻辑角度评审以下代码：

### 业务逻辑检查
1. **需求实现**
   - 是否完整实现业务需求
   - 边界条件处理是否完善
   - 异常场景考虑是否充分

2. **数据一致性**
   - 数据状态管理是否正确
   - 事务处理是否合理
   - 并发场景下的数据一致性

3. **业务规则**
   - 业务规则实现是否正确
   - 验证逻辑是否完善
   - 权限控制是否符合业务要求

### 输出要求
- 识别业务逻辑缺陷
- 提供业务场景测试建议
- 给出业务规则优化建议
```

## 6. 代码质量综合评审提示词

```
作为资深代码评审专家，请对以下代码进行全方位评审：

### 评审标准
- **可读性**：代码是否易于理解和维护
- **健壮性**：错误处理和边界条件
- **效率性**：性能和资源使用
- **安全性**：潜在安全风险
- **规范性**：编码标准和最佳实践
- **可测试性**：单元测试友好程度

### 评审流程
1. 整体架构分析
2. 逐个方法详细审查
3. 依赖关系梳理
4. 潜在风险识别
5. 改进建议制定

### 输出格式
```markdown
# 代码评审报告

## 📋 基本信息
- 评审对象：[类名/模块名]
- 评审时间：[时间]
- 代码行数：[行数]

## 🎯 评审总结
[整体评价]

## 🟢 优点
- [优点1]
- [优点2]

## 🟡 改进建议
- [建议1]
- [建议2]

## 🔴 严重问题
- [问题1]
- [问题2]

## 📊 评分矩阵
| 维度 | 评分 | 说明 |
|------|------|------|
| 可读性 | X/10 | [说明] |
| 健壮性 | X/10 | [说明] |
| 效率性 | X/10 | [说明] |
| 安全性 | X/10 | [说明] |
| 规范性 | X/10 | [说明] |
| **综合** | **X/10** | [总评] |

## 🛠️ 具体修改建议
### 高优先级
1. [具体建议1]
2. [具体建议2]

### 中优先级
1. [具体建议1]
2. [具体建议2]

### 低优先级
1. [具体建议1]
2. [具体建议2]

## 🧪 测试建议
- [测试建议1]
- [测试建议2]

## 📚 参考资料
- [相关文档或最佳实践]
```
```

## 使用说明

1. **选择合适的提示词**：根据评审重点选择对应的提示词
2. **组合使用**：可以将多个提示词组合使用，进行全面评审
3. **定制化调整**：根据具体项目特点调整提示词内容
4. **持续优化**：根据评审效果不断优化提示词

## 最佳实践

1. **分层评审**：先整体后细节，先架构后实现
2. **重点突出**：根据代码重要性调整评审深度
3. **建设性反馈**：不仅指出问题，更要提供解决方案
4. **标准化输出**：使用统一的评审报告格式
