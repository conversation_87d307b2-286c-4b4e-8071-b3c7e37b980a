package com.ruijie.nse.mgr.py3server.launcher;


import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.config.Py3Constants;
import com.ruijie.nse.mgr.py3server.config.Py3ServerProperties;
import com.ruijie.nse.mgr.py3server.launcher.context.EnvironmentContext;
import com.ruijie.nse.mgr.py3server.launcher.context.Python3ServerContext;
import com.ruijie.nse.mgr.py3server.launcher.lifecycle.Python3ServerLifecycle;
import com.ruijie.nse.mgr.py3server.launcher.manager.ServerPortManager;
import com.ruijie.nse.mgr.py3server.service.SerClientsService;
import com.ruijie.nse.mgr.py3server.service.SerHostsService;
import com.ruijie.nse.mgr.repository.entity.SerClients;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import com.ruijie.nse.mgr.repository.pojo.bo.BasicAuthBo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.data.id.IdUtil;
import org.dromara.hutool.core.net.Ipv4Util;
import org.dromara.hutool.core.thread.ThreadUtil;
import org.dromara.hutool.core.util.RandomUtil;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.function.Supplier;


/**
 * python服务启动管理
 * <AUTHOR>
 * @date 2025-07-22
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class Python3ServerLauncher implements Python3ServerLifecycle {

    private final Python3ServerContext python3ServerContext;
    private final Py3ServerProperties py3ServerProperties;
    private final SerHostsService serHostsService;
    private final SerClientsService serClientsService;

    private final static String PYTHON_COMMAND = "python";
    private final static String PYTHON_MAIN_PY = "py3server/main.py";
    private static final int MIN_PORT = 20000;   // 最小端口
    private static final int MAX_PORT = 30000;   // 最大端口
    private static final ServerPortManager portManager = new ServerPortManager(MIN_PORT, MAX_PORT);


    @Override
    public boolean beforeLauncher() {
        String userId = SecurityUtils.getUserId();
        
        // 检查用户是否已有运行中的服务
        if (hasRunningServer(userId)) {
            log.warn("用户 {} 已有运行中的Python3服务", userId);
            return false;
        }
        
        log.info("|--------- [NSE SERVER] {} 启动前检查通过 ---------|", userId);
        return true;
    }

    @Override
    public SerHosts launcher0() throws Exception {
        String userId = SecurityUtils.getUserId();
        
        BasicAuthBo basicAuth = randomServerSecret.get();
        log.info("|--------- [NSE SERVER] {} 服务启动 ---------|", userId);

        int port = portManager.acquireAvailablePort();
        String hostId = IdUtil.getSeataSnowflakeNextIdStr();
        Process process = this.startPythonProcess(hostId, userId, port, basicAuth);

        // 创建主机信息
        SerHosts host = SerHosts.builder()
                .serverIp(Ipv4Util.LOCAL_IP)
                .serverPort(port)
                .basicAuth(basicAuth)
                .pid(process.pid())
                .maxMemory(EnvironmentContext.getMaxMemory())
                .maxDisk(EnvironmentContext.getMaxDisk())
                .cpuCore(EnvironmentContext.getCpuCores())
                .userId(userId)
                .storagePath(py3ServerProperties.getStoragePath())
                .build();
        host.setId(hostId);
        return host;
    }

    @Override
    public void afterLauncher(SerHosts host) {
        String userId = SecurityUtils.getUserId();
        
        // 存储到缓存
        python3ServerContext.setHost(userId, host);

        // 异步保存到数据库
        this.insertHosts(host);
    }


    @Override
    public boolean release(String userId) {
        try {
            SerHosts host = python3ServerContext.getHost(userId);
            if (host == null) {
                log.warn("用户 {} 没有运行中的Python3服务", userId);
                return false;
            }
            
            // 终止进程
            this.terminateProcess(host.getPid());
            
            // 释放端口
            portManager.releasePort(host.getServerPort());
            
            // 从缓存中移除
            python3ServerContext.releaseHost(userId);

            log.info("|--------- NSE SERVER {} 服务已释放，端口：{} ---------|", userId, host.getServerPort());
            return true;
        } catch (Exception e) {
            log.error("释放用户 {} 的Python3服务失败", userId, e);
            return false;
        }
    }

    @Override
    public SerHosts getServerInfo(String userId) {
        return python3ServerContext.getHost(userId);
    }

    @Override
    public boolean hasRunningServer(String userId) {
        SerHosts host = python3ServerContext.getHost(userId);
        return host != null && this.isProcessAlive(host.getPid());
    }


    /***
     * 启动进程
     * @param userId
     * @param port
     * @param basicAuth
     */
    private Process startPythonProcess(String hostId, String userId, int port, BasicAuthBo basicAuth) throws IOException, InterruptedException {
        ProcessBuilder processBuilder = new ProcessBuilder(
                PYTHON_COMMAND,
                "C:\\__ruijie_work_space\\nse\\gns3-server\\gns3server\\main.py"
//                getPythonRunPath.apply(PYTHON_MAIN_PY)
        );
        log.info("|--------- [NSE SERVER] {} 获取到可用端口：{} ---------|", userId, port);

        processBuilder.environment().put(Py3Constants.Server.PY3_SERVER_HOST_ID_KEY, hostId);
        processBuilder.environment().put(Py3Constants.Server.PY3_SERVER_USER_ID_KEY, userId);
        processBuilder.environment().put(Py3Constants.Server.PY3_SERVER_BASIC_AUTH_USER_KEY, basicAuth.getKey());
        processBuilder.environment().put(Py3Constants.Server.PY3_SERVER_BASIC_AUTH_SECRET_KEY, basicAuth.getSecret());
        processBuilder.environment().put(Py3Constants.Server.PY3_SERVER_STORAGE_PATH_KEY, py3ServerProperties.getStoragePath());
        processBuilder.environment().put(Py3Constants.Server.PY3_SERVER_PORT_KEY, String.valueOf(port));
        // 资源限制
        processBuilder.environment().put(Py3Constants.Server.LIMIT_MAX_MEMORY_KEY, String.valueOf(EnvironmentContext.getMaxMemory()));
        processBuilder.environment().put(Py3Constants.Server.LIMIT_CPU_CORES_KEY, String.valueOf(EnvironmentContext.getCpuCores()));

        // 强制设置python输出编码
        processBuilder.environment().put("PYTHONIOENCODING", StandardCharsets.UTF_8.name());
        processBuilder.redirectErrorStream(true);

        // 目标与当前进程的io相同
        processBuilder.inheritIO();
        Process process = processBuilder.start();
        AtomicInteger timeout = new AtomicInteger(0);
        while (!process.isAlive() && timeout.getAndIncrement() < 5) {
            ThreadUtil.sleep(500);
        }

        if(!process.isAlive()) {
            throw new RuntimeException("服务启动失败: 进程未启动");
        }

//        AtomicBoolean started = new AtomicBoolean(false);
//        CountDownLatch latch = new CountDownLatch(1);
//        long startTime = System.currentTimeMillis();
//        final long TIMEOUT = 30000; // 30秒超时
//
//        ThreadUtil.execAsync(() -> {
//            try (BufferedReader reader = new BufferedReader(
//                    new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8))) {
//
//                String line;
//                while ((line = reader.readLine()) != null) {
//                    log.info("[NSE SERVER] - python - {}", line);
//
//                    // 检测启动标识
//                    if (line.contains("__OK__")) {
//                        log.info("[NSE SERVER] - python - 启动成功！");
//                        started.set(true);
//                        latch.countDown();
//                        break;
//                    }
//                }
//            } catch (IOException e) {
//                log.error("读取输出失败", e);
//                latch.countDown();
//            }
//
//        });
//
//        try {
//            if (!latch.await(TIMEOUT, TimeUnit.MILLISECONDS)) {
//                log.warn("等待启动标识超时");
//            }
//        } catch (InterruptedException e) {
//            Thread.currentThread().interrupt();
//            log.error("等待被中断");
//        }
//
//
//        if (!started.get()) {
//            log.error("NSE SERVER服务启动 [ 失败 ] ！！！未在超时时间内检测到启动标识");
//
//            // 强制终止进程
//            if (process.isAlive()) {
//                process.destroyForcibly();
//            }
//            throw new RuntimeException("服务启动失败: 超时未检测到启动标识");
//        }
        this.logServerStartSuccess(userId, process.pid(), port);
        return process;
    }


    private void logServerStartSuccess(String userId, long pid, int port) {
        log.info("|--------- [NSE SERVER] 启动成功 ---------|");
        log.info("| 用户: {}", userId);
        log.info("| PID: {}", pid);
        log.info("| 端口: {}", port);
        log.info("| 最大内存: {} GB", EnvironmentContext.getMaxMemory());
        log.info("| CPU核心: {}", EnvironmentContext.getCpuCores());
        log.info("| 最大磁盘: {} GB", EnvironmentContext.getMaxDisk());
        log.info("|----------------------------------------|");
    }


    /**
     * 插入主机信息到数据库
     * @param host 主机信息
     */
    private void insertHosts(SerHosts host) {
        try {
            // 将该学生的其他host都清空
            serHostsService.removeByUserId(host.getUserId());

            serHostsService.save(host);

            SerClients client = SerClients.builder()
                    .hostId(host.getId())
                    .userId(host.getUserId())
                    .connectTime(LocalDateTime.now()).build();
            serClientsService.save(client);
            log.debug("主机信息已保存到数据库，hostId: {}", host.getId());
        } catch (Exception e) {
            log.error("保存主机信息到数据库失败，hostId: {}", host.getId(), e);
        }
    }


    /**
     * 终止指定PID的进程
     * @param pid 进程ID
     */
    private void terminateProcess(long pid) {
        try {
            // 获取父进程，如果有父进程，先销毁父进程
            ProcessHandle.of(pid).ifPresent(handle -> handle.parent().ifPresent(ProcessHandle::destroy));
            // 终止进程树
            ProcessHandle.of(pid).ifPresent(handle -> handle.descendants().forEach(ProcessHandle::destroy));
            ProcessHandle.of(pid).ifPresent(ProcessHandle::destroy);
            log.info("进程 {} 已终止", pid);
        } catch (Exception e) {
            log.error("终止进程 {} 失败", pid, e);
        }
    }

    /**
     * 检查进程是否存活
     * @param pid 进程ID
     * @return 是否存活
     */
    private boolean isProcessAlive(long pid) {
        return ProcessHandle.of(pid).map(ProcessHandle::isAlive).orElse(false);
    }

    /**
     * 获取可执行文件路径
     */
    private final Function<String, String> getPythonRunPath = (mainFile) -> {
        try {
            return new File(
                    Objects.requireNonNull(getClass().getClassLoader().getResource(mainFile)).toURI()
            ).getAbsolutePath();
        } catch (URISyntaxException e) {
            throw new RuntimeException("获取python脚本路径失败", e);
        }
    };


    private final Supplier<BasicAuthBo> randomServerSecret = () -> new BasicAuthBo(
            RandomUtil.randomString(16),
            RandomUtil.randomString(64)
    );

}
