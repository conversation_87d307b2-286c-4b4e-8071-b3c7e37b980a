package com.ruijie.nse.mgr.py3server.utils;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URISyntaxException;
import java.net.URL;
import java.nio.file.*;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.Enumeration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * Python资源管理器
 * 负责管理Python脚本资源的复制和缓存
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class PythonResourceManager {

    private static final String TEMP_DIR_PREFIX = "nse-python-";
    private static final int BUFFER_SIZE = 8192;
    
    // 资源缓存：避免重复复制相同的资源
    private final ConcurrentHashMap<String, Path> resourceCache = new ConcurrentHashMap<>();
    private final ReentrantLock cacheLock = new ReentrantLock();

    /**
     * 获取Python脚本的可执行路径
     * 支持缓存机制，避免重复复制
     *
     * @param resourcePath 资源路径（如：py3server）
     * @param mainScript 主脚本文件（如：main.py）
     * @return 可执行的脚本路径
     */
    public String getPythonScriptPath(String resourcePath, String mainScript) {
        String cacheKey = resourcePath + ":" + mainScript;
        
        // 检查缓存
        Path cachedPath = resourceCache.get(cacheKey);
        if (cachedPath != null && Files.exists(cachedPath)) {
            log.debug("使用缓存的Python脚本路径: {}", cachedPath);
            return cachedPath.toString();
        }
        
        cacheLock.lock();
        try {
            // 双重检查
            cachedPath = resourceCache.get(cacheKey);
            if (cachedPath != null && Files.exists(cachedPath)) {
                return cachedPath.toString();
            }
            
            // 创建新的临时目录并复制资源
            Path tempDir = createTempDirectory();
            copyPythonResources(resourcePath, tempDir);
            
            // 计算主脚本路径
            Path scriptPath = tempDir.resolve(mainScript);
            if (!Files.exists(scriptPath)) {
                throw new RuntimeException("主脚本文件不存在: " + scriptPath);
            }
            
            // 缓存结果
            resourceCache.put(cacheKey, scriptPath);
            
            log.info("Python脚本路径已准备: {}", scriptPath);
            return scriptPath.toString();
            
        } catch (Exception e) {
            log.error("获取Python脚本路径失败: resourcePath={}, mainScript={}", resourcePath, mainScript, e);
            throw new RuntimeException("获取Python脚本路径失败", e);
        } finally {
            cacheLock.unlock();
        }
    }

    /**
     * 创建临时目录
     */
    private Path createTempDirectory() throws IOException {
        Path tempDir = Files.createTempDirectory(TEMP_DIR_PREFIX);
        
        // 注册关闭钩子，确保临时目录在JVM退出时被清理
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            try {
                deleteDirectoryRecursively(tempDir);
                log.debug("临时目录已清理: {}", tempDir);
            } catch (Exception e) {
                log.warn("清理临时目录失败: {}", tempDir, e);
            }
        }));
        
        log.debug("创建临时目录: {}", tempDir);
        return tempDir;
    }

    /**
     * 复制Python资源文件到目标目录
     *
     * @param resourcePath 资源路径
     * @param targetDir 目标目录
     */
    public void copyPythonResources(String resourcePath, Path targetDir) throws IOException {
        log.debug("开始复制Python资源: {} -> {}", resourcePath, targetDir);
        
        URL resourceUrl = getClass().getClassLoader().getResource(resourcePath);
        if (resourceUrl == null) {
            throw new IOException("找不到资源路径: " + resourcePath);
        }
        
        String protocol = resourceUrl.getProtocol();
        log.debug("资源协议: {}, URL: {}", protocol, resourceUrl);
        
        switch (protocol) {
            case "file" -> copyFromFileSystem(resourceUrl, targetDir);
            case "jar" -> copyFromJar(resourceUrl, resourcePath, targetDir);
            default -> throw new IOException("不支持的资源协议: " + protocol);
        }
        
        log.info("Python资源复制完成: {} -> {}", resourcePath, targetDir);
    }

    /**
     * 从文件系统复制资源（开发环境）
     */
    private void copyFromFileSystem(URL resourceUrl, Path targetDir) throws IOException {
        try {
            Path sourcePath = Paths.get(resourceUrl.toURI());
            log.debug("从文件系统复制: {} -> {}", sourcePath, targetDir);
            
            Files.walkFileTree(sourcePath, new SimpleFileVisitor<Path>() {
                @Override
                public FileVisitResult preVisitDirectory(Path dir, BasicFileAttributes attrs) throws IOException {
                    Path targetPath = targetDir.resolve(sourcePath.relativize(dir));
                    Files.createDirectories(targetPath);
                    return FileVisitResult.CONTINUE;
                }

                @Override
                public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                    Path targetPath = targetDir.resolve(sourcePath.relativize(file));
                    Files.copy(file, targetPath, StandardCopyOption.REPLACE_EXISTING);
                    
                    // 保持可执行权限（对于.py文件）
                    if (file.toString().endsWith(".py")) {
                        makeExecutable(targetPath);
                    }
                    
                    log.trace("复制文件: {} -> {}", file, targetPath);
                    return FileVisitResult.CONTINUE;
                }
            });
            
        } catch (URISyntaxException e) {
            throw new IOException("无效的资源URI", e);
        }
    }

    /**
     * 从JAR包复制资源（生产环境）
     */
    private void copyFromJar(URL resourceUrl, String resourcePath, Path targetDir) throws IOException {
        String jarPath = extractJarPath(resourceUrl);
        log.debug("从JAR包复制: {} (资源路径: {})", jarPath, resourcePath);
        
        try (JarFile jarFile = new JarFile(jarPath)) {
            Enumeration<JarEntry> entries = jarFile.entries();
            
            while (entries.hasMoreElements()) {
                JarEntry entry = entries.nextElement();
                String entryName = entry.getName();
                
                // 只处理指定资源路径下的文件
                if (!entryName.startsWith(resourcePath + "/") && !entryName.equals(resourcePath)) {
                    continue;
                }
                
                // 计算相对路径
                String relativePath = entryName.equals(resourcePath) ? "" : 
                    entryName.substring(resourcePath.length() + 1);
                Path targetPath = targetDir.resolve(relativePath);
                
                if (entry.isDirectory()) {
                    Files.createDirectories(targetPath);
                    log.trace("创建目录: {}", targetPath);
                } else {
                    copyJarEntry(jarFile, entry, targetPath);
                    log.trace("复制文件: {} -> {}", entryName, targetPath);
                }
            }
        }
    }

    /**
     * 复制JAR条目到目标路径
     */
    private void copyJarEntry(JarFile jarFile, JarEntry entry, Path targetPath) throws IOException {
        Files.createDirectories(targetPath.getParent());
        
        try (InputStream inputStream = jarFile.getInputStream(entry);
             OutputStream outputStream = Files.newOutputStream(targetPath)) {
            
            byte[] buffer = new byte[BUFFER_SIZE];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
        }
        
        // 保持可执行权限（对于.py文件）
        if (entry.getName().endsWith(".py")) {
            makeExecutable(targetPath);
        }
    }

    /**
     * 提取JAR文件路径
     */
    private String extractJarPath(URL resourceUrl) {
        String jarPath = resourceUrl.getPath();
        
        // 去掉 "file:" 前缀
        if (jarPath.startsWith("file:")) {
            jarPath = jarPath.substring(5);
        }
        
        // 去掉 "!/" 后缀
        int exclamationIndex = jarPath.indexOf("!");
        if (exclamationIndex != -1) {
            jarPath = jarPath.substring(0, exclamationIndex);
        }
        
        return jarPath;
    }

    /**
     * 设置文件为可执行
     */
    private void makeExecutable(Path filePath) {
        try {
            if (Files.exists(filePath) && !Files.isExecutable(filePath)) {
                filePath.toFile().setExecutable(true);
            }
        } catch (Exception e) {
            log.warn("设置文件可执行权限失败: {}", filePath, e);
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectoryRecursively(Path directory) throws IOException {
        if (!Files.exists(directory)) {
            return;
        }
        
        Files.walkFileTree(directory, new SimpleFileVisitor<Path>() {
            @Override
            public FileVisitResult visitFile(Path file, BasicFileAttributes attrs) throws IOException {
                Files.deleteIfExists(file);
                return FileVisitResult.CONTINUE;
            }

            @Override
            public FileVisitResult postVisitDirectory(Path dir, IOException exc) throws IOException {
                Files.deleteIfExists(dir);
                return FileVisitResult.CONTINUE;
            }
        });
    }

    /**
     * 清理资源缓存
     */
    public void clearCache() {
        cacheLock.lock();
        try {
            resourceCache.clear();
            log.info("Python资源缓存已清理");
        } finally {
            cacheLock.unlock();
        }
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format("缓存条目数: %d", resourceCache.size());
    }
}
