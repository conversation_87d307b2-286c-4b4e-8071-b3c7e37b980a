package com.ruijie.nse.mgr.py3server.feign.adaptor;

import com.ruijie.nse.mgr.py3server.dto.ProjectDto;

import java.nio.file.Path;

public interface Python3FeignForwardAdaptor {


    /**
     * 创建项目
     * @param projectId
     * @param projectName
     * @return
     */
    ProjectDto createProject(String projectId, String projectName);

    /**
     * gns3项目默认会自动打开，这里需要手动关闭
     * @return
     */
    boolean closeProject(String projectId);

    /***
     * 导入项目
     * @param projectId
     * @param projectName
     * @param nseProjectPath
     * @return
     */
    ProjectDto importProject(String projectId, String projectName, Path nseProjectPath);

    /**
     * 删除项目
     * @param projectId
     * @return
     */
    boolean deleteProject(String projectId);


    /**
     * 打开项目
     * @param projectId
     * @return
     */
    boolean openProject(String projectId);

    /**
     * 复制项目
     * @param projectId
     * @param projectName  新的项目名称
     * @return
     */
    ProjectDto duplicateProject(String projectId, String projectName);


    /**
     * 导出项目
     * @param projectId
     * @return
     */
    ProjectDto exportProject(String projectId);



    interface OpenApi {
        String PROJECT_URI = "/v2/projects";
        String PROJECT_URI_WITH_PLACE = "/v2/projects/%s";
        String CLOSE_PROJECT_URI_WITH_PLACE = "/v2/projects/%s/close";
        String IMPORT_PROJECT_URI_WITH_PLACE = "/v2/projects/%s/import";
        String OPEN_PROJECT_URI_WITH_PLACE = "/v2/projects/%s/open";
        String DUPLICATE_PROJECT_URI_WITH_PLACE = "/v2/projects/%s/duplicate";

        // 临时下载到本地的接口
        String EXPORT_PROJECT_URI_WITH_PLACE = "/v2/projects/%s/export/local";
    }

}
