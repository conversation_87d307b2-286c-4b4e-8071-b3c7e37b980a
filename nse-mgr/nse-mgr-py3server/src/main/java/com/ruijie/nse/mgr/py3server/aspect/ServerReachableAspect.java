package com.ruijie.nse.mgr.py3server.aspect;

import com.ruijie.nse.common.exception.InvalidAccessException;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.annotation.ValidateReachable;
import com.ruijie.nse.mgr.py3server.launcher.lifecycle.Python3ServerLifecycle;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;

/**
 * 服务可达性校验切面
 * 用于处理@ValidateReachable注解的逻辑
 * 
 * <AUTHOR>
 * @date 2025-01-22
 */
@Slf4j
@Aspect
@Component
@RequiredArgsConstructor
public class ServerReachableAspect {

    private final Python3ServerLifecycle python3ServerLifecycle;
    
    /**
     * 环绕通知：在方法执行前校验服务可达性
     * 
     * @param joinPoint
     * @param ValidateReachable
     * @return
     * @throws Throwable
     */
    @Around("@annotation(ValidateReachable)")
    public Object validateServerReachability(ProceedingJoinPoint joinPoint, ValidateReachable ValidateReachable) throws Throwable {
        String userId = SecurityUtils.getUserId();

        try {
            // 服务不是必须的，直接跳走。但是不是必须的，用这个注解显得多余，给个警告
            if (!ValidateReachable.required()) {
                log.warn("用户 {} 的 NSE 服务不可达，但注解配置为非必需，继续执行方法", userId);
                return joinPoint.proceed();
            }

            boolean hasRunningServer = python3ServerLifecycle.hasRunningServer(userId);

            // 已启动的，直接返回
            if (hasRunningServer) {
                SerHosts serverInfo = python3ServerLifecycle.getServerInfo(userId);
                if (serverInfo != null) {
                    log.info("用户 {} 的 NSE 服务可达，服务地址: {}:{}", userId, serverInfo.getServerIp(), serverInfo.getServerPort());
                } else {
                    log.warn("用户 {} 的 NSE 服务状态异常：hasRunningServer返回true但getServerInfo返回null", userId);
                }
                return joinPoint.proceed();
            }

            // 无需自动启动，直接报错
            if (!ValidateReachable.autoStart()) {
                log.warn("用户 {} 的 NSE 服务不可达，且未配置自动启动", userId);
                throw new InvalidAccessException(503, ValidateReachable.message());
            }
            
            // 尝试自动启动服务
            log.info("用户 {} 的 NSE 服务不可达，开始尝试自动启动服务", userId);
            try {
                python3ServerLifecycle.launcher();
                log.info("用户 {} 的 NSE 服务自动启动成功", userId);
                return joinPoint.proceed();
            } catch (Exception e) {
                log.error("用户 {} 的 NSE 服务自动启动失败", userId, e);
                throw new InvalidAccessException(HttpStatus.INTERNAL_SERVER_ERROR.value(), " NSE 服务自动启动失败: " + e.getMessage());
            }
            
        } catch (Exception e) {
            log.error("校验用户 {} 的 NSE 服务可达性时发生异常", userId, e);
            throw new InvalidAccessException(500, e.getMessage());
        }
    }
}