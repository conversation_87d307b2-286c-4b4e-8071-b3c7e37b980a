package com.ruijie.nse.mgr.py3server.utils;

import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.py3server.config.Py3Constants;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import com.ruijie.nse.mgr.repository.pojo.bo.BasicAuthBo;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.security.Keys;
import lombok.RequiredArgsConstructor;
import org.dromara.hutool.core.date.DateUtil;
import org.dromara.hutool.core.text.StrPool;
import org.dromara.hutool.http.HttpUtil;
import org.dromara.hutool.http.client.Request;
import org.dromara.hutool.http.client.Response;
import org.dromara.hutool.http.client.body.MultipartBody;
import org.dromara.hutool.http.meta.Method;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@RequiredArgsConstructor
public class NseServerRestUtil {

    public void header() {

    }

    public Response get(SerHosts host, String url) {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createGet(baseUrl + url)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_JSON_VALUE);
        return request.send();
    }

    public Response post(SerHosts host, String url, Map<String, Object> body) {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createPost(baseUrl + url)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_JSON_VALUE);
        if(body != null && !body.isEmpty()) {
            request.body(JSONUtil.toJsonStr(body));
        }
        return request.send();
    }

    public Response form(SerHosts host, String url, Map<String, Object> body) {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        return HttpUtil.createPost(baseUrl + url)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_FORM_URLENCODED_VALUE)
                .form(body)
                .send();
    }

    public Response multipartData(SerHosts host, String url, MultipartBody multipartBody) {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);

        try {
            return HttpUtil.createPost(baseUrl + url)
                    .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                    .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                    .contentType(MediaType.MULTIPART_FORM_DATA_VALUE)
                    .body(multipartBody)
                    .send();
        } catch (Exception e) {
            throw new RuntimeException("Failed to call Python API: " + e.getMessage(), e);
        }
    }

    public Response delete(SerHosts host, String url, Map<String, String> body) {
        BasicAuthBo basicAuth = host.getBasicAuth();
        String baseUrl = getDomain(host);
        Request request = HttpUtil.createRequest(baseUrl + url, Method.DELETE)
                .basicAuth(basicAuth.getKey(), basicAuth.getSecret())
                .header(Py3Constants.Server.PY3_HTTP_HEADER_TOKEN, buildBearerToken())
                .contentType(MediaType.APPLICATION_JSON_VALUE);
        if(body != null && !body.isEmpty()) {
            request.body(JSONUtil.toJsonStr(body));
        }

        return request.send();
    }


    private String buildBearerToken() {
        return "Bearer " + buildToken();
    }

    private String buildToken() {
        String userId = SecurityUtils.getUserId();
        return Jwts.builder()
                .subject(userId)
                .claim("user_id", userId)
                .claim("user_type", SecurityUtils.getUserType())
                .claim(CommonConstant.Jwt.CLAIM_USERNAME, SecurityUtils.getUserName())
                .issuedAt(DateUtil.now())
                .signWith(Keys.hmacShaKeyFor(CommonConstant.Jwt.SECRET.getBytes()))
                .compact();
    }

    private String getDomain(SerHosts host) {
        return "http://" + host.getServerIp() + StrPool.COLON + host.getServerPort();
    }
}
