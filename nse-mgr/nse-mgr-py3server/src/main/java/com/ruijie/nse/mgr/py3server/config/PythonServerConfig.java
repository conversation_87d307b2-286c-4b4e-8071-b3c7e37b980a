package com.ruijie.nse.mgr.py3server.config;

import com.ruijie.nse.mgr.py3server.utils.PythonResourceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Python服务器配置类
 * 负责Python服务器相关的配置和初始化
 *
 * <AUTHOR> Team
 */
@Slf4j
@Configuration
public class PythonServerConfig {

    /**
     * Python资源管理器Bean
     */
    @Bean
    public PythonResourceManager pythonResourceManager() {
        return new PythonResourceManager();
    }

    /**
     * 应用启动完成后的初始化操作
     */
    @EventListener(ApplicationReadyEvent.class)
    public void onApplicationReady() {
        log.info("Python服务器配置初始化开始...");
        
        try {
            // 验证Python脚本资源是否存在
            validatePythonResources();
            
            // 预热资源管理器（可选）
            // warmupResourceManager();
            
            log.info("Python服务器配置初始化完成");
            
        } catch (Exception e) {
            log.error("Python服务器配置初始化失败", e);
            throw new RuntimeException("Python服务器配置初始化失败", e);
        }
    }

    /**
     * 验证Python脚本资源是否存在
     */
    private void validatePythonResources() {
        log.debug("验证Python脚本资源...");
        
        // 检查主要的Python脚本文件
        String[] requiredFiles = {
            "py3server/main.py",
            "py3server/__init__.py",
            "py3server/config.py"
        };
        
        for (String file : requiredFiles) {
            ClassPathResource resource = new ClassPathResource(file);
            if (!resource.exists()) {
                throw new RuntimeException("必需的Python脚本文件不存在: " + file);
            }
            log.debug("Python脚本文件验证通过: {}", file);
        }
        
        log.info("Python脚本资源验证完成");
    }

    /**
     * 预热资源管理器（可选）
     * 在应用启动时预先复制Python资源，提高首次启动速度
     */
    private void warmupResourceManager() {
        log.debug("预热Python资源管理器...");
        
        try {
            PythonResourceManager resourceManager = pythonResourceManager();
            
            // 预先准备Python脚本路径
            String scriptPath = resourceManager.getPythonScriptPath("py3server", "main.py");
            log.info("Python脚本预热完成: {}", scriptPath);
            
        } catch (Exception e) {
            log.warn("Python资源管理器预热失败，将在首次使用时初始化", e);
        }
    }

    /**
     * 检查Python环境
     */
    public static boolean checkPythonEnvironment() {
        try {
            ProcessBuilder pb = new ProcessBuilder("python3", "--version");
            Process process = pb.start();
            int exitCode = process.waitFor();
            
            if (exitCode == 0) {
                log.info("Python3环境检查通过");
                return true;
            } else {
                log.warn("Python3环境检查失败，退出码: {}", exitCode);
                return false;
            }
            
        } catch (Exception e) {
            log.error("Python3环境检查异常", e);
            return false;
        }
    }

    /**
     * 获取Python命令
     * 根据系统环境返回合适的Python命令
     */
    public static String getPythonCommand() {
        // 优先使用python3
        if (isCommandAvailable("python3")) {
            return "python3";
        }
        
        // 回退到python
        if (isCommandAvailable("python")) {
            return "python";
        }
        
        throw new RuntimeException("系统中未找到Python解释器");
    }

    /**
     * 检查命令是否可用
     */
    private static boolean isCommandAvailable(String command) {
        try {
            ProcessBuilder pb = new ProcessBuilder(command, "--version");
            Process process = pb.start();
            int exitCode = process.waitFor();
            return exitCode == 0;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 创建用户工作目录
     */
    public static void createUserWorkspace(String userId, String storagePath) {
        try {
            Path userWorkspace = Paths.get(storagePath, "workspace", userId);
            Files.createDirectories(userWorkspace);
            
            Path userProjects = Paths.get(storagePath, "projects", userId);
            Files.createDirectories(userProjects);
            
            Path userTemp = Paths.get(storagePath, "temp", userId);
            Files.createDirectories(userTemp);
            
            log.debug("用户工作目录创建完成: userId={}, workspace={}", userId, userWorkspace);
            
        } catch (IOException e) {
            log.error("创建用户工作目录失败: userId={}, storagePath={}", userId, storagePath, e);
            throw new RuntimeException("创建用户工作目录失败", e);
        }
    }

    /**
     * 清理用户工作目录
     */
    public static void cleanupUserWorkspace(String userId, String storagePath) {
        try {
            Path userWorkspace = Paths.get(storagePath, "workspace", userId);
            if (Files.exists(userWorkspace)) {
                // 这里可以实现选择性清理逻辑
                log.debug("用户工作目录清理: userId={}, workspace={}", userId, userWorkspace);
            }
            
        } catch (Exception e) {
            log.error("清理用户工作目录失败: userId={}, storagePath={}", userId, storagePath, e);
        }
    }
}
