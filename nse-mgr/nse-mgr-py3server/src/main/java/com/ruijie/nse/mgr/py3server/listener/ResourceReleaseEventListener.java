package com.ruijie.nse.mgr.py3server.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruijie.nse.mgr.common.dto.EventMessage;
import com.ruijie.nse.mgr.common.dto.ResourceReleaseEventData;
import com.ruijie.nse.mgr.py3server.launcher.Python3ServerLauncher;
import com.ruijie.nse.mgr.py3server.service.ProjectCleanupService;
import com.ruijie.nse.mgr.py3server.service.StudentResourceService;
import com.ruijie.nse.mgr.repository.entity.SerHosts;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.messaging.Message;
import org.springframework.messaging.handler.annotation.Header;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.concurrent.CompletableFuture;

/**
 * 资源释放事件监听器
 * 专门处理Python3服务器和学生资源释放相关的事件
 *
 * <AUTHOR> Team
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ResourceReleaseEventListener {

    private final Python3ServerLauncher python3ServerLauncher;
    private final StudentResourceService studentResourceService;
    private final ProjectCleanupService projectCleanupService;
    private final ObjectMapper objectMapper;

    /**
     * 监听资源释放相关事件
     * 通过Spring Integration的ServiceActivator注解监听特定通道的消息
     */
    @ServiceActivator(inputChannel = "resourceReleaseChannel")
    public void handleResourceReleaseEvent(@Payload EventMessage eventMessage,
                                         @Header("eventType") String eventType) {
        try {
            log.info("接收到资源释放事件: type={}, message={}", eventType, eventMessage.getEventMessage());
            
            EventTypeEnum eventTypeEnum = EventTypeEnum.valueOf(eventType);
            
            switch (eventTypeEnum) {
                case EVT_STUDENT_RESOURCE_RELEASE -> handleStudentResourceRelease(eventMessage);
                case EVT_STUDENT_PROJECT_RELEASE -> handleStudentProjectRelease(eventMessage);
                case EVT_PY3_SERVER_RELEASE -> handlePy3ServerRelease(eventMessage);
                case EVT_USER_LOGOUT, EVT_USER_SESSION_EXPIRED, EVT_USER_FORCE_LOGOUT -> 
                    handleUserSessionEnd(eventMessage);
                default -> log.debug("未处理的事件类型: {}", eventType);
            }
            
        } catch (Exception e) {
            log.error("处理资源释放事件失败: eventType={}, error={}", eventType, e.getMessage(), e);
        }
    }

    /**
     * 处理学生资源释放事件
     */
    private void handleStudentResourceRelease(EventMessage eventMessage) {
        try {
            ResourceReleaseEventData eventData = parseEventData(eventMessage);
            if (eventData == null || !StringUtils.hasText(eventData.getUserId())) {
                log.warn("学生资源释放事件数据无效: {}", eventMessage.getEventDetails());
                return;
            }

            String userId = eventData.getUserId();
            log.info("开始处理学生资源释放: userId={}, reason={}", userId, eventData.getReleaseReason());

            // 异步处理资源释放，避免阻塞事件处理
            CompletableFuture.runAsync(() -> {
                try {
                    // 1. 释放Python3服务器资源
                    releasePython3ServerResources(userId, eventData);
                    
                    // 2. 清理学生相关资源
                    studentResourceService.cleanupStudentResources(userId, eventData);
                    
                    // 3. 清理项目资源
                    if (eventData.getProjects() != null && !eventData.getProjects().isEmpty()) {
                        projectCleanupService.cleanupProjects(userId, eventData.getProjects());
                    }
                    
                    log.info("学生资源释放完成: userId={}", userId);
                    
                } catch (Exception e) {
                    log.error("异步处理学生资源释放失败: userId={}, error={}", userId, e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            log.error("处理学生资源释放事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理学生项目释放事件
     */
    private void handleStudentProjectRelease(EventMessage eventMessage) {
        try {
            ResourceReleaseEventData eventData = parseEventData(eventMessage);
            if (eventData == null || !StringUtils.hasText(eventData.getUserId())) {
                log.warn("学生项目释放事件数据无效: {}", eventMessage.getEventDetails());
                return;
            }

            String userId = eventData.getUserId();
            log.info("开始处理学生项目释放: userId={}, projectCount={}", 
                    userId, eventData.getProjects() != null ? eventData.getProjects().size() : 0);

            // 异步处理项目清理
            CompletableFuture.runAsync(() -> {
                try {
                    if (eventData.getProjects() != null && !eventData.getProjects().isEmpty()) {
                        projectCleanupService.cleanupProjects(userId, eventData.getProjects());
                        log.info("学生项目释放完成: userId={}, projectCount={}", 
                                userId, eventData.getProjects().size());
                    }
                } catch (Exception e) {
                    log.error("异步处理学生项目释放失败: userId={}, error={}", userId, e.getMessage(), e);
                }
            });

        } catch (Exception e) {
            log.error("处理学生项目释放事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理Python3服务器释放事件
     */
    private void handlePy3ServerRelease(EventMessage eventMessage) {
        try {
            ResourceReleaseEventData eventData = parseEventData(eventMessage);
            if (eventData == null || !StringUtils.hasText(eventData.getUserId())) {
                log.warn("Python3服务器释放事件数据无效: {}", eventMessage.getEventDetails());
                return;
            }

            String userId = eventData.getUserId();
            log.info("开始处理Python3服务器释放: userId={}", userId);

            // 直接释放Python3服务器资源
            releasePython3ServerResources(userId, eventData);

        } catch (Exception e) {
            log.error("处理Python3服务器释放事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理用户会话结束事件
     */
    private void handleUserSessionEnd(EventMessage eventMessage) {
        try {
            ResourceReleaseEventData eventData = parseEventData(eventMessage);
            if (eventData == null || !StringUtils.hasText(eventData.getUserId())) {
                log.warn("用户会话结束事件数据无效: {}", eventMessage.getEventDetails());
                return;
            }

            String userId = eventData.getUserId();
            log.info("处理用户会话结束: userId={}, eventType={}", userId, eventMessage.getEventType());

            // 检查用户是否有运行中的Python3服务器
            SerHosts serverInfo = python3ServerLauncher.getServerInfo(userId);
            if (serverInfo != null) {
                log.info("用户会话结束，释放Python3服务器资源: userId={}", userId);
                
                // 创建资源释放事件数据
                ResourceReleaseEventData releaseData = ResourceReleaseEventData.createStudentReleaseEvent(
                        userId, eventData.getUsername(), "用户会话结束", "AUTO");
                
                // 填充服务器信息
                releaseData.setServerInfo(ResourceReleaseEventData.Python3ServerInfo.builder()
                        .hostId(serverInfo.getId())
                        .serverIp(serverInfo.getServerIp())
                        .serverPort(serverInfo.getServerPort())
                        .pid(serverInfo.getPid())
                        .storagePath(serverInfo.getStoragePath())
                        .maxMemory(serverInfo.getMaxMemory())
                        .currentMemory(serverInfo.getCurrentMemory())
                        .cpuCore(serverInfo.getCpuCore())
                        .build());
                
                // 异步释放资源
                CompletableFuture.runAsync(() -> {
                    try {
                        releasePython3ServerResources(userId, releaseData);
                        studentResourceService.cleanupStudentResources(userId, releaseData);
                        log.info("用户会话结束资源清理完成: userId={}", userId);
                    } catch (Exception e) {
                        log.error("用户会话结束资源清理失败: userId={}, error={}", userId, e.getMessage(), e);
                    }
                });
            }

        } catch (Exception e) {
            log.error("处理用户会话结束事件失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 释放Python3服务器资源
     */
    private void releasePython3ServerResources(String userId, ResourceReleaseEventData eventData) {
        try {
            boolean released = python3ServerLauncher.release(userId);
            if (released) {
                log.info("Python3服务器资源释放成功: userId={}, reason={}", 
                        userId, eventData.getReleaseReason());
            } else {
                log.warn("Python3服务器资源释放失败或无需释放: userId={}", userId);
            }
        } catch (Exception e) {
            log.error("释放Python3服务器资源异常: userId={}, error={}", userId, e.getMessage(), e);
        }
    }

    /**
     * 解析事件数据
     */
    private ResourceReleaseEventData parseEventData(EventMessage eventMessage) {
        try {
            if (StringUtils.hasText(eventMessage.getPayload())) {
                return objectMapper.readValue(eventMessage.getPayload(), ResourceReleaseEventData.class);
            } else if (StringUtils.hasText(eventMessage.getEventDetails())) {
                return objectMapper.readValue(eventMessage.getEventDetails(), ResourceReleaseEventData.class);
            }
        } catch (JsonProcessingException e) {
            log.warn("解析事件数据失败: {}", e.getMessage());
        }
        
        // 如果解析失败，创建基本的事件数据
        return ResourceReleaseEventData.builder()
                .releaseTime(LocalDateTime.now())
                .build();
    }
}
