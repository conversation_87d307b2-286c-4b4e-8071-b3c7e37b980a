package com.ruijie.nse.mgr.py3server.config;

public interface Py3Constants {

    interface Project {
        String PROJECT_EXTENSION = ".nse";
        String PROJECT_SIGN_URL = "http://%s:%d/static/web-ui/server/%s/project/%s";
    }

    interface Server {
        String PY3_SERVER_HOST_ID_KEY = "PY3SERVER_HOST_ID";
        String PY3_SERVER_USER_ID_KEY = "PY3SERVER_USER_ID";
        String PY3_SERVER_PORT_KEY = "PY3SERVER_PORT";
        String PY3SERVER_HOST_KEY = "PY3SERVER_HOST";
        String PY3_SERVER_STORAGE_PATH_KEY = "PY3SERVER_PATH";
        String PY3_SERVER_BASIC_AUTH_USER_KEY = "PY3_SERVER_BASIC_AUTH_USER";
        String PY3_SERVER_BASIC_AUTH_SECRET_KEY = "PY3_SERVER_BASIC_AUTH_SECRET";

        String LIMIT_MAX_MEMORY_KEY = "LIMIT_MAX_MEMORY";
        String LIMIT_CPU_CORES_KEY = "LIMIT_CPU_CORES";

        String PY3_HTTP_HEADER_TOKEN = "X-Auth-Nse-Token";
    }
}
