{"appliance_id": "b2027465-2959-4ef3-ba56-7b2880e4e711", "name": "Firefox", "category": "guest", "description": "A light Linux based on TinyCore Linux with Firefox preinstalled", "vendor_name": "Mozilla Foundation", "vendor_url": "http://www.mozilla.org", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/Firefox.png", "documentation_url": "https://support.mozilla.org", "product_name": "Firefox", "product_url": "https://www.mozilla.org/firefox", "registry_version": 4, "status": "stable", "maintainer": "GNS3 team", "maintainer_email": "<EMAIL>", "symbol": "firefox.svg", "qemu": {"adapter_type": "e1000", "adapters": 1, "ram": 256, "hda_disk_interface": "ide", "arch": "i386", "console_type": "vnc", "kvm": "allow", "options": "-vga std -usbdevice tablet"}, "images": [{"filename": "linux-tinycore-linux-6.4-firefox-33.1.1-2.img", "version": "31.1.1~2", "md5sum": "8db0d8dc890797cc335ceb8aaf2255f0", "filesize": 93257728, "download_url": "https://sourceforge.net/projects/gns-3/files/Qemu%20Appliances/", "direct_download_url": "http://downloads.sourceforge.net/project/gns-3/Qemu%20Appliances/linux-tinycore-linux-6.4-firefox-33.1.1-2.img"}, {"filename": "linux-tinycore-linux-6.4-firefox-33.1.1.img", "version": "31.1.1~1", "md5sum": "9e51ad24dc25c4a26f7a8fb99bc77830", "filesize": 82313216, "download_url": "https://sourceforge.net/projects/gns-3/files/Qemu%20Appliances/", "direct_download_url": "http://downloads.sourceforge.net/project/gns-3/Qemu%20Appliances/linux-tinycore-linux-6.4-firefox-33.1.1.img"}], "versions": [{"name": "31.1.1~2", "images": {"hda_disk_image": "linux-tinycore-linux-6.4-firefox-33.1.1-2.img"}}, {"name": "31.1.1~1", "images": {"hda_disk_image": "linux-tinycore-linux-6.4-firefox-33.1.1.img"}}]}