{"appliance_id": "6fe715ad-4c27-4f65-97f7-f8c1d7cd3e0a", "name": "OpenMediaVault", "category": "guest", "description": "openmediavault is the next generation network attached storage (NAS) solution based on Debian Linux. It contains services like SSH, (S)FTP, SMB/CIFS, DAAP media server, RSync, BitTorrent client and many more.", "vendor_name": "Volker Theile", "vendor_url": "https://www.openmediavault.org/", "documentation_url": "hhttps://docs.openmediavault.org", "product_name": "OpenMediaVault", "product_url": "https://www.openmediavault.org/", "registry_version": 4, "status": "stable", "maintainer": "<PERSON><PERSON>", "maintainer_email": "<EMAIL>", "usage": "Install OS to first Disk, poweroff, eject iso.\nAdd empty30G.qcow2 to Secondary master and slave this way you will get 3 hard disks for storage.\nDefault WUI credentials are admin:openmediavault.", "port_name_format": "eth{0}", "qemu": {"adapter_type": "e1000", "adapters": 1, "ram": 2048, "hda_disk_interface": "sata", "hdb_disk_interface": "sata", "arch": "x86_64", "console_type": "vnc", "boot_priority": "dc", "kvm": "require"}, "images": [{"filename": "openmediavault_7.0.32-amd64.iso", "version": "7.0.32", "md5sum": "36d1fda7de0c8dd6806a65145845d362", "filesize": 981467136, "download_url": "https://www.openmediavault.org/download.html", "direct_download_url": "https://sourceforge.net/projects/openmediavault/files/iso/7.0-32/openmediavault_7.0-32-amd64.iso"}, {"filename": "openmediavault_6.5.0-amd64.iso", "version": "6.5.0", "md5sum": "aa40e5ca50748b139cba2f4ac704a72d", "filesize": 941621248, "download_url": "https://www.openmediavault.org/download.html", "direct_download_url": "https://sourceforge.net/projects/openmediavault/files/6.5.0/openmediavault_6.5.0-amd64.iso"}, {"filename": "openmediavault_6.0.24-amd64.iso", "version": "6.0.24", "md5sum": "6f71b9470eb06954bda621972c546a13", "filesize": 868220928, "download_url": "https://www.openmediavault.org/download.html", "direct_download_url": "https://sourceforge.net/projects/openmediavault/files/6.0.24/openmediavault_6.0.24-amd64.iso"}, {"filename": "openmediavault_5.6.13-amd64.iso", "version": "5.6.13", "md5sum": "f08b41a5111fffca0355d53e26ec47ab", "filesize": 652214272, "download_url": "https://www.openmediavault.org/download.html", "direct_download_url": "https://sourceforge.net/projects/openmediavault/files/5.6.13/openmediavault_5.6.13-amd64.iso/download"}, {"filename": "openmediavault_5.5.11-amd64.iso", "version": "5.5.11", "md5sum": "76baad8e13dd49bee9b4b4a6936b7296", "filesize": 608174080, "download_url": "https://www.openmediavault.org/download.html", "direct_download_url": "https://sourceforge.net/projects/openmediavault/files/5.5.11/openmediavault_5.5.11-amd64.iso/download"}, {"filename": "empty30G.qcow2", "version": "1.0", "md5sum": "3411a599e822f2ac6be560a26405821a", "filesize": 197120, "download_url": "https://sourceforge.net/projects/gns-3/files/Empty%20Qemu%20disk/", "direct_download_url": "https://sourceforge.net/projects/gns-3/files/Empty%20Qemu%20disk/empty30G.qcow2/download"}], "versions": [{"name": "7.0.32", "images": {"hda_disk_image": "empty30G.qcow2", "hdb_disk_image": "empty30G.qcow2", "cdrom_image": "openmediavault_7.0.32-amd64.iso"}}, {"name": "6.5.0", "images": {"hda_disk_image": "empty30G.qcow2", "hdb_disk_image": "empty30G.qcow2", "cdrom_image": "openmediavault_6.5.0-amd64.iso"}}, {"name": "6.0.24", "images": {"hda_disk_image": "empty30G.qcow2", "hdb_disk_image": "empty30G.qcow2", "cdrom_image": "openmediavault_6.0.24-amd64.iso"}}, {"name": "5.6.13", "images": {"hda_disk_image": "empty30G.qcow2", "hdb_disk_image": "empty30G.qcow2", "cdrom_image": "openmediavault_5.6.13-amd64.iso"}}, {"name": "5.5.11", "images": {"hda_disk_image": "empty30G.qcow2", "hdb_disk_image": "empty30G.qcow2", "cdrom_image": "openmediavault_5.5.11-amd64.iso"}}]}