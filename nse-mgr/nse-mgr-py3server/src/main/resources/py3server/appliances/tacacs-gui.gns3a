{"appliance_id": "4e0796b3-2ce5-42a8-a1f2-e1f4beea02e1", "name": "TacacsGUI", "category": "guest", "description": "TacacsGUI Free Access Control Server for Your Network Devices. GUI for powerful daemon. The project of <PERSON><PERSON>, based on tacacs daemon by <PERSON>", "vendor_name": "TacacsGUI", "vendor_url": "https://tacacsgui.com/", "documentation_url": "https://tacacsgui.com/documentation/", "product_name": "TacacsGUI", "product_url": "https://drive.google.com/open?id=1U8tbj14NqEyCmarayhZm54qTyjgsJm4B", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "Credentials:\nSSH ---> username: root ---> password: 1234\nMySQL DB: ---> username: root --> password: tacacs\nWeb interface: ---> username: tacgui ---> password: abc123\n\nDefault for 0.9.82 or above:\nIP Address: **********\nNetmask: *********\nGateway: ********", "port_name_format": "Port{port1}", "qemu": {"adapter_type": "e1000", "adapters": 1, "ram": 4096, "hda_disk_interface": "ide", "arch": "x86_64", "console_type": "telnet", "boot_priority": "c", "kvm": "allow"}, "images": [{"filename": "tacgui-0.9.82-20201008.qcow2", "version": "0.9.82", "md5sum": "dc0c84aa61d8960a23bf3b309a826f3f", "filesize": 2914844672, "download_url": "https://drive.google.com/open?id=1tlDSyoD5dAWgJu6I76CgYV7BkwhScWSS"}, {"filename": "tac_plus.qcow2", "version": "201710201114", "md5sum": "6b5e66590051124dae586b8640b2eb11", "filesize": 160301056, "download_url": "https://drive.google.com/open?id=1U8tbj14NqEyCmarayhZm54qTyjgsJm4B"}], "versions": [{"name": "0.9.82", "images": {"hda_disk_image": "tacgui-0.9.82-20201008.qcow2"}}, {"name": "201710201114", "images": {"hda_disk_image": "tac_plus.qcow2"}}]}