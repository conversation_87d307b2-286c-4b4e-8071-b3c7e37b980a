{"appliance_id": "bd86792e-9870-4b42-8f16-cb2776f1d5d5", "name": "HuaWei CE12800", "category": "multilayer_switch", "description": "CE12800 series switches are high-performance core switches designed for data center networks and high-end campus networks. The switches provide stable, reliable, secure, and high-performance Layer 2/Layer 3 switching services, to help build an elastic, virtualized, agile, and high-quality network.", "vendor_name": "HuaW<PERSON>", "vendor_url": "https://www.huawei.com", "product_name": "HuaWei CE12800", "registry_version": 4, "status": "experimental", "availability": "service-contract", "maintainer": "none", "maintainer_email": "", "port_name_format": "GE1/0/{0}", "qemu": {"adapter_type": "e1000", "adapters": 12, "ram": 2048, "cpus": 2, "hda_disk_interface": "ide", "arch": "x86_64", "console_type": "telnet", "kvm": "require", "options": "-machine type=pc,accel=kvm -serial mon:stdio -nographic -nodefaults -rtc base=utc -cpu host"}, "images": [{"filename": "ce12800-V200R005C10SPC607B607.qcow2", "version": "V200R005C10SPC607B607", "md5sum": "a6f2b358b299e2b5f0da2820ef315368", "filesize": 707002368, "download_url": "https://support.huawei.com/enterprise/en/switches/cloudengine-12800-pid-7542409/software"}], "versions": [{"name": "V200R005C10SPC607B607", "images": {"hda_disk_image": "ce12800-V200R005C10SPC607B607.qcow2"}}]}