{"appliance_id": "da783400-a388-4cb2-8299-f916e3a1cc10", "name": "Ubu<PERSON><PERSON> Docker Guest", "category": "guest", "description": "Ubuntu is a Debian-based Linux operating system, with Unity as its default desktop environment. It is based on free software and named after the Southern African philosophy of ubuntu (literally, \"human-ness\"), which often is translated as \"humanity towards others\" or \"the belief in a universal bond of sharing that connects all humanity\".", "vendor_name": "Canonical", "vendor_url": "http://www.ubuntu.com", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/Ubuntu Docker Guest.png", "product_name": "Ubuntu", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "symbol": "linux_guest.svg", "docker": {"adapters": 1, "image": "gns3/ubuntu:noble", "console_type": "telnet"}}