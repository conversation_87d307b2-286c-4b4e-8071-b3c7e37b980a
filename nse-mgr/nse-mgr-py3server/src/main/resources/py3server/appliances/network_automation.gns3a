{"appliance_id": "a61b580a-99c5-4d77-bc25-61f4c60d214f", "name": "Network Automation", "category": "guest", "description": "This container provides the popular tools used for network automation: Netmiko, NAPALM, Pyntc, and Ansible.", "vendor_name": "GNS3", "vendor_url": "http://www.gns3.com", "product_name": "Network Automation", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "symbol": "linux_guest.svg", "docker": {"adapters": 1, "image": "adosztal/network_automation:latest", "console_type": "telnet"}}