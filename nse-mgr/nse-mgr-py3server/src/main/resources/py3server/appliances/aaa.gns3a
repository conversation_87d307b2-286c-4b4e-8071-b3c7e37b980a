{"appliance_id": "99920801-829d-4689-8231-7183c21ff878", "name": "AAA", "category": "guest", "description": "This appliance provides RADIUS and TACACS+ services with preconfigured users and groups.", "vendor_name": "Ubuntu", "vendor_url": "https://www.ubuntu.com/", "product_name": "AAA", "registry_version": 4, "status": "stable", "maintainer": "<PERSON><PERSON>", "maintainer_email": "<EMAIL>", "usage": "RADIUS users:\n- alice\n- bob\n\nTACACS+ users:\n- gns3 (role: admin)\n- readonly\n\nAll users, as well as the RADIUS/TACACS+ clients have the password 'gns3' set.", "symbol": "linux_guest.svg", "docker": {"adapters": 1, "image": "adosztal/aaa:latest", "console_type": "telnet"}}