{"appliance_id": "361f1e33-f6a5-4e6e-9268-9605053bd059", "name": "Micro Core Linux", "category": "guest", "description": "Micro Core Linux is a smaller variant of Tiny Core without a graphical desktop.\n\nThis is complete Linux system needing few resources to run.", "vendor_name": "Team Tiny Core", "vendor_url": "http://distro.ibiblio.org/tinycorelinux", "documentation_url": "http://wiki.tinycorelinux.net/", "product_name": "Micro Core Linux", "product_url": "http://distro.ibiblio.org/tinycorelinux", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "For version >= 6.4, login/password is gns3. For older version it is tc. Note that sudo works without any password", "symbol": "linux_guest.svg", "qemu": {"adapter_type": "e1000", "adapters": 1, "ram": 64, "hda_disk_interface": "ide", "arch": "i386", "console_type": "telnet", "kvm": "allow"}, "images": [{"filename": "linux-microcore-6.4.img", "version": "6.4", "md5sum": "877419f975c4891c019947ceead5c696", "filesize": 16580608, "download_url": "https://sourceforge.net/projects/gns-3/files/Qemu%20Appliances/", "direct_download_url": "http://downloads.sourceforge.net/project/gns-3/Qemu%20Appliances/linux-microcore-6.4.img"}, {"filename": "linux-microcore-4.0.2-clean.img", "version": "4.0.2", "md5sum": "e13d0d1c0b3999ae2386bba70417930c", "filesize": 26411008, "download_url": "https://sourceforge.net/projects/gns-3/files/Qemu%20Appliances/", "direct_download_url": "http://downloads.sourceforge.net/project/gns-3/Qemu%20Appliances/linux-microcore-4.0.2-clean.img"}, {"filename": "linux-microcore-3.4.1.img", "version": "3.4.1", "md5sum": "fa2ec4b1fffad67d8103c3391bbf9df2", "filesize": 24969216, "download_url": "https://sourceforge.net/projects/gns-3/files/Qemu%20Appliances/", "direct_download_url": "http://downloads.sourceforge.net/project/gns-3/Qemu%20Appliances/linux-microcore-3.4.1.img"}], "versions": [{"name": "6.4", "images": {"hda_disk_image": "linux-microcore-6.4.img"}}, {"name": "4.0.2", "images": {"hda_disk_image": "linux-microcore-4.0.2-clean.img"}}, {"name": "3.4.1", "images": {"hda_disk_image": "linux-microcore-3.4.1.img"}}]}