{"appliance_id": "a8897d1f-5ab1-4075-b538-0acdb6785a07", "name": "StoneWork", "category": "router", "description": "StoneWork is VPP and Ligato based routing platform", "vendor_name": "Pantheon.tech StoneWork router", "vendor_url": "https://pantheon.tech/", "documentation_url": "https://pantheon.tech/documentation-stonework-gns3/", "product_name": "StoneWork", "registry_version": 4, "status": "experimental", "availability": "free", "maintainer": "<PERSON>", "maintainer_email": "<EMAIL>", "docker": {"adapters": 5, "image": "ghcr.io/pantheontech/stonework", "start_command": "/root/stonework-gns3-startup.sh", "environment": "INITIAL_LOGLVL=debug,\nMICROSERVICE_LABEL=stonework,\nETCD_CONFIG=,\nCNF_MGMT_SUBNET=127.0.0.1/8"}}