{"appliance_id": "a8ca42e8-37f0-4bbb-a028-557eb882f909", "name": "ntopng", "category": "guest", "description": "ntopng is the next generation version of the original ntop, a network traffic probe that shows the network usage, similar to what the popular top Unix command does. ntopng is based on libpcap and it has been written in a portable way in order to virtually run on every Unix platform, MacOSX and on Windows as well. ntopng users can use a a web browser to navigate through ntop (that acts as a web server) traffic information and get a dump of the network status. In the latter case, ntopng can be seen as a simple RMON-like agent with an embedded web interface.", "vendor_name": "ntop", "vendor_url": "https://www.ntop.org/", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/ntopng.jpg", "documentation_url": "https://www.ntop.org/guides/ntopng/", "product_name": "ntopng", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "In the web interface login as admin/admin\n\nPersistent configuration:\n- Add \"/var/lib/redis\" as an additional persistent directory.\n- Use \"redis-cli save\" in an auxiliary console to save the configuration.", "docker": {"adapters": 1, "image": "ntop/ntopng:stable", "start_command": "--dns-mode 2 --interface eth0", "console_type": "http", "console_http_port": 3000, "console_http_path": "/"}}