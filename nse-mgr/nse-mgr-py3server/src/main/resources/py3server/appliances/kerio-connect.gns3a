{"appliance_id": "3efc41ea-bdb1-4c05-b12b-ef96a75fd63f", "name": "Kerio Connect", "category": "guest", "description": "Kerio Connect makes email, calendars, contacts and task management easy and affordable. With Kerio Connect, you have immediate, secure access to your communications anytime, anywhere, on any device - without complexity or expensive overhead.", "vendor_name": "Kerio Technologies Inc.", "vendor_url": "http://www.kerio.com", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/Kerio Connect.jpg", "documentation_url": "http://kb.kerio.com/product/kerio-connect/", "product_name": "Kerio Connect", "product_url": "http://www.kerio.com/products/kerio-connect", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "Default ucredentials: root / kerio", "port_name_format": "eth{0}", "qemu": {"adapter_type": "e1000", "adapters": 1, "ram": 2048, "hda_disk_interface": "virtio", "arch": "x86_64", "console_type": "vnc", "boot_priority": "c", "kvm": "require"}, "images": [{"filename": "kerio-connect-appliance-9.2.7-4225-p3-vmware-amd64-disk1.vmdk", "version": "9.2.7p3", "md5sum": "12950c8244300f1d86c85c109f0b3413", "filesize": 914165248, "download_url": "http://www.kerio.com/support/kerio-connect", "direct_download_url": "http://cdn.kerio.com/dwn/connect/connect-9.2.7-4225/kerio-connect-appliance-9.2.7-4225-p3-vmware-amd64-disk1.vmdk"}, {"filename": "kerio-connect-appliance-9.2.6-3851-p1-vmware-amd64-disk1.vmdk", "version": "9.2.6p1", "md5sum": "c6ed064114c6fd70608b7951b707528c", "filesize": 625852004, "download_url": "http://www.kerio.com/support/kerio-connect", "direct_download_url": "http://cdn.kerio.com/dwn/connect/connect-9.2.6-3851/kerio-connect-appliance-9.2.6-3851-p1-vmware-amd64-disk1.vmdk"}, {"filename": "kerio-connect-appliance-9.2.5-3336-p3-vmware-amd64-disk1.vmdk", "version": "9.2.5p3", "md5sum": "f2a202f29e71dc6e8bebce4c05a9e44d", "filesize": 824496128, "download_url": "http://www.kerio.com/support/kerio-connect", "direct_download_url": "http://cdn.kerio.com/dwn/connect/connect-9.2.5-3336/kerio-connect-appliance-9.2.5-3336-p3-vmware-amd64-disk1.vmdk"}, {"filename": "kerio-connect-appliance-9.2.4-3252-vmware-amd64-disk1.vmdk", "version": "9.2.4", "md5sum": "c585587a8de878d3940e42cf389b0f06", "filesize": 720217088, "download_url": "http://www.kerio.com/support/kerio-connect", "direct_download_url": "http://cdn.kerio.com/dwn/connect/connect-9.2.4-3252/kerio-connect-appliance-9.2.4-3252-vmware-amd64-disk1.vmdk"}, {"filename": "kerio-connect-appliance-9.2.3-2929-vmware-amd64-disk1.vmdk", "version": "9.2.3", "md5sum": "29ecf7ac72b32e576e1556af9a741ab2", "filesize": 676196352, "download_url": "http://www.kerio.com/support/kerio-connect", "direct_download_url": "http://cdn.kerio.com/dwn/connect/connect-9.2.3-2929/kerio-connect-appliance-9.2.3-2929-vmware-amd64-disk1.vmdk"}, {"filename": "kerio-connect-appliance-9.2.2-2831-p1-vmware-amd64-disk1.vmdk", "version": "9.2.2p1", "md5sum": "586ab9830602746e6a3438afaa6ee9b8", "filesize": 673714688, "download_url": "http://www.kerio.com/support/kerio-connect", "direct_download_url": "http://cdn.kerio.com/dwn/connect/connect-9.2.2-2831/kerio-connect-appliance-9.2.2-2831-p1-vmware-amd64-disk1.vmdk"}, {"filename": "kerio-connect-appliance-9.2.1-vmware-disk1.vmdk", "version": "9.2.1", "md5sum": "f1d60094c237f55e6737b0da9b5912ce", "filesize": 1851523072, "download_url": "http://www.kerio.com/support/kerio-connect", "direct_download_url": "http://download.kerio.com/dwn/kerio-connect-appliance-vmware-amd64.zip", "compression": "zip"}], "versions": [{"name": "9.2.7p3", "images": {"hda_disk_image": "kerio-connect-appliance-9.2.7-4225-p3-vmware-amd64-disk1.vmdk"}}, {"name": "9.2.6p1", "images": {"hda_disk_image": "kerio-connect-appliance-9.2.6-3851-p1-vmware-amd64-disk1.vmdk"}}, {"name": "9.2.5p3", "images": {"hda_disk_image": "kerio-connect-appliance-9.2.5-3336-p3-vmware-amd64-disk1.vmdk"}}, {"name": "9.2.4", "images": {"hda_disk_image": "kerio-connect-appliance-9.2.4-3252-vmware-amd64-disk1.vmdk"}}, {"name": "9.2.3", "images": {"hda_disk_image": "kerio-connect-appliance-9.2.3-2929-vmware-amd64-disk1.vmdk"}}, {"name": "9.2.2p1", "images": {"hda_disk_image": "kerio-connect-appliance-9.2.2-2831-p1-vmware-amd64-disk1.vmdk"}}, {"name": "9.2.1", "images": {"hda_disk_image": "kerio-connect-appliance-9.2.1-vmware-disk1.vmdk"}}]}