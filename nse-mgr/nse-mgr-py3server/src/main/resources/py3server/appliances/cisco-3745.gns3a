{"appliance_id": "18737edb-e43f-4fb0-8a0f-88982cff58b1", "name": "Cisco 3745", "category": "router", "description": "Cisco 3745 Multiservice Access Router", "vendor_name": "Cisco", "vendor_url": "http://www.cisco.com", "documentation_url": "http://www.cisco.com/c/en/us/support/routers/3745-multiservice-access-router/model.html", "product_name": "3745", "registry_version": 4, "status": "experimental", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "dynamips": {"platform": "c3745", "ram": 256, "nvram": 256, "startup_config": "ios_base_startup-config.txt", "wic0": "WIC-1T", "wic1": "WIC-1T", "wic2": "WIC-1T", "slot0": "GT96100-FE", "slot1": "NM-1FE-TX", "slot2": "NM-4T", "slot3": "", "slot4": ""}, "images": [{"filename": "c3745-adventerprisek9-mz.124-25d.image", "version": "124-25d", "md5sum": "ddbaf74274822b50fa9670e10c75b08f", "filesize": 82053028}], "versions": [{"name": "124-25d", "idlepc": "0x60aa1da0", "images": {"image": "c3745-adventerprisek9-mz.124-25d.image"}}]}