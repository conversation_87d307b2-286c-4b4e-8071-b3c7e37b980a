{"appliance_id": "c80f3423-20bf-4a12-b8fd-3eb2c747eabb", "name": "mcjoin", "category": "guest", "description": "mcjoin is a very simple and easy-to-use tool to test IPv4 and IPv6 multicast.", "vendor_name": "<PERSON>", "vendor_url": "https://github.com/troglobit", "product_name": "mcjoin", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "symbol": "linux_guest.svg", "docker": {"adapters": 1, "image": "troglobit/mcjoin:latest", "console_type": "telnet"}}