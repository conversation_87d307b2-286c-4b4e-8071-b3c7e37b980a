{"appliance_id": "6260ec1e-8ab0-40c6-9e35-fbcc88ce935e", "name": "Cisco NX-OSv", "category": "multilayer_switch", "description": "NXOSv is a reference platform for an implementation of the Cisco Nexus operating system, based on the Nexus 7000-series platforms, running as a full virtual machine on a hypervisor. This includes NXAPI and MPLS LDP support.", "vendor_name": "Cisco", "vendor_url": "http://www.cisco.com/", "documentation_url": "https://developer.cisco.com/docs/modeling-labs/nx-os/", "product_name": "NX-OSv", "product_url": "https://developer.cisco.com/modeling-labs/", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "usage": "The default username/password is admin/admin. A default configuration is present.", "first_port_name": "mgmt0", "port_name_format": "Ethernet2/{port1}", "qemu": {"adapter_type": "e1000", "adapters": 16, "ram": 3072, "hda_disk_interface": "ide", "arch": "x86_64", "console_type": "telnet", "kvm": "require"}, "images": [{"filename": "titanium-final.7.3.0.D1.1.qcow2", "version": "7.3.0", "md5sum": "b4cd6edf15ab4c6bce53c3f6c1e3a742", "filesize": *********, "download_url": "https://learningnetworkstore.cisco.com/myaccount"}, {"filename": "titanium-d1.7.2.0.D1.1.vmdk", "version": "7.2.0", "md5sum": "0ee38c7d717840cb4ca822f4870671d0", "filesize": *********, "download_url": "https://learningnetworkstore.cisco.com/myaccount"}], "versions": [{"name": "7.3.0", "images": {"hda_disk_image": "titanium-final.7.3.0.D1.1.qcow2"}}, {"name": "7.2.0", "images": {"hda_disk_image": "titanium-d1.7.2.0.D1.1.vmdk"}}]}