{"appliance_id": "fc520ae2-a4e5-48c3-9a13-516bb2e94668", "name": "Alpine Linux", "category": "guest", "description": "Alpine Linux is a security-oriented, lightweight Linux distribution based on musl libc and busybox.", "vendor_name": "Alpine Linux Development Team", "vendor_url": "http://alpinelinux.org", "vendor_logo_url": "https://raw.githubusercontent.com/GNS3/gns3-registry/master/vendor-logos/Alpine Linux.png", "documentation_url": "http://wiki.alpinelinux.org", "product_name": "Alpine Linux", "registry_version": 4, "status": "stable", "maintainer": "GNS3 Team", "maintainer_email": "<EMAIL>", "symbol": "linux_guest.svg", "docker": {"adapters": 1, "image": "alpine", "console_type": "telnet"}}