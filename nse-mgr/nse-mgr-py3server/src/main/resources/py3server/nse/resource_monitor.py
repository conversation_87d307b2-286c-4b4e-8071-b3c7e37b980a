# -*- coding: utf-8 -*-
#
# Copyright (C) 2024 GNS3 Technologies Inc.
#
# This program is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.
#
# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.
#
# You should have received a copy of the GNU General Public License
# along with this program.  If not, see <http://www.gnu.org/licenses/>.

import functools
import inspect
import logging
import time
from typing import Callable, Any, Optional

import psutil

log = logging.getLogger(__name__)


class ResourceExhaustedException(Exception):
    """当系统资源耗尽时抛出的异常"""
    pass


class ResourceMonitor:
    """
    资源监控器，用于监控当前进程的内存和CPU使用情况
    """
    
    def __init__(self):
        self._process = psutil.Process()
        self._last_cpu_check = None
        self._last_cpu_percent = 0.0
    
    def get_memory_usage_percent(self) -> float:
        """
        获取当前进程的内存使用百分比
        
        :returns: 内存使用百分比 (0-100)
        """
        try:
            # 获取系统总内存
            system_memory = psutil.virtual_memory()
            # 获取当前进程内存使用情况
            process_memory = self._process.memory_info()
            # 计算当前进程内存使用百分比
            memory_percent = (process_memory.rss / system_memory.total) * 100
            return memory_percent
        except Exception as e:
            log.warning(f"获取内存使用率失败: {e}")
            return 0.0
    
    def get_cpu_usage_percent(self) -> float:
        """
        获取当前进程的CPU使用百分比
        
        :returns: CPU使用百分比 (0-100)
        """
        try:
            current_time = time.time()
            # 如果是第一次调用或距离上次调用超过1秒，重新计算
            if (self._last_cpu_check is None or 
                (current_time - self._last_cpu_check) >= 1.0):
                self._last_cpu_percent = self._process.cpu_percent()
                self._last_cpu_check = current_time
            return self._last_cpu_percent
        except Exception as e:
            log.warning(f"获取CPU使用率失败: {e}")
            return 0.0
    
    def get_system_memory_usage_percent(self) -> float:
        """
        获取系统整体内存使用百分比
        
        :returns: 系统内存使用百分比 (0-100)
        """
        try:
            return psutil.virtual_memory().percent
        except Exception as e:
            log.warning(f"获取系统内存使用率失败: {e}")
            return 0.0


# 全局资源监控器实例
_resource_monitor = ResourceMonitor()


def resource_limit(memory_threshold: float = 90.0, 
                  cpu_threshold: Optional[float] = None,
                  check_system_memory: bool = False,
                  log_usage: bool = True):
    """
    资源限制装饰器，支持同步和异步函数
    
    :param memory_threshold: 内存使用率阈值 (百分比，默认90%)
    :param cpu_threshold: CPU使用率阈值 (百分比，可选)
    :param check_system_memory: 是否检查系统整体内存而非进程内存
    :param log_usage: 是否记录资源使用情况日志
    """
    def decorator(func: Callable) -> Callable:
        def _check_resources():
            """检查资源使用情况的通用函数"""
            # 检查内存使用率
            if check_system_memory:
                current_memory = _resource_monitor.get_system_memory_usage_percent()
                memory_type = "系统"
            else:
                current_memory = _resource_monitor.get_memory_usage_percent()
                memory_type = "进程"
            
            # 检查CPU使用率
            current_cpu = _resource_monitor.get_cpu_usage_percent()
            
            # 记录资源使用情况
            if log_usage:
                log.info(f"执行 {func.__name__} 前资源使用情况: {memory_type}内存 {current_memory:.2f}%, CPU {current_cpu:.2f}%")
            
            # 检查内存阈值
            if current_memory > memory_threshold:
                error_msg = (f"内存使用率过高: {memory_type}内存使用率 {current_memory:.2f}% 超过阈值 {memory_threshold}%, "
                           f"拒绝执行 {func.__name__}")
                log.error(error_msg)
                raise ResourceExhaustedException(error_msg)
            
            # 检查CPU阈值（如果设置了）
            if cpu_threshold is not None and current_cpu > cpu_threshold:
                error_msg = (f"CPU使用率过高: CPU使用率 {current_cpu:.2f}% 超过阈值 {cpu_threshold}%, "
                           f"拒绝执行 {func.__name__}")
                log.error(error_msg)
                raise ResourceExhaustedException(error_msg)
            
            return memory_type
        
        def _log_post_execution(memory_type: str):
            """记录执行后的资源使用情况"""
            if log_usage:
                post_memory = (_resource_monitor.get_system_memory_usage_percent() 
                             if check_system_memory 
                             else _resource_monitor.get_memory_usage_percent())
                post_cpu = _resource_monitor.get_cpu_usage_percent()
                log.info(f"执行 {func.__name__} 后资源使用情况: {memory_type}内存 {post_memory:.2f}%, CPU {post_cpu:.2f}%")
        
        # 检查函数是否为异步函数
        if inspect.iscoroutinefunction(func):
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs) -> Any:
                # 检查资源
                memory_type = _check_resources()
                
                # 执行原异步函数
                try:
                    result = await func(*args, **kwargs)
                    
                    # 执行后再次检查资源使用情况（可选）
                    _log_post_execution(memory_type)
                    
                    return result
                except Exception as e:
                    log.error(f"执行 {func.__name__} 时发生异常: {e}")
                    raise
            
            return async_wrapper
        else:
            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs) -> Any:
                # 检查资源
                memory_type = _check_resources()
                
                # 执行原同步函数
                try:
                    result = func(*args, **kwargs)
                    
                    # 执行后再次检查资源使用情况（可选）
                    _log_post_execution(memory_type)
                    
                    return result
                except Exception as e:
                    log.error(f"执行 {func.__name__} 时发生异常: {e}")
                    raise
            
            return sync_wrapper
    
    return decorator


# 便捷的预设装饰器
def memory_limit(threshold: float = 90.0, check_system: bool = False):
    """
    仅检查内存使用率的装饰器
    
    :param threshold: 内存使用率阈值 (百分比，默认90%)
    :param check_system: 是否检查系统整体内存
    """
    return resource_limit(memory_threshold=threshold, check_system_memory=check_system)


def strict_resource_limit(memory_threshold: float = 80.0, cpu_threshold: float = 80.0):
    """
    严格的资源限制装饰器，同时检查内存和CPU
    
    :param memory_threshold: 内存使用率阈值 (百分比，默认80%)
    :param cpu_threshold: CPU使用率阈值 (百分比，默认80%)
    """
    return resource_limit(memory_threshold=memory_threshold, 
                         cpu_threshold=cpu_threshold, 
                         check_system_memory=True)
