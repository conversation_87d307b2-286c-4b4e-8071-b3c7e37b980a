import os
import random
import sys
from typing import Optional

import psutil

"""
NSE环境配置，这俩将会从Java侧传输过来。
这里将做为项目的初始配置信息，尝试替换gns3_server配置
"""
PY3_SERVER_BASIC_AUTH_USER_KEY = "PY3_SERVER_BASIC_AUTH_USER"
PY3_SERVER_BASIC_AUTH_SECRET_KEY = "PY3_SERVER_BASIC_AUTH_SECRET"
PY3_SERVER_HOST_ID_KEY = "PY3SERVER_HOST_ID"
PY3_SERVER_USER_ID_KEY = "PY3SERVER_USER_ID"
PY3_SERVER_STORAGE_PATH_KEY = "PY3SERVER_PATH"
PY3_SERVER_PORT_KEY = "PY3SERVER_PORT"

LIMIT_MAX_MEMORY_KEY = "LIMIT_MAX_MEMORY"
LIMIT_CPU_CORES_KEY = "LIMIT_CPU_CORES"

"""
# 根据给过来的cpu核心数，随机获取当前可用的cpu
# cpu_cores cpu核心数，至少要1个
# total_cpus 总的可用核心数，默认8个
"""
def rlimit_cpus_by_cores(cpu_cores=1, total_cpus=8):
    p = psutil.Process()
    available_cpus = list(range(total_cpus))
    selected_cpus = random.sample(available_cpus, int(cpu_cores))
    p.cpu_affinity(selected_cpus)


def rlimit_memory(max_memory=10):
    p = psutil.Process()
    current_mem = p.memory_info().rss
    if sys.platform == 'win32':
        print(f"windows 暂时不支持内存限制，当前内存为：{current_mem / 1024 / 1024}MB")
    else:
        if hasattr(psutil.Process(), 'rlimit'):
            p.rlimit(psutil.RLIMIT_AS, (current_mem, max_memory))
        else:
            import resource
            resource.setrlimit(resource.RLIMIT_AS, (current_mem, max_memory))


class NseEnv:
    def __init__(self):
        self.host_id: Optional[str] = os.environ.get(PY3_SERVER_HOST_ID_KEY)
        self.user_id: Optional[str] = os.environ.get("test_user_id") or os.environ.get(PY3_SERVER_USER_ID_KEY)
        self.basic_user: Optional[str] = os.environ.get(PY3_SERVER_BASIC_AUTH_USER_KEY)
        self.basic_secret: Optional[str] = os.environ.get(PY3_SERVER_BASIC_AUTH_SECRET_KEY)
        self.storage_path: Optional[str] = os.environ.get(PY3_SERVER_STORAGE_PATH_KEY)

        self.storage_projects_path: Optional[str] = os.path.join(self.storage_path, "projects")
        self.port: Optional[str] = os.environ.get(PY3_SERVER_PORT_KEY)
        self.host: Optional[str] = "127.0.0.1" # 这里不允许配置，默认不绑定本机访问


        self.max_memory: Optional[str] = os.environ.get(LIMIT_MAX_MEMORY_KEY)
        self.cpu_cores: Optional[str] = os.environ.get(LIMIT_CPU_CORES_KEY)

        # 存放项目数据的路径
        self.__create_storage_path()

    def __create_storage_path(self):
        if self.storage_path:
            try:
                os.makedirs(self.storage_path, exist_ok=True)
                print(f"存储路径创建成功： {self.storage_path}")
                print("__OK__", flush=True)
            except Exception as e:
                print(f"存储路径创建失败： {self.storage_path}: {e}")

    def validate(self) -> bool:
        return all([self.basic_user, self.basic_secret, self.storage_path, self.port])


nse_env = NseEnv()