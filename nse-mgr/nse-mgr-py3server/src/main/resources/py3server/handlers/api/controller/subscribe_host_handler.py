from gns3server.nse.nse_env import nse_env
from gns3server.nse.token_required import token_required, UserContext
from gns3server.web.route import Route

import logging
log = logging.getLogger(__name__)

"""
获取主机信息
@param host_id: 主机ID
@return: 主机信息
@raise: Exception: 服务器主机信息不正确，请检查后重试
"""
class SubscribeHostHandler:
    @Route.get(
        r"/subscribe/host/{host_id}",
        description="subscribe the server host infos",
        parameters={
            "host_id": "HOST UUID",
        },
        status_codes={
            200: "Server has subscribed"
        })
    async def subscribe(request, response):
        host_id = request.match_info["host_id"]
        log.info("subscribe host {}".format(host_id))

        # 校验该host_id是否正确
        if host_id != nse_env.host_id:
            raise Exception("服务器主机信息不正确，请检查后重试")

        # 获取主机信息
        response.set_status(200)
        response.json(
            {
                "host_id": host_id,
                "permi": UserContext.is_admin(),
                "id": host_id,
                "login": getattr(nse_env, 'basic_user', 'default_user'),
                "authorization": "basic",
                "password": getattr(nse_env, 'basic_secret', 'default_password'),
                "host": getattr(nse_env, 'host', 'localhost'),
                "port": nse_env.port
            }
        )

