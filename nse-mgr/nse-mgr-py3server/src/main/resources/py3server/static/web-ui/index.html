<!DOCTYPE html><html lang="en"><head>
    <meta charset="utf-8">
    <title>Ruijie NSE Web UI</title>
    <!-- It's important to have base here because of the script below //-->
    <base href="/static/web-ui/">

    <script>
      var userAgent = navigator.userAgent.toLowerCase();

      //in case we're running in electron because we need it for resources
      if (userAgent.indexOf(' electron/') > -1) {
        var base = document.getElementsByTagName('base');
        if (base.length > 0) {
          base[0].href = './';
        } else {
          document.write('<base href="./" />');
        }
      }
    </script>

    <meta name="viewport" content="width=device-width, initial-scale=1">
    <link rel="icon" type="image/x-icon" href="assets/favicon.ico">

    <script type="application/javascript">
      // Github Pages redirection
      (function () {
        var redirect = sessionStorage.redirect;
        delete sessionStorage.redirect;
        if (redirect && redirect != location.href) {
          history.replaceState(null, null, redirect);
        }
      })();
    </script>
  <style>@charset "UTF-8";:root{--bs-blue:#0d6efd;--bs-indigo:#6610f2;--bs-purple:#6f42c1;--bs-pink:#d63384;--bs-red:#dc3545;--bs-orange:#fd7e14;--bs-yellow:#ffc107;--bs-green:#198754;--bs-teal:#20c997;--bs-cyan:#0dcaf0;--bs-white:#fff;--bs-gray:#6c757d;--bs-gray-dark:#343a40;--bs-gray-100:#f8f9fa;--bs-gray-200:#e9ecef;--bs-gray-300:#dee2e6;--bs-gray-400:#ced4da;--bs-gray-500:#adb5bd;--bs-gray-600:#6c757d;--bs-gray-700:#495057;--bs-gray-800:#343a40;--bs-gray-900:#212529;--bs-primary:#0d6efd;--bs-secondary:#6c757d;--bs-success:#198754;--bs-info:#0dcaf0;--bs-warning:#ffc107;--bs-danger:#dc3545;--bs-light:#f8f9fa;--bs-dark:#212529;--bs-primary-rgb:13,110,253;--bs-secondary-rgb:108,117,125;--bs-success-rgb:25,135,84;--bs-info-rgb:13,202,240;--bs-warning-rgb:255,193,7;--bs-danger-rgb:220,53,69;--bs-light-rgb:248,249,250;--bs-dark-rgb:33,37,41;--bs-white-rgb:255,255,255;--bs-black-rgb:0,0,0;--bs-body-color-rgb:33,37,41;--bs-body-bg-rgb:255,255,255;--bs-font-sans-serif:system-ui,-apple-system,"Segoe UI",Roboto,"Helvetica Neue",Arial,"Noto Sans","Liberation Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji";--bs-font-monospace:SFMono-Regular,Menlo,Monaco,Consolas,"Liberation Mono","Courier New",monospace;--bs-gradient:linear-gradient(180deg,hsla(0,0%,100%,.15),hsla(0,0%,100%,0));--bs-body-font-family:var(--bs-font-sans-serif);--bs-body-font-size:1rem;--bs-body-font-weight:400;--bs-body-line-height:1.5;--bs-body-color:#212529;--bs-body-bg:#fff}*,:after,:before{box-sizing:border-box}@media (prefers-reduced-motion:no-preference){:root{scroll-behavior:smooth}}body{margin:0;font-family:system-ui,-apple-system,Segoe UI,Roboto,Helvetica Neue,Arial,Noto Sans,Liberation Sans,sans-serif,Apple Color Emoji,Segoe UI Emoji,Segoe UI Symbol,Noto Color Emoji;font-family:var(--bs-body-font-family);font-size:1rem;font-size:var(--bs-body-font-size);font-weight:400;font-weight:var(--bs-body-font-weight);line-height:1.5;line-height:var(--bs-body-line-height);color:#212529;color:var(--bs-body-color);text-align:var(--bs-body-text-align);background-color:#fff;background-color:var(--bs-body-bg);-webkit-text-size-adjust:100%;-webkit-tap-highlight-color:transparent}@font-face{font-family:Noto Sans;src:url(NotoSans-Regular.f55982ed9f2bc3af6185.eot);src:local("Noto Sans Regular"),local("NotoSans-Regular"),url(NotoSans-Regular.f55982ed9f2bc3af6185.eot?#iefix) format("embedded-opentype"),url(NotoSans-Regular.730e73a4d4556fa0efe8.woff2) format("woff2"),url(NotoSans-Regular.22c53c8686edcaecdf66.woff) format("woff"),url(NotoSans-Regular.e962f548522aa99bb8f9.ttf) format("truetype"),url(NotoSans-Regular.8142e5b2e99a1cccafb7.svg#NotoSans-Regular) format("svg");font-weight:400;font-style:normal}@font-face{font-family:Noto Sans;src:url(NotoSans-Italic.1506cb93f574152bda3d.eot);src:local("Noto Sans Italic"),local("NotoSans-Italic"),url(NotoSans-Italic.1506cb93f574152bda3d.eot?#iefix) format("embedded-opentype"),url(NotoSans-Italic.ca985d172a576d01c77e.woff2) format("woff2"),url(NotoSans-Italic.8a08f0f08e448e4f522e.woff) format("woff"),url(NotoSans-Italic.08690ed789a5532ed7be.ttf) format("truetype"),url(NotoSans-Italic.cecaa17f122ac96a50f8.svg#NotoSans-Italic) format("svg");font-weight:400;font-style:italic}@font-face{font-family:Noto Sans;src:url(NotoSans-Bold.fe2c3263802c4469728b.eot);src:local("Noto Sans Bold"),local("NotoSans-Bold"),url(NotoSans-Bold.fe2c3263802c4469728b.eot?#iefix) format("embedded-opentype"),url(NotoSans-Bold.3ea2282022a16bb2827b.woff2) format("woff2"),url(NotoSans-Bold.364158e7b3016f83790a.woff) format("woff"),url(NotoSans-Bold.82b1a58ddf26951345dc.ttf) format("truetype"),url(NotoSans-Bold.18ef6a21171328dc11a9.svg#NotoSans-Bold) format("svg");font-weight:700;font-style:normal}@font-face{font-family:Noto Sans;src:url(NotoSans-BoldItalic.b40d78b2f9e2490108d0.eot);src:local("Noto Sans BoldItalic"),local("NotoSans-BoldItalic"),url(NotoSans-BoldItalic.b40d78b2f9e2490108d0.eot?#iefix) format("embedded-opentype"),url(NotoSans-BoldItalic.f2639d17cfb5c6e74edd.woff2) format("woff2"),url(NotoSans-BoldItalic.ed8d2295c0b2e0a854d8.woff) format("woff"),url(NotoSans-BoldItalic.9e49c91c40231a024afb.ttf) format("truetype"),url(NotoSans-BoldItalic.7930d6e32b12448fc0ae.svg#NotoSans-BoldItalic) format("svg");font-weight:700;font-style:italic}body{background-color:#e8ecef}app-root{width:100%}@font-face{font-family:Roboto;font-style:normal;font-display:swap;font-weight:100;src:local("Roboto Thin "),local("Roboto-Thin"),url(roboto-latin-100.c2aa4ab115bf9c6057cb.woff2) format("woff2"),url(roboto-latin-100.a45108d3b34af91f9113.woff) format("woff")}@font-face{font-family:Roboto;font-style:italic;font-display:swap;font-weight:100;src:local("Roboto Thin italic"),local("Roboto-Thinitalic"),url(roboto-latin-100italic.7f839a8652da29745ce4.woff2) format("woff2"),url(roboto-latin-100italic.451d4e559d6f57cdf6a1.woff) format("woff")}@font-face{font-family:Roboto;font-style:normal;font-display:swap;font-weight:300;src:local("Roboto Light "),local("Roboto-Light"),url(roboto-latin-300.37a7069dc30fc663c878.woff2) format("woff2"),url(roboto-latin-300.865f928cbabcc9f8f2b5.woff) format("woff")}@font-face{font-family:Roboto;font-style:italic;font-display:swap;font-weight:300;src:local("Roboto Light italic"),local("Roboto-Lightitalic"),url(roboto-latin-300italic.c64e7e354c88e613c77c.woff2) format("woff2"),url(roboto-latin-300italic.bd5b7a13f2c52b531a2a.woff) format("woff")}@font-face{font-family:Roboto;font-style:normal;font-display:swap;font-weight:400;src:local("Roboto Regular "),local("Roboto-Regular"),url(roboto-latin-400.176f8f5bd5f02b3abfcf.woff2) format("woff2"),url(roboto-latin-400.49ae34d4cc6b98c00c69.woff) format("woff")}@font-face{font-family:Roboto;font-style:italic;font-display:swap;font-weight:400;src:local("Roboto Regular italic"),local("Roboto-Regularitalic"),url(roboto-latin-400italic.d022bc70dc1bf7b3425d.woff2) format("woff2"),url(roboto-latin-400italic.b1d9d9904bfca8802a63.woff) format("woff")}@font-face{font-family:Roboto;font-style:normal;font-display:swap;font-weight:500;src:local("Roboto Medium "),local("Roboto-Medium"),url(roboto-latin-500.f5b74d7ffcdf85b9dd60.woff2) format("woff2"),url(roboto-latin-500.cea99d3e3e13a3a599a0.woff) format("woff")}@font-face{font-family:Roboto;font-style:italic;font-display:swap;font-weight:500;src:local("Roboto Medium italic"),local("Roboto-Mediumitalic"),url(roboto-latin-500italic.0d8bb5b3ee5f5dac9e44.woff2) format("woff2"),url(roboto-latin-500italic.18d00f739ff1e1c52db1.woff) format("woff")}@font-face{font-family:Roboto;font-style:normal;font-display:swap;font-weight:700;src:local("Roboto Bold "),local("Roboto-Bold"),url(roboto-latin-700.c18ee39fb002ad58b6dc.woff2) format("woff2"),url(roboto-latin-700.2267169ee7270a22a963.woff) format("woff")}@font-face{font-family:Roboto;font-style:italic;font-display:swap;font-weight:700;src:local("Roboto Bold italic"),local("Roboto-Bolditalic"),url(roboto-latin-700italic.7d8125ff7f707231fd89.woff2) format("woff2"),url(roboto-latin-700italic.9360531f9bb817f917f0.woff) format("woff")}@font-face{font-family:Roboto;font-style:normal;font-display:swap;font-weight:900;src:local("Roboto Black "),local("Roboto-Black"),url(roboto-latin-900.870c8c1486f76054301a.woff2) format("woff2"),url(roboto-latin-900.bac8362e7a6ea60b6983.woff) format("woff")}@font-face{font-family:Roboto;font-style:italic;font-display:swap;font-weight:900;src:local("Roboto Black italic"),local("Roboto-Blackitalic"),url(roboto-latin-900italic.cb5ad999740e9d8a8bd1.woff2) format("woff2"),url(roboto-latin-900italic.c20d916c1a1b094c1cec.woff) format("woff")}@-webkit-keyframes cdk-text-field-autofill-start{}@-webkit-keyframes cdk-text-field-autofill-end{}</style><link rel="stylesheet" href="styles.f8555f2eecf8cf87f666.css" media="print" onload="this.media='all'"><noscript><link rel="stylesheet" href="styles.f8555f2eecf8cf87f666.css"></noscript></head>
  <!-- <body class="mat-app-background" oncontextmenu="return false;"> -->
  <body class="mat-app-background" oncontextmenu="return false;">
    <app-root></app-root>
    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async="" src="https://www.googletagmanager.com/gtag/js?id=G-0BT7QQV1W1"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag() {
        dataLayer.push(arguments);
      }
      gtag('js', new Date());

      gtag('config', 'G-0BT7QQV1W1');
    </script>
  <script src="runtime.d1c096170c40afd2c94b.js" defer></script><script src="polyfills-es5.63384773aa4a98e3c1b3.js" nomodule defer></script><script src="polyfills.967458a3a5226a77a0ba.js" defer></script><script src="main.1c180bae97853c31114c.js" defer></script>

</body></html>