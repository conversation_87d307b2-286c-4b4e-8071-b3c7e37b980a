# 资源监控装饰器测试套件

本目录包含了对资源监控装饰器（`resource_monitor.py`）的完整测试套件。

## 文件说明

- `test_resource_monitor.py` - 完整的单元测试套件，使用unittest框架
- `run_tests.py` - 简化的测试运行脚本，用于快速验证功能
- `README.md` - 本说明文件

## 测试内容

### 1. 基本功能测试 (TestBasicFunctionality)
- 正常函数执行测试
- 带参数函数测试
- 函数内部异常处理测试

### 2. 内存超限测试 (TestMemoryLimitExceeded)
- 进程内存限制超出测试
- 系统内存限制超出测试
- CPU限制超出测试（使用mock）

### 3. 装饰器变体测试 (TestDecoratorVariants)
- `@memory_limit()` 装饰器测试
- `@memory_limit(check_system=True)` 系统内存检查测试
- `@strict_resource_limit()` 严格限制装饰器测试
- `@resource_limit()` 完整选项测试
- 无CPU阈值的资源限制测试

### 4. 异常处理测试 (TestExceptionHandling)
- 内存监控异常处理测试
- CPU监控异常处理测试
- ResourceExhaustedException异常属性测试
- 函数元数据保留测试

### 5. 并发测试 (TestConcurrency)
- 多线程并发执行测试

### 6. 资源监控器基础功能测试 (TestResourceMonitor)
- 内存使用率获取测试
- CPU使用率获取测试
- 系统内存使用率获取测试

### 7. 性能测试
- 装饰器性能开销测试
- 执行时间基准测试

## 使用方法

### 运行完整测试套件

```bash
# 在 gns3server/tests/utils/ 目录下运行
python test_resource_monitor.py
```

### 运行快速测试

```bash
# 在 gns3server/tests/utils/ 目录下运行
python run_tests.py
```

### 从项目根目录运行

```bash
# 在 gns3server/ 目录下运行
python -m tests.utils.test_resource_monitor
python -m tests.utils.run_tests
```

## 测试预期结果

### 成功的测试应该显示：
- ✓ 基本功能测试通过
- ✓ 内存超限测试通过（正确触发ResourceExhaustedException）
- ✓ 各种装饰器变体测试通过
- ✓ 异常处理测试通过
- ✓ 并发测试通过
- ✓ 性能测试显示合理的执行时间

### 测试覆盖的场景：
1. **正常使用场景**：资源使用率在阈值以下，函数正常执行
2. **资源超限场景**：内存或CPU使用率超过阈值，抛出ResourceExhaustedException
3. **异常处理场景**：监控过程中发生异常，装饰器能够优雅处理
4. **并发场景**：多个线程同时使用装饰器，不会产生竞态条件
5. **性能场景**：装饰器不会显著影响函数执行性能

## 故障排除

### 常见问题

1. **导入错误**
   ```
   ModuleNotFoundError: No module named 'utils.resource_monitor'
   ```
   解决方案：确保在正确的目录下运行测试，或者检查Python路径设置

2. **权限错误**
   ```
   PermissionError: [Errno 13] Permission denied
   ```
   解决方案：确保有足够的权限访问系统资源信息

3. **psutil相关错误**
   ```
   ImportError: No module named 'psutil'
   ```
   解决方案：安装psutil库 `pip install psutil`

### 调试技巧

1. **启用详细日志**：
   ```python
   import logging
   logging.basicConfig(level=logging.DEBUG)
   ```

2. **单独运行特定测试**：
   ```bash
   python -m unittest test_resource_monitor.TestBasicFunctionality.test_normal_execution
   ```

3. **查看资源使用情况**：
   测试过程中会输出当前的内存和CPU使用率，可以用来判断阈值设置是否合理

## 扩展测试

如果需要添加新的测试用例，可以：

1. 在 `test_resource_monitor.py` 中添加新的测试类或测试方法
2. 在 `run_tests.py` 中添加新的快速测试函数
3. 确保新测试遵循现有的命名约定和结构

## 注意事项

1. **阈值设置**：测试中使用的阈值（如0.1%）是为了触发异常，实际使用时应设置合理的阈值
2. **系统依赖**：某些测试依赖于系统的实际资源使用情况，在高负载系统上可能表现不同
3. **时间敏感**：性能测试结果可能因系统负载而变化，应关注趋势而非绝对值
4. **Mock使用**：部分测试使用了mock来模拟特定条件，确保测试的可重复性

## 贡献指南

在修改资源监控装饰器时，请确保：
1. 所有现有测试仍然通过
2. 为新功能添加相应的测试用例
3. 更新本README文件以反映变更
4. 保持测试的可读性和可维护性