package com.ruijie.nse.mgr.repository.entity.enums;

import com.baomidou.mybatisplus.annotation.IEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum EventTypeEnum implements IEnum<String> {

    EVT_LIC_FILE_ERROR("lic文件错误", "ERROR"),
    EVT_LIC_CONTEXT_ERROR("lic上下文内容错误", "ERROR"),
    EVT_LIC_VERIFY_FAILED("lic校验失败", "ERROR"),
    EVT_LIC_EXPIRED("lic已过期", "ERROR"),
    EVT_LIC_CANCEL("lic已注销", "ERROR"),
    EVT_THREAT_DETECTED("系统安全检查", "WARNING"),
    EVT_SYSTEM_UP("系统启动", "INFO"),
    EVT_SYSTEM_DOWN("系统关闭", "INFO")
    ;

    private final String desc;
    private final String level;

    @Override
    public String getValue() {
        return this.name();
    }
}
