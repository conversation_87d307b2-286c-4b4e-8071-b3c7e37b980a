package com.ruijie.nse.mgr.repository.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.ruijie.nse.common.entity.DataEntity;
import com.ruijie.nse.mgr.repository.pojo.bo.BasicAuthBo;
import lombok.*;


/**
 * python服务主机信息
 * <AUTHOR>
 * @since 2024-01-01
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName(value = "ser_hosts", autoResultMap = true)
public class SerHosts extends DataEntity {

    /**
     * 服务主机ip
     */
    private String serverIp;

    /**
     * 服务port
     */
    private Integer serverPort;

    /**
     * basicAuth信息
     */
    @TableField(value = "basic_auth", typeHandler = JacksonTypeHandler.class)
    private BasicAuthBo basicAuth;

    /**
     * 进程id
     */
    private long pid;

    /**
     * 最大可用内存，单位GB
     */
    private Double maxMemory;

    /**
     * 最大可用磁盘，单位GB
     */
    private Double maxDisk;

    /**
     * 最大可用CPU核心数
     */
    private long cpuCore;

    private String userId;

    private String storagePath;

    /**
     * 当前已用内存，单位GB
     */
    private Double currentMemory;

}
