package com.ruijie.nse.mgr.repository.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruijie.nse.mgr.repository.dto.output.UserOfficialOutput;
import com.ruijie.nse.mgr.repository.entity.UserOfficial;

import java.util.List;


/**
 * 用户-班级信息关联表 Mapper 接口
 */
public interface UserOfficialDao extends BaseMapper<UserOfficial> {

    /**
     * 根据用户id查询用户-班级信息
     *
     * @param userIds
     * @return
     */
    List<UserOfficialOutput> selectUserOfficialByUserId(List<String> userIds);
}
