-dontshrink
-dontoptimize
-adaptclassstrings
-keepattributes  Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod
-keepnames interface ** { *; }
-keepparameternames
-keepclassmembers class * {
    @org.springframework.context.annotation.Bean *;
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @org.springframework.stereotype.Service *;
    @org.springframework.stereotype.Component *;
    @org.springframework.scheduling.annotation.Scheduled *;
}
-keepclassmembers class * implements java.io.Serializable {*;}
-ignorewarnings
-dontnote
-printconfiguration

-keep class com.**.dao.* {*;}
-keep class com.**.entity.* {*;}
-keep class com.**.dto.* {*;}
-keep class com.**.controller.* {*;}

-keep public class com.ruijie.nse.mgr.launcher.NseMgrApplication {
    public static void main(java.lang.String[]);
    public <init>();
    *;
}

-keep class org.springframework.boot.loader.** {
    *;
}

-keep public class org.springframework.boot.loader.JarLauncher {
    public <init>(java.lang.String);
    public static void main(java.lang.String[]);
}

-keep public class * extends org.springframework.boot.loader.launch.JarLauncher
-keep public class * extends org.springframework.boot.loader.launch.PropertiesLauncher
-keep public class * extends org.springframework.boot.loader.launch.WarLauncher

-adaptclassstrings org.springframework.boot.loader