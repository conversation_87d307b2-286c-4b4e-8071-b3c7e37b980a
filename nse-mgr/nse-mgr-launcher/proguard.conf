# 基本配置
-dontshrink
-dontoptimize
-adaptclassstrings
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod,RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations,RuntimeVisibleParameterAnnotations,RuntimeInvisibleParameterAnnotations
-keepnames interface ** { *; }
-keepparameternames
-ignorewarnings
-dontnote
-printconfiguration
-printmapping proguard_map.txt
-printseeds proguard_seed.txt

# Spring Boot 和 Spring Framework 相关保护
-keepclassmembers class * {
    @org.springframework.context.annotation.Bean *;
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @org.springframework.stereotype.Service *;
    @org.springframework.stereotype.Component *;
    @org.springframework.stereotype.Repository *;
    @org.springframework.stereotype.Controller *;
    @org.springframework.web.bind.annotation.RestController *;
    @org.springframework.scheduling.annotation.Scheduled *;
    @org.springframework.boot.context.properties.ConfigurationProperties *;
    @org.springframework.context.annotation.Configuration *;
    @jakarta.annotation.PostConstruct *;
    @jakarta.annotation.PreDestroy *;
}

# 序列化相关
-keepclassmembers class * implements java.io.Serializable {*;}

# JPA/Hibernate 实体保护
-keep @jakarta.persistence.Entity class * { *; }
-keep @jakarta.persistence.Embeddable class * { *; }
-keep @jakarta.persistence.MappedSuperclass class * { *; }

# 应用程序包保护
-keep class com.ruijie.nse.mgr.**.dao.** { *; }
-keep class com.ruijie.nse.mgr.**.entity.** { *; }
-keep class com.ruijie.nse.mgr.**.dto.** { *; }
-keep class com.ruijie.nse.mgr.**.controller.** { *; }
-keep class com.ruijie.nse.mgr.**.service.** { *; }
-keep class com.ruijie.nse.mgr.**.config.** { *; }

# 主启动类保护
-keep public class com.ruijie.nse.mgr.launcher.NseMgrApplication {
    public static void main(java.lang.String[]);
    public <init>();
    *;
}

# Spring Boot Loader 保护（新版本）
-keep class org.springframework.boot.loader.** { *; }
-keep class org.springframework.boot.loader.launch.** { *; }
-keep class org.springframework.boot.loader.jar.** { *; }
-keep class org.springframework.boot.loader.net.** { *; }
-keep class org.springframework.boot.loader.nio.** { *; }
-keep class org.springframework.boot.loader.zip.** { *; }

# 启动器类保护
-keep public class org.springframework.boot.loader.launch.JarLauncher {
    public <init>();
    public static void main(java.lang.String[]);
}

-keep public class * extends org.springframework.boot.loader.launch.JarLauncher
-keep public class * extends org.springframework.boot.loader.launch.PropertiesLauncher
-keep public class * extends org.springframework.boot.loader.launch.WarLauncher

# 反射相关保护
-keepclassmembers class * {
    @org.springframework.web.bind.annotation.RequestMapping *;
    @org.springframework.web.bind.annotation.GetMapping *;
    @org.springframework.web.bind.annotation.PostMapping *;
    @org.springframework.web.bind.annotation.PutMapping *;
    @org.springframework.web.bind.annotation.DeleteMapping *;
    @org.springframework.web.bind.annotation.PatchMapping *;
}

# Jackson JSON 处理
-keep class com.fasterxml.jackson.** { *; }
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* *;
}

# MyBatis Plus 相关
-keep class com.baomidou.mybatisplus.** { *; }
-keep interface com.ruijie.nse.mgr.**.mapper.** { *; }

# 适配类字符串
-adaptclassstrings org.springframework.boot.loader
-adaptclassstrings com.ruijie.nse.mgr