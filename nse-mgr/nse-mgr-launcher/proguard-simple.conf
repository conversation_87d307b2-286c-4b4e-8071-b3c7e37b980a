# 简化的ProGuard配置，专门解决Spring Boot混淆问题

# 基本设置
-dontshrink
-dontoptimize
-ignorewarnings
-dontnote
-printmapping proguard_map.txt
-printseeds proguard_seed.txt

# 保持属性
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod

# 主启动类
-keep public class com.ruijie.nse.mgr.launcher.NseMgrApplication {
    public static void main(java.lang.String[]);
    *;
}

# Spring相关注解类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }

# 保持所有公共API
-keep public class com.ruijie.nse.mgr.** {
    public *;
}

# 保持实体类
-keep class com.ruijie.nse.mgr.**.entity.** { *; }
-keep class com.ruijie.nse.mgr.**.dto.** { *; }

# 保持Mapper接口
-keep interface com.ruijie.nse.mgr.**.mapper.** { *; }

# Spring Boot相关
-keep class org.springframework.boot.** { *; }
-keep class org.springframework.** { *; }

# 序列化
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# 反射相关
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @jakarta.annotation.PostConstruct *;
    @jakarta.annotation.PreDestroy *;
}
