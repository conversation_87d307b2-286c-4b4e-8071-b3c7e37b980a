-injars target\nse-mgr-launcher-1.0.0-SNAPSHOT.jar(!META-INF/maven/**)
-outjars target\nse-mgr-launcher-1.0.0-SNAPSHOT-obfuscated.jar

-libraryjars 'C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-license\target\classes'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter\3.4.2\spring-boot-starter-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot\3.4.2\spring-boot-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-autoconfigure\3.4.2\spring-boot-autoconfigure-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-logging\3.4.2\spring-boot-starter-logging-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\ch\qos\logback\logback-classic\1.5.16\logback-classic-1.5.16.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\ch\qos\logback\logback-core\1.5.16\logback-core-1.5.16.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\logging\log4j\log4j-to-slf4j\2.24.3\log4j-to-slf4j-2.24.3.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\slf4j\jul-to-slf4j\2.0.16\jul-to-slf4j-2.0.16.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\yaml\snakeyaml\2.3\snakeyaml-2.3.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-security\3.4.2\spring-boot-starter-security-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\security\spring-security-config\6.4.2\spring-security-config-6.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\jackson\core\jackson-databind\2.18.2\jackson-databind-2.18.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\jackson\core\jackson-annotations\2.18.2\jackson-annotations-2.18.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\jackson\core\jackson-core\2.18.2\jackson-core-2.18.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-cache\3.4.2\spring-boot-starter-cache-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-context-support\6.2.2\spring-context-support-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\bouncycastle\bcprov-jdk18on\1.77\bcprov-jdk18on-1.77.jar'(!META-INF/**,!module-info.class)
-libraryjars 'C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-common\target\nse-mgr-common-1.0.0-SNAPSHOT.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-websocket\3.4.2\spring-boot-starter-websocket-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-websocket\6.2.2\spring-websocket-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-repository\target\nse-mgr-repository-1.0.0-SNAPSHOT.jar'(!META-INF/**,!module-info.class)
-libraryjars 'C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-course\target\classes'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-boot-starter\1.5.4\mybatis-plus-join-boot-starter-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-extension\1.5.4\mybatis-plus-join-extension-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-core\1.5.4\mybatis-plus-join-core-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-annotation\1.5.4\mybatis-plus-join-annotation-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-v312\1.5.4\mybatis-plus-join-adapter-v312-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-base\1.5.4\mybatis-plus-join-adapter-base-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-jsqlparser\1.5.4\mybatis-plus-join-adapter-jsqlparser-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-jsqlparser-v46\1.5.4\mybatis-plus-join-adapter-jsqlparser-v46-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-v33x\1.5.4\mybatis-plus-join-adapter-v33x-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-v3431\1.5.4\mybatis-plus-join-adapter-v3431-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-v352\1.5.4\mybatis-plus-join-adapter-v352-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-adapter-v355\1.5.4\mybatis-plus-join-adapter-v355-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\yulichang\mybatis-plus-join-wrapper-ext\1.5.4\mybatis-plus-join-wrapper-ext-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-sys\target\classes'(!META-INF/**,!module-info.class)
-libraryjars 'C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-py3server\target\classes'(!META-INF/**,!module-info.class)
-libraryjars 'C:\__ruijie_work_space\nse\nse-service\nse-mgr\nse-mgr-jobs\target\classes'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-web\3.4.2\spring-boot-starter-web-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-json\3.4.2\spring-boot-starter-json-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.18.2\jackson-datatype-jdk8-2.18.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.18.2\jackson-module-parameter-names-2.18.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-tomcat\3.4.2\spring-boot-starter-tomcat-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.34\tomcat-embed-core-10.1.34.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.34\tomcat-embed-el-10.1.34.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.34\tomcat-embed-websocket-10.1.34.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-web\6.2.2\spring-web-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-beans\6.2.2\spring-beans-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\micrometer\micrometer-observation\1.14.3\micrometer-observation-1.14.3.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\micrometer\micrometer-commons\1.14.3\micrometer-commons-1.14.3.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-webmvc\6.2.2\spring-webmvc-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-context\6.2.2\spring-context-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-expression\6.2.2\spring-expression-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\flywaydb\flyway-core\10.20.1\flyway-core-10.20.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\jackson\dataformat\jackson-dataformat-toml\2.18.2\jackson-dataformat-toml-2.18.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.18.2\jackson-datatype-jsr310-2.18.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\flywaydb\flyway-database-postgresql\10.20.1\flyway-database-postgresql-10.20.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\slf4j\slf4j-api\2.0.16\slf4j-api-2.0.16.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-core\6.2.2\spring-core-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-jcl\6.2.2\spring-jcl-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\security\spring-security-core\6.4.2\spring-security-core-6.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\security\spring-security-crypto\6.4.2\spring-security-crypto-6.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\security\spring-security-web\6.4.2\spring-security-web-6.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\ruijie\nse\nse-common\1.0.0-SNAPSHOT\nse-common-1.0.0-SNAPSHOT.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-spring-boot3-starter\3.5.12\mybatis-plus-spring-boot3-starter-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus\3.5.12\mybatis-plus-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-core\3.5.12\mybatis-plus-core-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-annotation\3.5.12\mybatis-plus-annotation-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-spring\3.5.12\mybatis-plus-spring-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\mybatis\mybatis\3.5.19\mybatis-3.5.19.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\mybatis\mybatis-spring\3.0.4\mybatis-spring-3.0.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-spring-boot-autoconfigure\3.5.12\mybatis-plus-spring-boot-autoconfigure-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-jdbc\3.4.2\spring-boot-starter-jdbc-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\zaxxer\HikariCP\5.1.0\HikariCP-5.1.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-jdbc\6.2.2\spring-jdbc-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-jsqlparser\3.5.12\mybatis-plus-jsqlparser-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\jsqlparser\jsqlparser\5.1\jsqlparser-5.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-jsqlparser-common\3.5.12\mybatis-plus-jsqlparser-common-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\baomidou\mybatis-plus-extension\3.5.12\mybatis-plus-extension-3.5.12.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\jakarta\servlet\jakarta.servlet-api\6.0.0\jakarta.servlet-api-6.0.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\jsonwebtoken\jjwt\0.12.6\jjwt-0.12.6.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\jsonwebtoken\jjwt-api\0.12.6\jjwt-api-0.12.6.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\ehcache\ehcache\3.10.8\ehcache-3.10.8.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\javax\cache\cache-api\1.1.1\cache-api-1.1.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\oshi\oshi-core\6.8.2\oshi-core-6.8.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\net\java\dev\jna\jna\5.17.0\jna-5.17.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\net\java\dev\jna\jna-platform\5.17.0\jna-platform-5.17.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\aliyun\oss\aliyun-sdk-oss\3.18.0\aliyun-sdk-oss-3.18.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\commons-codec\commons-codec\1.17.2\commons-codec-1.17.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\aliyun\aliyun-java-sdk-core\4.5.10\aliyun-java-sdk-core-4.5.10.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\google\code\gson\gson\2.11.0\gson-2.11.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\google\errorprone\error_prone_annotations\2.27.0\error_prone_annotations-2.27.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\poi\poi-ooxml\5.3.0\poi-ooxml-5.3.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\poi\poi\5.3.0\poi-5.3.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\commons\commons-math3\3.6.1\commons-math3-3.6.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\zaxxer\SparseBitSet\1.3\SparseBitSet-1.3.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\poi\poi-ooxml-lite\5.3.0\poi-ooxml-lite-5.3.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\xmlbeans\xmlbeans\5.2.1\xmlbeans-5.2.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\commons\commons-compress\1.26.2\commons-compress-1.26.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\commons\commons-lang3\3.17.0\commons-lang3-3.17.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\commons-io\commons-io\2.16.1\commons-io-2.16.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\github\virtuald\curvesapi\1.08\curvesapi-1.08.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\logging\log4j\log4j-api\2.24.3\log4j-api-2.24.3.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\apache\commons\commons-collections4\4.4\commons-collections4-4.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-aop\3.4.2\spring-boot-starter-aop-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-aop\6.2.2\spring-aop-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\aspectj\aspectjweaver\1.9.22.1\aspectjweaver-1.9.22.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\boot\spring-boot-starter-integration\3.4.2\spring-boot-starter-integration-3.4.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\integration\spring-integration-core\6.4.1\spring-integration-core-6.4.1.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-messaging\6.2.2\spring-messaging-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\spring-tx\6.2.2\spring-tx-6.2.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\springframework\retry\spring-retry\2.0.11\spring-retry-2.0.11.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\io\projectreactor\reactor-core\3.7.2\reactor-core-3.7.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\reactivestreams\reactive-streams\1.0.4\reactive-streams-1.0.4.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\hibernate\validator\hibernate-validator\8.0.2.Final\hibernate-validator-8.0.2.Final.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\jboss\logging\jboss-logging\3.6.1.Final\jboss-logging-3.6.1.Final.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\com\fasterxml\classmate\1.7.0\classmate-1.7.0.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\postgresql\postgresql\42.7.5\postgresql-42.7.5.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\dromara\hutool\hutool-all\6.0.0-M22\hutool-all-6.0.0-M22.jar'(!META-INF/**,!module-info.class)
-libraryjars 'D:\Rj_maven_repository\org\projectlombok\lombok\1.18.36\lombok-1.18.36.jar'(!META-INF/**,!module-info.class)
-libraryjars 'E:\Software\jdk-21.0.6_windows-x64_bin\jdk-21.0.6\jmods'(!META-INF/**,!module-info.class)

-keepdirectories BOOT-INF/**,META-INF/**
-dontshrink
-dontoptimize
-printmapping target\proguard_map.txt
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod,RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations,Signature
-dontnote
-ignorewarnings
-printconfiguration proguard_config.txt
-printseeds target\proguard_seed.txt



# === 关键修复：保持Spring Boot Loader结构 ===
# 保持所有Spring Boot Loader类不被混淆
-keep class org.springframework.boot.loader.** {
    <fields>;
    <methods>;
}

# === 应用程序类保护 ===
# 主启动类
-keep public class com.ruijie.nse.mgr.launcher.NseMgrApplication {
    <fields>;
    public static void main(java.lang.String[]);
    public <init>();
    <methods>;
}

# 保持所有应用程序包的公共类
-keep public class com.ruijie.nse.mgr.** {
    public <fields>;
    public <methods>;
}

# === Spring框架相关保护 ===
# Spring注解类
-keep @org.springframework.stereotype.Component class * {
    <fields>;
    <methods>;
}

-keep @org.springframework.stereotype.Service class * {
    <fields>;
    <methods>;
}

-keep @org.springframework.stereotype.Repository class * {
    <fields>;
    <methods>;
}

-keep @org.springframework.stereotype.Controller class * {
    <fields>;
    <methods>;
}

-keep @org.springframework.web.bind.annotation.RestController class * {
    <fields>;
    <methods>;
}

-keep @org.springframework.context.annotation.Configuration class * {
    <fields>;
    <methods>;
}

-keep @org.springframework.boot.context.properties.ConfigurationProperties class * {
    <fields>;
    <methods>;
}

# Spring方法级注解
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired
    <fields>;
    @org.springframework.beans.factory.annotation.Value
    <fields>;
    @org.springframework.context.annotation.Bean
    <fields>;
    @org.springframework.web.bind.annotation.RequestMapping
    <fields>;
    @org.springframework.web.bind.annotation.*Mapping
    <fields>;
    @jakarta.annotation.PostConstruct
    <fields>;
    @jakarta.annotation.PreDestroy
    <fields>;
    @org.springframework.beans.factory.annotation.Autowired
    <methods>;
    @org.springframework.beans.factory.annotation.Value
    <methods>;
    @org.springframework.context.annotation.Bean
    <methods>;
    @org.springframework.web.bind.annotation.RequestMapping
    <methods>;
    @org.springframework.web.bind.annotation.*Mapping
    <methods>;
    @jakarta.annotation.PostConstruct
    <methods>;
    @jakarta.annotation.PreDestroy
    <methods>;
}

# === 数据层保护 ===
# 实体类和DTO
-keep class com.ruijie.nse.mgr.**.entity.** {
    <fields>;
    <methods>;
}

-keep class com.ruijie.nse.mgr.**.dto.** {
    <fields>;
    <methods>;
}

-keep class com.ruijie.nse.mgr.**.vo.** {
    <fields>;
    <methods>;
}

# MyBatis Mapper接口
-keep interface  com.ruijie.nse.mgr.**.mapper.** {
    <fields>;
    <methods>;
}

# JPA实体
-keep @jakarta.persistence.Entity class * {
    <fields>;
    <methods>;
}

-keep @jakarta.persistence.Embeddable class * {
    <fields>;
    <methods>;
}

# === 序列化支持 ===
-keepclassmembers class * extends java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# === JSON序列化支持 ===
-keep class com.fasterxml.jackson.** {
    <fields>;
    <methods>;
}

-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.*
    <fields>;
    @com.fasterxml.jackson.annotation.*
    <methods>;
}

# === 反射相关保护 ===
# 保持所有枚举
-keepclassmembers enum  * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

-keep class **.R

-keep class **.R$* {
    <fields>;
    <methods>;
}
