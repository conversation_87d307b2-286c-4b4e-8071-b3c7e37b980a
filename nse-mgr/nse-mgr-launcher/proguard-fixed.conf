# 修复Spring Boot Fat Jar混淆问题的ProGuard配置

# 基本设置
-dontshrink
-dontoptimize
-ignorewarnings
-dontnote
-printmapping proguard_map.txt
-printseeds proguard_seed.txt
-printconfiguration proguard_config.txt

# 保持属性和注解
-keepattributes Exceptions,InnerClasses,Signature,Deprecated,SourceFile,LineNumberTable,*Annotation*,EnclosingMethod,RuntimeVisibleAnnotations,RuntimeInvisibleAnnotations

# === 关键修复：保持Spring Boot Loader结构 ===
# 保持所有Spring Boot Loader类不被混淆
-keep class org.springframework.boot.loader.** { *; }

# 保持BOOT-INF结构
-keepdirectories BOOT-INF/**
-keepdirectories META-INF/**

# === 应用程序类保护 ===
# 主启动类
-keep public class com.ruijie.nse.mgr.launcher.NseMgrApplication {
    public static void main(java.lang.String[]);
    public <init>();
    *;
}

# 保持所有应用程序包的公共类
-keep public class com.ruijie.nse.mgr.** {
    public *;
}

# === Spring框架相关保护 ===
# Spring注解类
-keep @org.springframework.stereotype.Component class * { *; }
-keep @org.springframework.stereotype.Service class * { *; }
-keep @org.springframework.stereotype.Repository class * { *; }
-keep @org.springframework.stereotype.Controller class * { *; }
-keep @org.springframework.web.bind.annotation.RestController class * { *; }
-keep @org.springframework.context.annotation.Configuration class * { *; }
-keep @org.springframework.boot.context.properties.ConfigurationProperties class * { *; }

# Spring方法级注解
-keepclassmembers class * {
    @org.springframework.beans.factory.annotation.Autowired *;
    @org.springframework.beans.factory.annotation.Value *;
    @org.springframework.context.annotation.Bean *;
    @org.springframework.web.bind.annotation.RequestMapping *;
    @org.springframework.web.bind.annotation.*Mapping *;
    @jakarta.annotation.PostConstruct *;
    @jakarta.annotation.PreDestroy *;
}

# === 数据层保护 ===
# 实体类和DTO
-keep class com.ruijie.nse.mgr.**.entity.** { *; }
-keep class com.ruijie.nse.mgr.**.dto.** { *; }
-keep class com.ruijie.nse.mgr.**.vo.** { *; }

# MyBatis Mapper接口
-keep interface com.ruijie.nse.mgr.**.mapper.** { *; }

# JPA实体
-keep @jakarta.persistence.Entity class * { *; }
-keep @jakarta.persistence.Embeddable class * { *; }

# === 序列化支持 ===
-keepclassmembers class * implements java.io.Serializable {
    static final long serialVersionUID;
    private static final java.io.ObjectStreamField[] serialPersistentFields;
    private void writeObject(java.io.ObjectOutputStream);
    private void readObject(java.io.ObjectInputStream);
    java.lang.Object writeReplace();
    java.lang.Object readResolve();
}

# === JSON序列化支持 ===
-keep class com.fasterxml.jackson.** { *; }
-keepclassmembers class * {
    @com.fasterxml.jackson.annotation.* *;
}

# === 反射相关保护 ===
# 保持所有枚举
-keepclassmembers enum * {
    public static **[] values();
    public static ** valueOf(java.lang.String);
}

# 保持泛型信息
-keepattributes Signature

# === 资源文件保护 ===
-keepdirectories
-keep class **.R
-keep class **.R$* { *; }
