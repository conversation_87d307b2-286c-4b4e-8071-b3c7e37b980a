import{d as e,r,g as s,f as l,m as t,w as o,a1 as a,aC as i,i as m}from"./index.Ckm1SagX.js";import{a as n,E as p}from"./form-item.CUMILu98.js";import{_ as u}from"./index.vue_vue_type_script_setup_true_lang.8dFNY07J.js";/* empty css             */import{E as d}from"./index.CMOQuMWt.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./castArray.Chmjnshw.js";import"./aria.C1IWO_Rd.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./checkbox.CyAsOZKA.js";import"./index.BLy3nyPI.js";import"./event.BwRzfsZt.js";import"./isEqual.CZKKciWh.js";import"./index.BRUQ9gWw.js";import"./radio.D_DAkhYS.js";/* empty css            */import"./select.DHkh6uhw.js";import"./popper.DpZVcW1M.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";import"./scrollbar.6rbryiG1.js";import"./index.BPj3iklg.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./index.Byj-i824.js";import"./scroll.XdyICIdv.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.B0geSHq7.js";import"./vnode.BkZiIFpS.js";const c={class:"app-container"},j=e({__name:"dictionary",setup(e){const j=r("1"),_=r(1),f=r(["1","2"]);return(e,r)=>{const v=d,b=u,y=n,x=p;return l(),s("div",c,[t(v,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/dictionary.vue",type:"primary",target:"_blank",class:"mb-[20px]"},{default:o(()=>r[4]||(r[4]=[a(" 示例源码 请点击>>>> ")])),_:1,__:[4]}),t(x,null,{default:o(()=>[t(y,{label:"性别"},{default:o(()=>[t(b,{modelValue:m(j),"onUpdate:modelValue":r[0]||(r[0]=e=>i(j)?j.value=e:null),code:"gender"},null,8,["modelValue"]),t(v,{underline:"never",type:"primary",class:"ml-5"},{default:o(()=>r[5]||(r[5]=[a(' 值为String: const value = ref("1"); ')])),_:1,__:[5]})]),_:1}),t(y,{label:"性别"},{default:o(()=>[t(b,{modelValue:m(_),"onUpdate:modelValue":r[1]||(r[1]=e=>i(_)?_.value=e:null),code:"gender"},null,8,["modelValue"]),t(v,{underline:"never",type:"success",class:"ml-5"},{default:o(()=>r[6]||(r[6]=[a(" 值为Number: const value = ref(1); ")])),_:1,__:[6]})]),_:1}),t(y,{label:"单选框字典"},{default:o(()=>[t(b,{modelValue:m(_),"onUpdate:modelValue":r[2]||(r[2]=e=>i(_)?_.value=e:null),type:"radio",code:"gender"},null,8,["modelValue"]),t(v,{underline:"never",type:"success",class:"ml-5"},{default:o(()=>r[7]||(r[7]=[a(" 值为Number: const value = ref(1); ")])),_:1,__:[7]})]),_:1}),t(y,{label:"复选框字典"},{default:o(()=>[t(b,{modelValue:m(f),"onUpdate:modelValue":r[3]||(r[3]=e=>i(f)?f.value=e:null),type:"checkbox",code:"gender"},null,8,["modelValue"]),t(v,{underline:"never",type:"success",class:"ml-5"},{default:o(()=>r[8]||(r[8]=[a(' 值为Array: const value = ref(["1", "2"]); ')])),_:1,__:[8]})]),_:1})]),_:1})])}}});export{j as default};
