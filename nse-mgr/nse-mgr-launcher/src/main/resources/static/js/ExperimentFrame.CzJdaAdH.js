import{d as e,r as a,am as r,o as n,aY as s,aZ as l,g as o,C as t,h as i,X as u,i as c,m as v,w as d,E as m,F as f,Z as p,n as _,a2 as g,a_ as x,a$ as E,a1 as w,aD as y,f as j}from"./index.Ckm1SagX.js";/* empty css               */import{u as k}from"./experiment.store.7iwyeQEo.js";import{E as h}from"./index.CbYeWxT8.js";import{_ as I}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BRUQ9gWw.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";const L={class:"experiment-frame"},b={key:0,class:"loading-container"},C={class:"loading-tip"},S={key:1,class:"error-container"},D={class:"error-actions"},U={key:2,class:"error-container"},Z={class:"error-actions"},F=["src"],T=I(e({__name:"ExperimentFrame",setup(e){const I=r(),T=y(),q=k(),R=a(),V=a(!0),X=a(""),Y=a(!1),$=a(null),z=a("请稍候..."),A=a(null),B=I.query.sessionId;let G=null;const H=()=>{V.value=!1,X.value="",G&&(clearInterval(G),G=null)},J=e=>{V.value=!1,X.value="实验环境加载失败，请检查网络连接",G&&(clearInterval(G),G=null)},K=()=>{B&&q.removeSession(B),T.back()},M=()=>{Y.value=!!document.fullscreenElement};n(()=>{(async()=>{if(!B)return X.value="缺少实验会话ID",void(V.value=!1);const e=q.getSession(B);if(!e)return X.value="实验会话不存在或已过期",void(V.value=!1);A.value=e,R.value=e.signUrl,g(()=>{$.value&&e.signUrl&&($.value.src=e.signUrl)});const a=setTimeout(()=>{V.value&&(X.value="加载超时，请检查网络连接或重试",V.value=!1)},2e4);s(()=>{a&&clearTimeout(a)})})(),A.value&&(()=>{const e=["正在连接实验服务器...","正在初始化实验环境...","正在加载实验资源...","即将完成加载..."];let a=0;G=setInterval(()=>{a=(a+1)%e.length,z.value=e[a]},3e3)})(),document.addEventListener("fullscreenchange",M),window.addEventListener("beforeunload",N),window.addEventListener("unload",O)});const N=e=>{const a="你有未保存的更改，确定要离开吗？";return e.preventDefault(),e.returnValue=a,a},O=()=>{B&&q.removeSession(B)};return s(()=>{G&&clearInterval(G),document.removeEventListener("fullscreenchange",M),window.removeEventListener("beforeunload",N),window.removeEventListener("unload",O)}),l(()=>{B&&q.removeSession(B)}),(e,a)=>{const r=m,n=h;return j(),o("div",L,[t("div",{class:_(["experiment-content",{fullscreen:c(Y)}])},[c(V)?(j(),o("div",b,[v(r,{class:"loading-icon"},{default:d(()=>[v(c(x))]),_:1}),a[0]||(a[0]=t("p",null,"正在加载实验环境...",-1)),t("p",C,f(c(z)),1)])):c(X)?(j(),o("div",S,[v(r,{class:"error-icon"},{default:d(()=>[v(c(E))]),_:1}),a[2]||(a[2]=t("h3",null,"加载失败",-1)),t("p",null,f(c(X)),1),t("div",D,[v(n,{onClick:K},{default:d(()=>a[1]||(a[1]=[w("返回")])),_:1,__:[1]})])])):c(A)?i("",!0):(j(),o("div",U,[v(r,{class:"error-icon"},{default:d(()=>[v(c(E))]),_:1}),a[4]||(a[4]=t("h3",null,"实验会话不存在",-1)),a[5]||(a[5]=t("p",null,"实验会话可能已过期或不存在，请重新开始实验。",-1)),t("div",Z,[v(n,{type:"primary",onClick:K},{default:d(()=>a[3]||(a[3]=[w("返回实验列表")])),_:1,__:[3]})])])),u((j(),o("iframe",{ref_key:"iframeRef",ref:$,key:c(R),src:c(R),class:"experiment-iframe",frameborder:"0",allowfullscreen:"",onLoad:H,onError:J},null,40,F)),[[p,!c(V)&&!c(X)&&c(A)]])],2)])}}}),[["__scopeId","data-v-0ba4d80a"]]);export{T as default};
