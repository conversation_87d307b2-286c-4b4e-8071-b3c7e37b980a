import{d as e,bv as l,r as a,I as t,o as d,e as s,h as o,k as u,i as r,aC as i,w as n,g as m,Q as p,R as y,a1 as c,F as v,f as b}from"./index.Ckm1SagX.js";import{a as f,E as V}from"./checkbox.CyAsOZKA.js";import{a as g,E as h}from"./radio.D_DAkhYS.js";/* empty css            */import{E as k,a as j}from"./select.DHkh6uhw.js";import"./scrollbar.6rbryiG1.js";import"./popper.DpZVcW1M.js";const x=e({__name:"index",props:{code:{type:String,required:!0},modelValue:{type:[String,Number,Array],required:!1},type:{type:String,default:"select",validator:e=>["select","radio","checkbox"].includes(e)},placeholder:{type:String,default:"请选择"},disabled:{type:Boolean,default:!1},style:{type:Object,default:()=>({width:"300px"})}},emits:["update:modelValue"],setup(e,{emit:x}){const _=l(),S=e,A=x,C=a([]),w=a("string"==typeof S.modelValue||"number"==typeof S.modelValue||Array.isArray(S.modelValue)?S.modelValue:void 0);function E(e){A("update:modelValue",e)}return t([()=>S.modelValue,()=>C.value],([e,l])=>{if(l.length>0&&void 0!==e)if("checkbox"===S.type)w.value=Array.isArray(e)?e:[];else{const a=l.find(l=>String(l.value)===String(e));w.value=null==a?void 0:a.value}else w.value=void 0},{immediate:!0}),d(async()=>{await _.loadDictItems(S.code),C.value=_.getDictItems(S.code)}),(l,a)=>{const t=j,d=k,x=h,_=g,S=V,A=f;return"select"===e.type?(b(),s(d,{key:0,modelValue:r(w),"onUpdate:modelValue":a[0]||(a[0]=e=>i(w)?w.value=e:null),placeholder:e.placeholder,disabled:e.disabled,clearable:"",style:u(e.style),onChange:E},{default:n(()=>[(b(!0),m(p,null,y(r(C),e=>(b(),s(t,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue","placeholder","disabled","style"])):"radio"===e.type?(b(),s(_,{key:1,modelValue:r(w),"onUpdate:modelValue":a[1]||(a[1]=e=>i(w)?w.value=e:null),disabled:e.disabled,style:u(e.style),onChange:E},{default:n(()=>[(b(!0),m(p,null,y(r(C),e=>(b(),s(x,{key:e.value,value:e.value},{default:n(()=>[c(v(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled","style"])):"checkbox"===e.type?(b(),s(A,{key:2,modelValue:r(w),"onUpdate:modelValue":a[2]||(a[2]=e=>i(w)?w.value=e:null),disabled:e.disabled,style:u(e.style),onChange:E},{default:n(()=>[(b(!0),m(p,null,y(r(C),e=>(b(),s(S,{key:e.value,value:e.value},{default:n(()=>[c(v(e.label),1)]),_:2},1032,["value"]))),128))]),_:1},8,["modelValue","disabled","style"])):o("",!0)}}});export{x as _};
