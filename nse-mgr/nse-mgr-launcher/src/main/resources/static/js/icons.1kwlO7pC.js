import{d as e,r as s,bu as t,g as o,f as i,m as r,w as a,C as n,Q as l,R as p,n as c,F as d,i as m,E as u,e as _,D as f}from"./index.Ckm1SagX.js";import{E as j,a as v}from"./tab-pane.BwxvTe__.js";import{_ as x}from"./index.vue_vue_type_script_setup_true_lang.B4vYqujV.js";/* empty css                */import{E as g}from"./popper.DpZVcW1M.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./strings.By8NVWWL.js";import"./aria.C1IWO_Rd.js";import"./event.BwRzfsZt.js";import"./index.DeprZc7T.js";import"./vnode.BkZiIFpS.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./index.BLy3nyPI.js";/* empty css               */import"./index.CbYeWxT8.js";import"./index.BRUQ9gWw.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";const b={class:"icons-container"},h={class:"grid"},k={class:"icon-item"},E={class:"grid"},I={class:"icon-item"},C=y(e({name:"Icons",inheritAttrs:!1,__name:"icons",setup(e){const y=["api","cascader","client","close","close_all","close_left","close_other","close_right","dict","document","download","drag","edit","exit-fullscreen","eye-open","eye","fullscreen","github","homepage","language","link","menu","message","money","monitor","order","password","peoples","perm","publish","role","security","size","skill","system","tree","user","uv","verify-code"],C=s(t);function w(e){return`<div class="i-svg:${e}" />`}function U(e){return`<el-icon><${e} /></el-icon>`}return(e,s)=>{const t=g,$=x,z=j,A=u,D=v;return i(),o("div",b,[r(D,{type:"border-card"},{default:a(()=>[r(z,{label:"Icons"},{default:a(()=>[n("div",h,[(i(),o(l,null,p(y,e=>n("div",{key:e},[r($,{text:w(e)},{default:a(()=>[r(t,{effect:"dark",content:w(e),placement:"top"},{default:a(()=>[n("div",k,[n("div",{class:c(`i-svg:${e}`)},null,2),n("span",null,d(e),1)])]),_:2},1032,["content"])]),_:2},1032,["text"])])),64))])]),_:1}),r(z,{label:"Element-UI Icons"},{default:a(()=>[n("div",E,[(i(!0),o(l,null,p(m(C),(e,s)=>(i(),o("div",{key:s},[r($,{text:U(s)},{default:a(()=>[r(t,{effect:"dark",content:U(s),placement:"top"},{default:a(()=>[n("div",I,[r(A,{size:20},{default:a(()=>[(i(),_(f(e)))]),_:2},1024),n("span",null,d(s),1)])]),_:2},1032,["content"])]),_:2},1032,["text"])]))),128))])]),_:1})]),_:1})])}}}),[["__scopeId","data-v-aadd67f9"]]);export{C as default};
