import{_ as o}from"./index.BUsM2o7g.js";import{d as r,r as s,g as i,f as t,m as e,w as p,a1 as m,aC as a,i as n}from"./index.Ckm1SagX.js";/* empty css             */import{E as j}from"./index.CMOQuMWt.js";import"./popper.DpZVcW1M.js";import"./index.BLy3nyPI.js";import"./_arrayPush.Dbwejsrt.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./aria.C1IWO_Rd.js";import"./index.Dh_vcBr5.js";import"./focus-trap.Bd_uzvDY.js";import"./use-form-common-props.BSYTvb6G.js";import"./popover.OzOh_E3J.js";import"./dropdown.Dp4e0zMH.js";import"./tab-pane.BwxvTe__.js";import"./strings.By8NVWWL.js";import"./event.BwRzfsZt.js";import"./index.DeprZc7T.js";import"./vnode.BkZiIFpS.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./scrollbar.6rbryiG1.js";/* empty css                *//* empty css              */import"./index.4JfkAhur.js";import"./index.DJHzyRe5.js";import"./index.Byj-i824.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BRUQ9gWw.js";const l={class:"app-container"},d=r({__name:"icon-selector",setup(r){const d=s("el-icon-edit");return(r,s)=>{const u=j,c=o;return t(),i("div",l,[e(u,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/icon-selector.vue",type:"primary",target:"_blank",class:"mb-10"},{default:p(()=>s[1]||(s[1]=[m(" 示例源码 请点击>>>> ")])),_:1,__:[1]}),e(c,{modelValue:n(d),"onUpdate:modelValue":s[0]||(s[0]=o=>a(d)?d.value=o:null)},null,8,["modelValue"])])}}});export{d as default};
