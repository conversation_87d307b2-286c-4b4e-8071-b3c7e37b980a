import{t as e,_ as l,d as a,A as t,b as s,r,c as o,a8 as i,u as n,ah as u,e as c,f as v,w as m,X as f,C as d,j as p,n as h,i as y,k as b,Z as g,T as w,H as S,g as z,m as x,Q as E,aV as _,z as L,J as H,I as T,y as k,V as C,bx as B,o as R,a2 as W,by as j,h as N,l as A,D as M,ac as $,ad as O,q}from"./index.Ckm1SagX.js";import{t as X}from"./aria.C1IWO_Rd.js";import{u as Y}from"./index.BLy3nyPI.js";const I={vertical:{offset:"offsetHeight",scroll:"scrollTop",scrollSize:"scrollHeight",size:"height",key:"vertical",axis:"Y",client:"clientY",direction:"top"},horizontal:{offset:"offsetWidth",scroll:"scrollLeft",scrollSize:"scrollWidth",size:"width",key:"horizontal",axis:"X",client:"clientX",direction:"left"}},K=Symbol("scrollbarContextKey"),P=e({vertical:Boolean,size:String,move:Number,ratio:{type:Number,required:!0},always:Boolean});var V=l(a({__name:"thumb",props:P,setup(e){const l=e,a=t(K),z=s("scrollbar");a||X("Thumb","can not inject scrollbar context");const x=r(),E=r(),_=r({}),L=r(!1);let H=!1,T=!1,k=0,C=0,B=S?document.onselectstart:null;const R=o(()=>I[l.vertical?"vertical":"horizontal"]),W=o(()=>(({move:e,size:l,bar:a})=>({[a.size]:l,transform:`translate${a.axis}(${e}%)`}))({size:l.size,move:l.move,bar:R.value})),j=o(()=>x.value[R.value.offset]**2/a.wrapElement[R.value.scrollSize]/l.ratio/E.value[R.value.offset]),N=e=>{var l;if(e.stopPropagation(),e.ctrlKey||[1,2].includes(e.button))return;null==(l=window.getSelection())||l.removeAllRanges(),M(e);const a=e.currentTarget;a&&(_.value[R.value.axis]=a[R.value.offset]-(e[R.value.client]-a.getBoundingClientRect()[R.value.direction]))},A=e=>{if(!E.value||!x.value||!a.wrapElement)return;const l=100*(Math.abs(e.target.getBoundingClientRect()[R.value.direction]-e[R.value.client])-E.value[R.value.offset]/2)*j.value/x.value[R.value.offset];a.wrapElement[R.value.scroll]=l*a.wrapElement[R.value.scrollSize]/100},M=e=>{e.stopImmediatePropagation(),H=!0,k=a.wrapElement.scrollHeight,C=a.wrapElement.scrollWidth,document.addEventListener("mousemove",$),document.addEventListener("mouseup",O),B=document.onselectstart,document.onselectstart=()=>!1},$=e=>{if(!x.value||!E.value)return;if(!1===H)return;const l=_.value[R.value.axis];if(!l)return;const t=100*(-1*(x.value.getBoundingClientRect()[R.value.direction]-e[R.value.client])-(E.value[R.value.offset]-l))*j.value/x.value[R.value.offset];"scrollLeft"===R.value.scroll?a.wrapElement[R.value.scroll]=t*C/100:a.wrapElement[R.value.scroll]=t*k/100},O=()=>{H=!1,_.value[R.value.axis]=0,document.removeEventListener("mousemove",$),document.removeEventListener("mouseup",O),q(),T&&(L.value=!1)};i(()=>{q(),document.removeEventListener("mouseup",O)});const q=()=>{document.onselectstart!==B&&(document.onselectstart=B)};return n(u(a,"scrollbarElement"),"mousemove",()=>{T=!1,L.value=!!l.size}),n(u(a,"scrollbarElement"),"mouseleave",()=>{T=!0,L.value=H}),(e,l)=>(v(),c(w,{name:y(z).b("fade"),persisted:""},{default:m(()=>[f(d("div",{ref_key:"instance",ref:x,class:h([y(z).e("bar"),y(z).is(y(R).key)]),onMousedown:A,onClick:p(()=>{},["stop"])},[d("div",{ref_key:"thumb",ref:E,class:h(y(z).e("thumb")),style:b(y(W)),onMousedown:N},null,38)],42,["onClick"]),[[g,e.always||L.value]])]),_:1},8,["name"]))}}),[["__file","thumb.vue"]]);var D=l(a({__name:"bar",props:e({always:{type:Boolean,default:!0},minSize:{type:Number,required:!0}}),setup(e,{expose:l}){const a=e,s=t(K),o=r(0),i=r(0),n=r(""),u=r(""),c=r(1),m=r(1);return l({handleScroll:e=>{if(e){const l=e.offsetHeight-4,a=e.offsetWidth-4;i.value=100*e.scrollTop/l*c.value,o.value=100*e.scrollLeft/a*m.value}},update:()=>{const e=null==s?void 0:s.wrapElement;if(!e)return;const l=e.offsetHeight-4,t=e.offsetWidth-4,r=l**2/e.scrollHeight,o=t**2/e.scrollWidth,i=Math.max(r,a.minSize),v=Math.max(o,a.minSize);c.value=r/(l-r)/(i/(l-i)),m.value=o/(t-o)/(v/(t-v)),u.value=i+4<l?`${i}px`:"",n.value=v+4<t?`${v}px`:""}}),(e,l)=>(v(),z(E,null,[x(V,{move:o.value,ratio:m.value,size:n.value,always:e.always},null,8,["move","ratio","size","always"]),x(V,{move:i.value,ratio:c.value,size:u.value,vertical:"",always:e.always},null,8,["move","ratio","size","always"])],64))}}),[["__file","bar.vue"]]);const J=e({height:{type:[String,Number],default:""},maxHeight:{type:[String,Number],default:""},native:Boolean,wrapStyle:{type:L([String,Object,Array]),default:""},wrapClass:{type:[String,Array],default:""},viewClass:{type:[String,Array],default:""},viewStyle:{type:[String,Array,Object],default:""},noresize:Boolean,tag:{type:String,default:"div"},always:Boolean,minSize:{type:Number,default:20},tabindex:{type:[String,Number],default:void 0},id:String,role:String,...Y(["ariaLabel","ariaOrientation"])}),Q={"end-reached":e=>["left","right","top","bottom"].includes(e),scroll:({scrollTop:e,scrollLeft:l})=>[e,l].every(_)},Z=a({name:"ElScrollbar"});const F=q(l(a({...Z,props:J,emits:Q,setup(e,{expose:l,emit:a}){const t=e,i=s("scrollbar");let u,f,p=0,g=0,w="";const S=r(),x=r(),E=r(),L=r(),q=o(()=>{const e={};return t.height&&(e.height=H(t.height)),t.maxHeight&&(e.maxHeight=H(t.maxHeight)),[t.wrapStyle,e]}),X=o(()=>[t.wrapClass,i.e("wrap"),{[i.em("wrap","hidden-default")]:!t.native}]),Y=o(()=>[i.e("view"),t.viewClass]),I=()=>{var e;if(x.value){null==(e=L.value)||e.handleScroll(x.value);const l=p,t=g;p=x.value.scrollTop,g=x.value.scrollLeft;const s={bottom:p+x.value.clientHeight>=x.value.scrollHeight,top:p<=0&&0!==l,right:g+x.value.clientWidth>=x.value.scrollWidth&&t!==g,left:g<=0&&0!==t};l!==p&&(w=p>l?"bottom":"top"),t!==g&&(w=g>t?"right":"left"),a("scroll",{scrollTop:p,scrollLeft:g}),s[w]&&a("end-reached",w)}};const P=()=>{var e;null==(e=L.value)||e.update()};return T(()=>t.noresize,e=>{e?(null==u||u(),null==f||f()):(({stop:u}=O(E,P)),f=n("resize",P))},{immediate:!0}),T(()=>[t.maxHeight,t.height],()=>{t.native||W(()=>{var e;P(),x.value&&(null==(e=L.value)||e.handleScroll(x.value))})}),k(K,C({scrollbarElement:S,wrapElement:x})),B(()=>{x.value&&(x.value.scrollTop=p,x.value.scrollLeft=g)}),R(()=>{t.native||W(()=>{P()})}),j(()=>P()),l({wrapRef:x,update:P,scrollTo:function(e,l){$(e)?x.value.scrollTo(e):_(e)&&_(l)&&x.value.scrollTo(e,l)},setScrollTop:e=>{_(e)&&(x.value.scrollTop=e)},setScrollLeft:e=>{_(e)&&(x.value.scrollLeft=e)},handleScroll:I}),(e,l)=>(v(),z("div",{ref_key:"scrollbarRef",ref:S,class:h(y(i).b())},[d("div",{ref_key:"wrapRef",ref:x,class:h(y(X)),style:b(y(q)),tabindex:e.tabindex,onScroll:I},[(v(),c(M(e.tag),{id:e.id,ref_key:"resizeRef",ref:E,class:h(y(Y)),style:b(e.viewStyle),role:e.role,"aria-label":e.ariaLabel,"aria-orientation":e.ariaOrientation},{default:m(()=>[A(e.$slots,"default")]),_:3},8,["id","class","style","role","aria-label","aria-orientation"]))],46,["tabindex"]),e.native?N("v-if",!0):(v(),c(D,{key:0,ref_key:"barRef",ref:L,always:e.always,"min-size":e.minSize},null,8,["always","min-size"]))],2))}}),[["__file","scrollbar.vue"]]));export{F as E,Q as s};
