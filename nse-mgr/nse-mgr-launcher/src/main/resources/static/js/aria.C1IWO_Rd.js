class e extends Error{constructor(e){super(e),this.name="ElementPlusError"}}function t(t,n){throw new e(`[${t}] ${n}`)}function n(e,t){}const r=e=>Array.from(e.querySelectorAll('a[href],button:not([disabled]),button:not([hidden]),:not([tabindex="-1"]),input:not([disabled]),input:not([type="hidden"]),select:not([disabled]),textarea:not([disabled])')).filter(e=>a(e)&&(e=>"fixed"!==getComputedStyle(e).position&&null!==e.offsetParent)(e)),a=e=>{if(e.tabIndex>0||0===e.tabIndex&&null!==e.getAttribute("tabIndex"))return!0;if(e.tabIndex<0||e.hasAttribute("disabled")||"true"===e.getAttribute("aria-disabled"))return!1;switch(e.nodeName){case"A":return!!e.href&&"ignore"!==e.rel;case"INPUT":return!("hidden"===e.type||"file"===e.type);case"BUTTON":case"SELECT":case"TEXTAREA":return!0;default:return!1}},s=function(e,t,...n){let r;r=t.includes("mouse")||t.includes("click")?"MouseEvents":t.includes("key")?"KeyboardEvent":"HTMLEvents";const a=document.createEvent(r);return a.initEvent(t,...n),e.dispatchEvent(a),e},i=e=>!e.getAttribute("aria-owns"),o=(e,t,n)=>{const{parentNode:r}=e;if(!r)return null;const a=r.querySelectorAll(n);return a[Array.prototype.indexOf.call(a,e)+t]||null},u=e=>{e&&(e.focus(),!i(e)&&e.click())};export{s as a,a as b,n as d,u as f,o as g,i,r as o,t};
