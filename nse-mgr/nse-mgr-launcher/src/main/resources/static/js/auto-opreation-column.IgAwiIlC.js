import{d as e,r as a,c as l,e as t,f as o,w as s,X as i,g as r,l as n,i as m,m as p,C as d,a1 as u,aC as c,h as f}from"./index.Ckm1SagX.js";import{a as y,E as g}from"./table-column.DQa6-hu-.js";import{E as _}from"./checkbox.CyAsOZKA.js";/* empty css                */import"./popper.DpZVcW1M.js";import"./scrollbar.6rbryiG1.js";/* empty css               *//* empty css            *//* empty css             */import{E as b}from"./index.CMOQuMWt.js";import{E as j}from"./index.CbYeWxT8.js";import"./aria.C1IWO_Rd.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./index.BLy3nyPI.js";import"./_baseIteratee.PZHdcgYb.js";import"./isEqual.CZKKciWh.js";import"./castArray.Chmjnshw.js";import"./debounce.YgIwzEIs.js";import"./index.B0geSHq7.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./event.BwRzfsZt.js";import"./index.BRUQ9gWw.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";const v={class:"operation-buttons"},h=e({__name:"index",props:{listDataLength:{},prop:{},label:{default:"操作"},fixed:{default:"right"},align:{default:"center"},width:{},showOverflowTooltip:{type:Boolean},minWidth:{default:80}},setup(e){const p=e,d=a(0),u=a(p.minWidth||80),c=()=>{if(d.value++,d.value!==p.listDataLength)return;const e=_();u.value=Math.max(e,p.minWidth),d.value=0},f=l(()=>p.width||u.value||p.minWidth),g={mounted(){c()},updated(){c()}},_=()=>{const e=document.getElementsByClassName("operation-buttons");let a=0,l=0;return Array.prototype.forEach.call(e,e=>{const t=e.querySelectorAll(".el-button");l=Array.from(t).reduce((e,a)=>e+a.scrollWidth+22,0),l>a&&(a=l)}),a};return(e,a)=>{const l=y;return o(),t(l,{label:e.label,fixed:e.fixed,align:e.align,"show-overflow-tooltip":e.showOverflowTooltip,width:m(f)},{default:s(({row:a})=>[i((o(),r("div",v,[n(e.$slots,"default",{row:a})])),[[g]])]),_:3},8,["label","fixed","align","show-overflow-tooltip","width"])}}}),x={class:"app-container"},w={class:"mt-30px"},A=e({__name:"auto-opreation-column",setup(e){const l=a(!0),i=a(!1),n=a(!1),v=a(!1),A=a(!1),z=a([]);return setTimeout(()=>{z.value=[{date:"2016-05-03",name:"Tom",state:"California",city:"Los Angeles",address:"No. 189, Grove St, Los Angeles",zip:"CA 90036",tag:"Home"},{date:"2016-05-02",name:"Tom",state:"California",city:"Los Angeles",address:"No. 189, Grove St, Los Angeles",zip:"CA 90036",tag:"Office"},{date:"2016-05-04",name:"Tom",state:"California",city:"Los Angeles",address:"No. 189, Grove St, Los Angeles",zip:"CA 90036",tag:"Home"},{date:"2016-05-01",name:"Tom",state:"California",city:"Los Angeles",address:"No. 189, Grove St, Los Angeles",zip:"CA 90036",tag:"Office"}]},300),(e,a)=>{const V=b,k=_,C=y,L=j,E=g;return o(),r("div",x,[p(V,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/auto-opreation-column.vue",type:"primary",target:"_blank",class:"mb-10"},{default:s(()=>a[5]||(a[5]=[u(" 示例源码 请点击>>>> ")])),_:1,__:[5]}),d("div",null,[a[12]||(a[12]=d("h3",null,"自适应表格操作列",-1)),a[13]||(a[13]=d("div",{class:"text-14px color-#999"}," 该组件适用于含有操作列的表格。在某些情况下，按钮可能需要根据数据状态或其他条件动态展示，无法预设固定宽度。操作列组件能根据按钮数量自适应宽度，不需要再手动设置宽度。 ",-1)),d("div",w,[p(k,{modelValue:m(l),"onUpdate:modelValue":a[0]||(a[0]=e=>c(l)?l.value=e:null),label:"查看",size:"large"},null,8,["modelValue"]),p(k,{modelValue:m(i),"onUpdate:modelValue":a[1]||(a[1]=e=>c(i)?i.value=e:null),label:"超过了六个字会怎么样",size:"large"},null,8,["modelValue"]),p(k,{modelValue:m(n),"onUpdate:modelValue":a[2]||(a[2]=e=>c(n)?n.value=e:null),label:"新增",size:"large"},null,8,["modelValue"]),p(k,{modelValue:m(v),"onUpdate:modelValue":a[3]||(a[3]=e=>c(v)?v.value=e:null),label:"返回很多个字",size:"large"},null,8,["modelValue"]),p(k,{modelValue:m(A),"onUpdate:modelValue":a[4]||(a[4]=e=>c(A)?A.value=e:null),label:"编辑",size:"large"},null,8,["modelValue"])]),p(E,{data:m(z),style:{width:"100%"},border:""},{default:s(()=>[p(C,{prop:"date",label:"Date"}),p(C,{prop:"name",label:"Name"}),p(C,{prop:"state",label:"State"}),p(C,{prop:"city",label:"City"}),p(C,{prop:"address",label:"Address"}),p(C,{prop:"zip",label:"Zip"}),p(h,{"list-data-length":m(z).length},{default:s(({row:e})=>[m(l)?(o(),t(L,{key:0,link:"",type:"primary",size:"small"},{default:s(()=>a[6]||(a[6]=[u("查看")])),_:1,__:[6]})):f("",!0),m(i)?(o(),t(L,{key:1,link:"",type:"primary",size:"small"},{default:s(()=>a[7]||(a[7]=[u(" 超过了六个字会怎么样 ")])),_:1,__:[7]})):f("",!0),m(n)?(o(),t(L,{key:2,link:"",type:"primary",size:"small"},{default:s(()=>a[8]||(a[8]=[u("新增")])),_:1,__:[8]})):f("",!0),m(v)?(o(),t(L,{key:3,link:"",type:"primary",size:"small"},{default:s(()=>a[9]||(a[9]=[u("返回很多个字")])),_:1,__:[9]})):f("",!0),m(A)?(o(),t(L,{key:4,link:"",type:"primary",size:"small"},{default:s(()=>a[10]||(a[10]=[u("编辑")])),_:1,__:[10]})):f("",!0),"Home"===e.tag?(o(),t(L,{key:5,link:"",type:"primary",size:"small"},{default:s(()=>a[11]||(a[11]=[u("默认")])),_:1,__:[11]})):f("",!0)]),_:1},8,["list-data-length"])]),_:1},8,["data"])])])}}});export{A as default};
