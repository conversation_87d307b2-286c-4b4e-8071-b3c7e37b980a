import{d as e,ai as a,r as l,V as t,c as o,ao as i,I as r,o as s,ap as n,g as d,f as u,C as m,m as p,w as c,a0 as f,i as _,a1 as v,X as j,e as b,h as g,aC as h,F as y,E as k,aP as x,di as w}from"./index.Ckm1SagX.js";/* empty css                */import{E as V}from"./drawer.CQbqc65D.js";import"./overlay.CXfNA60T.js";import{E as C}from"./tree.BMRqnaD4.js";import{E}from"./checkbox.CyAsOZKA.js";/* empty css             *//* empty css                */import{E as U}from"./popper.DpZVcW1M.js";import{E as z}from"./dialog.TtqHlFhB.js";import{E as R}from"./card.BfhlXze7.js";import{_ as F}from"./index.r55iLQne.js";import{E as S,a as q}from"./table-column.DQa6-hu-.js";import"./scrollbar.6rbryiG1.js";/* empty css            */import{a as N,E as O}from"./form-item.CUMILu98.js";/* empty css               *//* empty css              */import{R as P}from"./role.api.BGSj26C6.js";/* empty css                    */import{E as T}from"./index.4JfkAhur.js";import{E as I}from"./index.CbYeWxT8.js";import{v as A}from"./directive.C7vihscI.js";import{E as B}from"./index.KtapGdwl.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./aria.C1IWO_Rd.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./index.BRUQ9gWw.js";import"./event.BwRzfsZt.js";import"./index.Dh_vcBr5.js";import"./token.DWNpOE8r.js";import"./index.Cg5eTZHL.js";import"./index.CqmGTqol.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.BLy3nyPI.js";import"./_arrayPush.Dbwejsrt.js";import"./isEqual.CZKKciWh.js";import"./_Uint8Array.BCiDNJWl.js";import"./index.Cn1QDWeG.js";import"./refs.biN0GvkM.js";import"./pagination.TL6aFrlm.js";import"./select.DHkh6uhw.js";import"./index.BPj3iklg.js";import"./strings.By8NVWWL.js";import"./castArray.Chmjnshw.js";import"./index.Byj-i824.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.B0geSHq7.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./_baseClone.ByRc02qR.js";import"./index.DJHzyRe5.js";const M={class:"app-container"},D={class:"search-container"},K={class:"data-table__toolbar"},X={class:"data-table__toolbar--actions"},G={class:"dialog-footer"},H={class:"flex-x-between"},J={class:"flex-center ml-5"},L={class:"dialog-footer"},Q=e({name:"Role",inheritAttrs:!1,__name:"index",setup(e){const Q=a(),W=l(),Y=l(),Z=l(),$=l(!1),ee=l([]),ae=l(0),le=t({pageNumber:1,pageSize:10}),te=l(),oe=l([]),ie=t({title:"",visible:!1}),re=o(()=>Q.device===i.DESKTOP?"600px":"90%"),se=t({sort:1,status:1}),ne=t({name:[{required:!0,message:"请输入角色名称",trigger:"blur"}],level:[{required:!0,message:"请输入角色等级",trigger:"blur"}],remark:[{required:!0,message:"请选择角色备注",trigger:"blur"}]}),de=l({}),ue=l(!1),me=l(""),pe=l(!0),ce=l(!0);function fe(){$.value=!0,P.getPage(le).then(e=>{te.value=e.rows,ae.value=e.total}).finally(()=>{$.value=!1})}function _e(){le.pageNumber=1,fe()}function ve(){W.value.resetFields(),le.pageNumber=1,fe()}function je(e){ee.value=e.map(e=>e.id)}function be(e){ie.visible=!0,e?(ie.title="修改角色",P.getFormData(e).then(e=>{Object.assign(se,e)})):ie.title="新增角色"}function ge(){Y.value.validate(e=>{if(e){$.value=!0;const e=se.id;e?P.update(e,se).then(()=>{x.success("修改成功"),he(),ve()}).finally(()=>$.value=!1):P.create(se).then(()=>{x.success("新增成功"),he(),ve()}).finally(()=>$.value=!1)}})}function he(){ie.visible=!1,Y.value.resetFields(),Y.value.clearValidate(),se.id=void 0,se.sort=1,se.status=1}function ye(e){const a=[e||ee.value].join(",");a?B.confirm("确认删除已选中的数据项?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(()=>{$.value=!0,P.deleteByIds(a).then(()=>{x.success("删除成功"),ve()}).finally(()=>$.value=!1)},()=>{x.info("已取消删除")}):x.warning("请勾选删除项")}function ke(){const e=de.value.id;if(e){const a=Z.value.getCheckedNodes(!1,!0).map(e=>e.value);$.value=!0,P.updateRoleMenus(e,a).then(()=>{x.success("分配权限成功"),ue.value=!1,ve()}).finally(()=>{$.value=!1})}}function xe(){pe.value=!pe.value,Z.value&&Object.values(Z.value.store.nodesMap).forEach(e=>{pe.value?e.expand():e.collapse()})}function we(e,a){return!e||a.label.includes(e)}function Ve(e){ce.value=e}return r(me,e=>{Z.value.filter(e)}),s(()=>{_e()}),(e,a)=>{const l=T,t=N,o=I,i=O,r=q,s=S,B=F,Q=R,Ce=z,Ee=n("Search"),Ue=n("Switch"),ze=E,Re=n("QuestionFilled"),Fe=k,Se=U,qe=C,Ne=V,Oe=A;return u(),d("div",M,[m("div",D,[p(i,{ref_key:"queryFormRef",ref:W,model:_(le),inline:!0},{default:c(()=>[p(t,{prop:"keywords",label:"关键字"},{default:c(()=>[p(l,{modelValue:_(le).keywords,"onUpdate:modelValue":a[0]||(a[0]=e=>_(le).keywords=e),placeholder:"角色名称",clearable:"",onKeyup:f(_e,["enter"])},null,8,["modelValue"])]),_:1}),p(t,{class:"search-buttons"},{default:c(()=>[p(o,{type:"primary",icon:"search",onClick:_e},{default:c(()=>a[14]||(a[14]=[v("搜索")])),_:1,__:[14]}),p(o,{icon:"refresh",onClick:ve},{default:c(()=>a[15]||(a[15]=[v("重置")])),_:1,__:[15]})]),_:1})]),_:1},8,["model"])]),p(Q,{shadow:"hover",class:"data-table"},{default:c(()=>[m("div",K,[m("div",X,[p(o,{type:"success",icon:"plus",onClick:a[1]||(a[1]=e=>be())},{default:c(()=>a[16]||(a[16]=[v("新增")])),_:1,__:[16]}),p(o,{type:"danger",disabled:0===_(ee).length,icon:"delete",onClick:a[2]||(a[2]=e=>ye())},{default:c(()=>a[17]||(a[17]=[v(" 删除 ")])),_:1,__:[17]},8,["disabled"])])]),j((u(),b(s,{ref:"dataTableRef",data:_(te),"highlight-current-row":"",border:"",class:"data-table__content",onSelectionChange:je},{default:c(()=>[p(r,{type:"selection",width:"55",align:"center"}),p(r,{label:"角色名称",prop:"name","min-width":"100"}),p(r,{label:"角色等级",prop:"level",width:"150"}),p(r,{label:"角色备注",prop:"remark",width:"150"}),p(r,{fixed:"right",label:"操作",width:"220"},{default:c(e=>[p(o,{type:"primary",size:"small",link:"",icon:"position",onClick:a=>async function(e){if("super_admin"===e.name)return void x.warning("超级管理员默认最高权限，无需分配");const a=e.id;a&&(ue.value=!0,$.value=!0,de.value.id=a,de.value.name=e.name,oe.value=await w.getOptions(),P.getRoleMenuIds(a).then(e=>{e.forEach(e=>Z.value.setChecked(e,!0,!1))}).finally(()=>{$.value=!1}))}(e.row)},{default:c(()=>a[18]||(a[18]=[v(" 分配权限 ")])),_:2,__:[18]},1032,["onClick"]),p(o,{type:"primary",size:"small",link:"",icon:"edit",onClick:a=>be(e.row.id)},{default:c(()=>a[19]||(a[19]=[v(" 编辑 ")])),_:2,__:[19]},1032,["onClick"]),p(o,{type:"danger",size:"small",link:"",icon:"delete",onClick:a=>ye(e.row.id)},{default:c(()=>a[20]||(a[20]=[v(" 删除 ")])),_:2,__:[20]},1032,["onClick"])]),_:1})]),_:1},8,["data"])),[[Oe,_($)]]),_(ae)>0?(u(),b(B,{key:0,total:_(ae),"onUpdate:total":a[3]||(a[3]=e=>h(ae)?ae.value=e:null),page:_(le).pageNumber,"onUpdate:page":a[4]||(a[4]=e=>_(le).pageNumber=e),limit:_(le).pageSize,"onUpdate:limit":a[5]||(a[5]=e=>_(le).pageSize=e),onPagination:fe},null,8,["total","page","limit"])):g("",!0)]),_:1}),p(Ce,{modelValue:_(ie).visible,"onUpdate:modelValue":a[9]||(a[9]=e=>_(ie).visible=e),title:_(ie).title,width:"500px",onClose:he},{footer:c(()=>[m("div",G,[p(o,{type:"primary",onClick:ge},{default:c(()=>a[21]||(a[21]=[v("确 定")])),_:1,__:[21]}),p(o,{onClick:he},{default:c(()=>a[22]||(a[22]=[v("取 消")])),_:1,__:[22]})])]),default:c(()=>[p(i,{ref_key:"roleFormRef",ref:Y,model:_(se),rules:_(ne),"label-width":"100px"},{default:c(()=>[p(t,{label:"角色名称",prop:"name"},{default:c(()=>[p(l,{modelValue:_(se).name,"onUpdate:modelValue":a[6]||(a[6]=e=>_(se).name=e),disabled:_(se).id,placeholder:"请输入角色名称"},null,8,["modelValue","disabled"])]),_:1}),p(t,{label:"角色等级",prop:"level"},{default:c(()=>[p(l,{modelValue:_(se).level,"onUpdate:modelValue":a[7]||(a[7]=e=>_(se).level=e),placeholder:"请输入角色等级"},null,8,["modelValue"])]),_:1}),p(t,{label:"角色备注",prop:"remark"},{default:c(()=>[p(l,{modelValue:_(se).remark,"onUpdate:modelValue":a[8]||(a[8]=e=>_(se).remark=e),placeholder:"请输入角色备注"},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title"]),p(Ne,{modelValue:_(ue),"onUpdate:modelValue":a[13]||(a[13]=e=>h(ue)?ue.value=e:null),title:"【"+_(de).name+"】权限分配",size:_(re)},{footer:c(()=>[m("div",L,[p(o,{type:"primary",onClick:ke},{default:c(()=>a[25]||(a[25]=[v("确 定")])),_:1,__:[25]}),p(o,{onClick:a[12]||(a[12]=e=>ue.value=!1)},{default:c(()=>a[26]||(a[26]=[v("取 消")])),_:1,__:[26]})])]),default:c(()=>[m("div",H,[p(l,{modelValue:_(me),"onUpdate:modelValue":a[10]||(a[10]=e=>h(me)?me.value=e:null),clearable:"",class:"w-[150px]",placeholder:"菜单权限名称"},{prefix:c(()=>[p(Ee)]),_:1},8,["modelValue"]),m("div",J,[p(o,{type:"primary",size:"small",plain:"",onClick:xe},{icon:c(()=>[p(Ue)]),default:c(()=>[v(" "+y(_(pe)?"收缩":"展开"),1)]),_:1}),p(ze,{modelValue:_(ce),"onUpdate:modelValue":a[11]||(a[11]=e=>h(ce)?ce.value=e:null),class:"ml-5",onChange:Ve},{default:c(()=>a[23]||(a[23]=[v(" 父子联动 ")])),_:1,__:[23]},8,["modelValue"]),p(Se,{placement:"bottom"},{content:c(()=>a[24]||(a[24]=[v(" 如果只需勾选菜单权限，不需要勾选子菜单或者按钮权限，请关闭父子联动 ")])),default:c(()=>[p(Fe,{class:"ml-1 color-[--el-color-primary] inline-block cursor-pointer"},{default:c(()=>[p(Re)]),_:1})]),_:1})])]),p(qe,{ref_key:"permTreeRef",ref:Z,"node-key":"value","show-checkbox":"",data:_(oe),"filter-node-method":we,"default-expand-all":!0,"check-strictly":!_(ce),class:"mt-5"},{default:c(({data:e})=>[v(y(e.label),1)]),_:1},8,["data","check-strictly"])]),_:1},8,["modelValue","title","size"])])}}});export{Q as default};
