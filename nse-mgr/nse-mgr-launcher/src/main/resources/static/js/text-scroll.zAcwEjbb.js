import{d as e,r as t,bw as l,c as a,o as s,aY as r,I as i,ap as n,g as o,f as c,n as u,C as d,h as p,m as v,E as y,w as f,k as w,i as m}from"./index.Ckm1SagX.js";import{_ as x}from"./_plugin-vue_export-helper.BCo6x5W8.js";const _=["typewriter"],g={class:"left-icon"},h={class:"scroll-wrapper"},T=["innerHTML"],C=["innerHTML"],L=x(e({__name:"index",props:{text:{},speed:{default:70},direction:{default:"left"},type:{default:"default"},showClose:{type:Boolean,default:!1},typewriter:{type:Boolean,default:!1},typewriterSpeed:{default:100}},emits:["close"],setup(e,{emit:x}){const L=x,k=e,H=t(null),M=l(H),B=t(null),E=t(0),I=t("");let b=null;const j=t(!1),z=a(()=>k.typewriter?!M.value&&j.value:!M.value),S=a(()=>k.typewriter?I.value:k.text),$=a(()=>({"--animation-duration":`${E.value}s`,"--animation-play-state":z.value?"running":"paused","--animation-direction":"left"===k.direction?"normal":"reverse"})),R=()=>{if(B.value){const e=B.value.scrollWidth/2;E.value=e/k.speed}},W=()=>{L("close"),H.value&&H.value.remove()},Y=()=>{let e=0;I.value="",j.value=!1;const t=()=>{e<k.text.length?(I.value+=k.text[e],e++,b=setTimeout(t,k.typewriterSpeed)):j.value=!0};t()};return s(()=>{R(),window.addEventListener("resize",R),k.typewriter&&Y()}),r(()=>{window.removeEventListener("resize",R),b&&clearTimeout(b)}),i(()=>k.text,()=>{k.typewriter&&(b&&clearTimeout(b),Y())}),(e,t)=>{const l=n("Bell"),a=y,s=n("Close");return c(),o("div",{ref_key:"containerRef",ref:H,class:u(["text-scroll-container",[`text-scroll--${k.type}`]]),typewriter:k.typewriter?"true":void 0},[d("div",g,[v(a,null,{default:f(()=>[v(l)]),_:1})]),d("div",h,[d("div",{ref_key:"scrollContent",ref:B,class:u(["text-scroll-content",{scrolling:m(z)}]),style:w(m($))},[d("div",{class:"scroll-item",innerHTML:m(S)},null,8,T),d("div",{class:"scroll-item",innerHTML:m(S)},null,8,C)],6)]),e.showClose?(c(),o("div",{key:0,class:"right-icon",onClick:W},[v(a,null,{default:f(()=>[v(s)]),_:1})])):p("",!0)],10,_)}}}),[["__scopeId","data-v-72832969"]]),k={class:"app-container"},H=x(e({__name:"text-scroll",setup:e=>(e,t)=>(c(),o("div",k,[v(L,{text:"这是一条基础的滚动公告，默认向左滚动。",typewriter:""}),v(L,{type:"success",text:"这是一条成功类型的滚动公告",typewriter:""}),v(L,{type:"warning",text:"这是一条警告类型的滚动公告"}),v(L,{type:"danger",text:"这是一条危险类型的滚动公告"}),v(L,{type:"info",text:"这是一条信息类型的滚动公告"}),v(L,{text:"这是一条速度较慢、向右滚动的公告",speed:30,direction:"right","show-close":""})]))}),[["__scopeId","data-v-facbd393"]]);export{H as default};
