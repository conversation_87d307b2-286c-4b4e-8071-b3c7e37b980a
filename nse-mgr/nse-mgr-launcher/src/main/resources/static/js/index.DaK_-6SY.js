import{d as e,ai as a,r as l,V as t,c as o,ao as r,o as s,aQ as i,g as d,f as n,m as p,w as u,i as m,C as c,a0 as f,a1 as _,X as b,e as g,h as j,F as v,aC as h,Q as y,R as V,b3 as w,aA as x,aP as k}from"./index.Ckm1SagX.js";/* empty css                */import{E as U}from"./drawer.CQbqc65D.js";import"./overlay.CXfNA60T.js";import{E as C}from"./switch.TMg_el83.js";import{_ as E}from"./index.vue_vue_type_script_setup_true_lang.8dFNY07J.js";/* empty css            */import{E as I,a as z}from"./select.DHkh6uhw.js";import"./scrollbar.6rbryiG1.js";import"./popper.DpZVcW1M.js";import"./tree.BMRqnaD4.js";import"./checkbox.CyAsOZKA.js";/* empty css             */import{E as R}from"./tree-select.CTL8lHB1.js";import{a as T,E as A}from"./col.Bi-hDQ18.js";import{E as D}from"./card.BfhlXze7.js";import{_ as O}from"./index.r55iLQne.js";import{E as P,a as q}from"./table-column.DQa6-hu-.js";/* empty css                */import{_ as B}from"./DictLabel.vue_vue_type_script_setup_true_lang.BOw0aI_B.js";import{E as F,a as L}from"./form-item.CUMILu98.js";/* empty css               */import{E as N}from"./date-picker.CB50TImD.js";/* empty css              */import{D as S}from"./dept.api.CHEkLPxS.js";import{R as Y}from"./role.api.BGSj26C6.js";import{_ as Z}from"./DeptTree.vue_vue_type_script_setup_true_lang.DdIWNsUV.js";import{_ as K}from"./UserImport.vue_vue_type_script_setup_true_lang.BiJQsr0o.js";/* empty css                    */import{E as M}from"./index.4JfkAhur.js";import{E as X}from"./index.CbYeWxT8.js";import{E as Q}from"./index.BPj3iklg.js";import{v as $}from"./directive.C7vihscI.js";import{E as G}from"./index.KtapGdwl.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./aria.C1IWO_Rd.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./index.BRUQ9gWw.js";import"./event.BwRzfsZt.js";import"./index.Dh_vcBr5.js";import"./index.BLy3nyPI.js";import"./_arrayPush.Dbwejsrt.js";import"./use-form-common-props.BSYTvb6G.js";import"./radio.D_DAkhYS.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./castArray.Chmjnshw.js";import"./isEqual.CZKKciWh.js";import"./_Uint8Array.BCiDNJWl.js";import"./index.Byj-i824.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.B0geSHq7.js";import"./index.Cn1QDWeG.js";import"./index.Cg5eTZHL.js";import"./index.CqmGTqol.js";import"./pagination.TL6aFrlm.js";import"./_plugin-vue_export-helper.BCo6x5W8.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./_baseClone.ByRc02qR.js";import"./index.COAWgEf6.js";import"./index.DJHzyRe5.js";import"./alert.DMAbw9T1.js";import"./dialog.TtqHlFhB.js";import"./refs.biN0GvkM.js";import"./upload.CnbKCtkP.js";import"./progress.ChnKapv7.js";/* empty css             */import"./index.CMOQuMWt.js";const H={class:"app-container"},J={class:"search-container"},W={class:"data-table__toolbar"},ee={class:"data-table__toolbar--actions"},ae={class:"data-table__toolbar--tools"},le={class:"dialog-footer"},te=e({name:"User",inheritAttrs:!1,__name:"index",setup(e){const te=a(),oe=l(),re=l(),se=t({pageNumber:1,pageSize:10}),ie=l(),de=l(0),ne=l(!1),pe=t({visible:!1,title:"新增用户"}),ue=o(()=>te.device===r.DESKTOP?"600px":"90%"),me=t({status:1}),ce=t({username:[{required:!0,message:"用户名不能为空",trigger:"blur"}],nickname:[{required:!0,message:"用户昵称不能为空",trigger:"blur"}],deptId:[{required:!0,message:"所属部门不能为空",trigger:"blur"}],roleIds:[{required:!0,message:"用户角色不能为空",trigger:"blur"}],email:[{pattern:/\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/,message:"请输入正确的邮箱地址",trigger:"blur"}],mobile:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}]}),fe=l([]),_e=l(),be=l(),ge=l(!1);async function je(){ne.value=!0;try{const e=await x.getPage(se);ie.value=e.rows,de.value=e.total}finally{ne.value=!1}}function ve(){se.pageNumber=1,je()}function he(){oe.value.resetFields(),se.pageNumber=1,se.deptId=void 0,se.createTime=void 0,je()}function ye(e){fe.value=e.map(e=>e.id)}async function Ve(e){pe.visible=!0,be.value=await Y.getOptions(),_e.value=await S.getOptions(),e?(pe.title="修改用户",x.getFormData(e).then(e=>{Object.assign(me,{...e})})):pe.title="新增用户"}function we(){pe.visible=!1,re.value.resetFields(),re.value.clearValidate(),me.id=void 0,me.status=1}const xe=w(()=>{re.value.validate(e=>{if(e){const e=me.id;ne.value=!0,e?x.update(e,me).then(()=>{k.success("修改用户成功"),we(),he()}).finally(()=>ne.value=!1):x.create(me).then(()=>{k.success("新增用户成功"),we(),he()}).finally(()=>ne.value=!1)}})},1e3);function ke(e){const a=[e||fe.value].join(",");a?G.confirm("确认删除用户?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(function(){ne.value=!0,x.deleteByIds(a).then(()=>{k.success("删除成功"),he()}).finally(()=>ne.value=!1)},function(){k.info("已取消删除")}):k.warning("请勾选删除项")}function Ue(){ge.value=!0}function Ce(){x.export(se).then(e=>{const a=e.data,l=decodeURI(e.headers["content-disposition"].split(";")[1].split("=")[1]),t=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"}),o=window.URL.createObjectURL(t),r=document.createElement("a");r.href=o,r.download=l,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(o)})}return s(()=>{ve()}),(e,a)=>{const l=T,t=M,o=L,r=z,s=I,w=N,S=X,Y=F,te=q,Ee=B,Ie=Q,ze=P,Re=O,Te=D,Ae=A,De=R,Oe=E,Pe=C,qe=U,Be=i("hasPerm"),Fe=$;return n(),d("div",H,[p(Ae,{gutter:20},{default:u(()=>[p(l,{lg:4,xs:24,class:"mb-[12px]"},{default:u(()=>[p(Z,{modelValue:m(se).deptId,"onUpdate:modelValue":a[0]||(a[0]=e=>m(se).deptId=e),onNodeClick:ve},null,8,["modelValue"])]),_:1}),p(l,{lg:20,xs:24},{default:u(()=>[c("div",J,[p(Y,{ref_key:"queryFormRef",ref:oe,model:m(se),inline:!0,"label-width":"auto"},{default:u(()=>[p(o,{label:"关键字",prop:"keywords"},{default:u(()=>[p(t,{modelValue:m(se).keywords,"onUpdate:modelValue":a[1]||(a[1]=e=>m(se).keywords=e),placeholder:"用户名/昵称/手机号",clearable:"",onKeyup:f(ve,["enter"])},null,8,["modelValue"])]),_:1}),p(o,{label:"状态",prop:"status"},{default:u(()=>[p(s,{modelValue:m(se).status,"onUpdate:modelValue":a[2]||(a[2]=e=>m(se).status=e),placeholder:"全部",clearable:"",style:{width:"100px"}},{default:u(()=>[p(r,{label:"正常",value:1}),p(r,{label:"禁用",value:0})]),_:1},8,["modelValue"])]),_:1}),p(o,{label:"创建时间"},{default:u(()=>[p(w,{modelValue:m(se).createTime,"onUpdate:modelValue":a[3]||(a[3]=e=>m(se).createTime=e),editable:!1,type:"daterange","range-separator":"~","start-placeholder":"开始时间","end-placeholder":"截止时间","value-format":"YYYY-MM-DD"},null,8,["modelValue"])]),_:1}),p(o,{class:"search-buttons"},{default:u(()=>[p(S,{type:"primary",icon:"search",onClick:ve},{default:u(()=>a[20]||(a[20]=[_("搜索")])),_:1,__:[20]}),p(S,{icon:"refresh",onClick:he},{default:u(()=>a[21]||(a[21]=[_("重置")])),_:1,__:[21]})]),_:1})]),_:1},8,["model"])]),p(Te,{shadow:"hover",class:"data-table"},{default:u(()=>[c("div",W,[c("div",ee,[b((n(),g(S,{type:"success",icon:"plus",onClick:a[4]||(a[4]=e=>Ve())},{default:u(()=>a[22]||(a[22]=[_(" 新增 ")])),_:1,__:[22]})),[[Be,["sys:user:add"]]]),b((n(),g(S,{type:"danger",icon:"delete",disabled:0===m(fe).length,onClick:a[5]||(a[5]=e=>ke())},{default:u(()=>a[23]||(a[23]=[_(" 删除 ")])),_:1,__:[23]},8,["disabled"])),[[Be,"sys:user:delete"]])]),c("div",ae,[b((n(),g(S,{icon:"upload",onClick:Ue},{default:u(()=>a[24]||(a[24]=[_(" 导入 ")])),_:1,__:[24]})),[[Be,"sys:user:import"]]),b((n(),g(S,{icon:"download",onClick:Ce},{default:u(()=>a[25]||(a[25]=[_(" 导出 ")])),_:1,__:[25]})),[[Be,"sys:user:export"]])])]),b((n(),g(ze,{data:m(ie),border:"",stripe:"","highlight-current-row":"",class:"data-table__content",onSelectionChange:ye},{default:u(()=>[p(te,{type:"selection",width:"50",align:"center"}),p(te,{label:"用户名",prop:"username"}),p(te,{label:"昵称",width:"150",align:"center",prop:"nickname"}),p(te,{label:"性别",width:"100",align:"center"},{default:u(e=>[p(Ee,{modelValue:e.row.gender,"onUpdate:modelValue":a=>e.row.gender=a,code:"gender"},null,8,["modelValue","onUpdate:modelValue"])]),_:1}),p(te,{label:"部门",width:"120",align:"center",prop:"deptName"}),p(te,{label:"手机号码",align:"center",prop:"mobile",width:"120"}),p(te,{label:"邮箱",align:"center",prop:"email",width:"160"}),p(te,{label:"状态",align:"center",prop:"status",width:"80"},{default:u(e=>[p(Ie,{type:1==e.row.status?"success":"info"},{default:u(()=>[_(v(1==e.row.status?"正常":"禁用"),1)]),_:2},1032,["type"])]),_:1}),p(te,{label:"创建时间",align:"center",prop:"createTime",width:"150"}),p(te,{label:"操作",fixed:"right",width:"220"},{default:u(e=>[b((n(),g(S,{type:"primary",icon:"RefreshLeft",size:"small",link:"",onClick:a=>{return l=e.row,void G.prompt("请输入用户【"+l.username+"】的新密码","重置密码",{confirmButtonText:"确定",cancelButtonText:"取消"}).then(({value:e})=>{if(!e||e.length<6)return k.warning("密码至少需要6位字符，请重新输入"),!1;x.resetPassword(l.id,e).then(()=>{k.success("密码重置成功，新密码是："+e)})},()=>{k.info("已取消重置密码")});var l}},{default:u(()=>a[26]||(a[26]=[_(" 重置密码 ")])),_:2,__:[26]},1032,["onClick"])),[[Be,"sys:user:reset-password"]]),b((n(),g(S,{type:"primary",icon:"edit",link:"",size:"small",onClick:a=>Ve(e.row.id)},{default:u(()=>a[27]||(a[27]=[_(" 编辑 ")])),_:2,__:[27]},1032,["onClick"])),[[Be,"sys:user:edit"]]),b((n(),g(S,{type:"danger",icon:"delete",link:"",size:"small",onClick:a=>ke(e.row.id)},{default:u(()=>a[28]||(a[28]=[_(" 删除 ")])),_:2,__:[28]},1032,["onClick"])),[[Be,"sys:user:delete"]])]),_:1})]),_:1},8,["data"])),[[Fe,m(ne)]]),m(de)>0?(n(),g(Re,{key:0,total:m(de),"onUpdate:total":a[6]||(a[6]=e=>h(de)?de.value=e:null),page:m(se).pageNumber,"onUpdate:page":a[7]||(a[7]=e=>m(se).pageNumber=e),limit:m(se).pageSize,"onUpdate:limit":a[8]||(a[8]=e=>m(se).pageSize=e),onPagination:je},null,8,["total","page","limit"])):j("",!0)]),_:1})]),_:1})]),_:1}),p(qe,{modelValue:m(pe).visible,"onUpdate:modelValue":a[17]||(a[17]=e=>m(pe).visible=e),title:m(pe).title,"append-to-body":"",size:m(ue),onClose:we},{footer:u(()=>[c("div",le,[p(S,{type:"primary",onClick:m(xe)},{default:u(()=>a[29]||(a[29]=[_("确 定")])),_:1,__:[29]},8,["onClick"]),p(S,{onClick:we},{default:u(()=>a[30]||(a[30]=[_("取 消")])),_:1,__:[30]})])]),default:u(()=>[p(Y,{ref_key:"userFormRef",ref:re,model:m(me),rules:m(ce),"label-width":"80px"},{default:u(()=>[p(o,{label:"用户名",prop:"username"},{default:u(()=>[p(t,{modelValue:m(me).username,"onUpdate:modelValue":a[9]||(a[9]=e=>m(me).username=e),readonly:!!m(me).id,placeholder:"请输入用户名"},null,8,["modelValue","readonly"])]),_:1}),p(o,{label:"用户昵称",prop:"nickname"},{default:u(()=>[p(t,{modelValue:m(me).nickname,"onUpdate:modelValue":a[10]||(a[10]=e=>m(me).nickname=e),placeholder:"请输入用户昵称"},null,8,["modelValue"])]),_:1}),p(o,{label:"所属部门",prop:"deptId"},{default:u(()=>[p(De,{modelValue:m(me).deptId,"onUpdate:modelValue":a[11]||(a[11]=e=>m(me).deptId=e),placeholder:"请选择所属部门",data:m(_e),filterable:"","check-strictly":"","render-after-expand":!1},null,8,["modelValue","data"])]),_:1}),p(o,{label:"性别",prop:"gender"},{default:u(()=>[p(Oe,{modelValue:m(me).gender,"onUpdate:modelValue":a[12]||(a[12]=e=>m(me).gender=e),code:"gender"},null,8,["modelValue"])]),_:1}),p(o,{label:"角色",prop:"roleIds"},{default:u(()=>[p(s,{modelValue:m(me).roleIds,"onUpdate:modelValue":a[13]||(a[13]=e=>m(me).roleIds=e),multiple:"",placeholder:"请选择"},{default:u(()=>[(n(!0),d(y,null,V(m(be),e=>(n(),g(r,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),p(o,{label:"手机号码",prop:"mobile"},{default:u(()=>[p(t,{modelValue:m(me).mobile,"onUpdate:modelValue":a[14]||(a[14]=e=>m(me).mobile=e),placeholder:"请输入手机号码",maxlength:"11"},null,8,["modelValue"])]),_:1}),p(o,{label:"邮箱",prop:"email"},{default:u(()=>[p(t,{modelValue:m(me).email,"onUpdate:modelValue":a[15]||(a[15]=e=>m(me).email=e),placeholder:"请输入邮箱",maxlength:"50"},null,8,["modelValue"])]),_:1}),p(o,{label:"状态",prop:"status"},{default:u(()=>[p(Pe,{modelValue:m(me).status,"onUpdate:modelValue":a[16]||(a[16]=e=>m(me).status=e),"inline-prompt":"","active-text":"正常","inactive-text":"禁用","active-value":1,"inactive-value":0},null,8,["modelValue"])]),_:1})]),_:1},8,["model","rules"])]),_:1},8,["modelValue","title","size"]),p(K,{modelValue:m(ge),"onUpdate:modelValue":a[18]||(a[18]=e=>h(ge)?ge.value=e:null),onImportSuccess:a[19]||(a[19]=e=>ve())},null,8,["modelValue"])])}}});export{te as default};
