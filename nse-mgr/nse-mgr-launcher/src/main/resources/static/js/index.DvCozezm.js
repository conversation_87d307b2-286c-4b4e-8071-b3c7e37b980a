import{s as e,r as t,u as a,o as l,a as o,_ as n,d as s,b as r,c as i,e as u,f as c,w as d,g as p,h as v,i as h,j as m,n as f,k as g,l as b,m as y,E as x,p as w,T as k,q as _,t as C,v as S,x as A,y as M,z as E,A as I,B as T,C as L,D as V,F as P,G as j,H as O,I as B,J as $,K as D,L as F,M as N,N as R,O as W,P as U,Q as z,R as H,S as q,U as G,V as K,W as Y,X as Z,Y as Q,Z as J,$ as X,a0 as ee,a1 as te,a2 as ae,a3 as le,a4 as oe,a5 as ne,a6 as se,a7 as re,a8 as ie,a9 as ue,aa as ce,ab as de,ac as pe,ad as ve,ae as he,af as me,ag as fe,ah as ge,ai as be,aj as ye,ak as xe,al as we,am as ke,an as _e,ao as Ce,ap as Se,aq as Ae,ar as Me,as as Ee,at as Ie,au as Te,av as Le,aw as Ve,ax as Pe,ay as je,az as Oe,aA as Be,aB as $e,aC as De,aD as Fe,aE as Ne,aF as Re,aG as We,aH as Ue,aI as ze,aJ as He,aK as qe,aL as Ge,aM as Ke,aN as Ye,aO as Ze,aP as Qe}from"./index.Ckm1SagX.js";import{E as Je}from"./scrollbar.6rbryiG1.js";import{t as Xe,d as et,a as tt}from"./aria.C1IWO_Rd.js";import{_ as at}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{l as lt,E as ot,a as nt,b as st}from"./dropdown-item.CZEdesWp.js";import{E as rt}from"./dialog.TtqHlFhB.js";import"./overlay.CXfNA60T.js";/* empty css               */import{E as it,a as ut}from"./descriptions-item.Ds_fDr-2.js";import{u as ct,E as dt}from"./popper.DpZVcW1M.js";import{E as pt,a as vt}from"./form-item.CUMILu98.js";import{E as ht}from"./progress.ChnKapv7.js";/* empty css              */import{E as mt}from"./index.4JfkAhur.js";import{T as ft,E as gt}from"./index.CbYeWxT8.js";/* empty css                *//* empty css              *//* empty css             */import{E as bt}from"./divider.Cky69QLC.js";/* empty css             *//* empty css            *//* empty css                    */import{E as yt}from"./index.KtapGdwl.js";import{i as xt}from"./index.D2P-30Pk.js";import{E as wt}from"./index.Cg5eTZHL.js";import{C as kt}from"./index.B0geSHq7.js";import{f as _t}from"./vnode.BkZiIFpS.js";import{E as Ct}from"./drawer.CQbqc65D.js";import{E as St,a as At}from"./radio.D_DAkhYS.js";import{E as Mt}from"./switch.TMg_el83.js";import{g as Et}from"./position.D3azAgd1.js";import{u as It}from"./index.BLy3nyPI.js";import{C as Tt,U as Lt}from"./event.BwRzfsZt.js";import{u as Vt,a as Pt,b as jt,c as Ot}from"./use-form-common-props.BSYTvb6G.js";import{u as Bt}from"./index.Byj-i824.js";import{d as $t}from"./debounce.YgIwzEIs.js";import"./dropdown.Dp4e0zMH.js";import"./index.Dh_vcBr5.js";import"./castArray.Chmjnshw.js";import"./refs.biN0GvkM.js";import"./focus-trap.Bd_uzvDY.js";import"./index.BjYFza3j.js";import"./scroll.XdyICIdv.js";import"./index.C0OsJ5su.js";import"./index.BRUQ9gWw.js";import"./index.Cn1QDWeG.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./index.DJHzyRe5.js";const Dt={visibilityHeight:{type:Number,default:200},target:{type:String,default:""},right:{type:Number,default:40},bottom:{type:Number,default:40}},Ft={click:e=>e instanceof MouseEvent},Nt="ElBacktop",Rt=s({name:Nt});const Wt=_(n(s({...Rt,props:Dt,emits:Ft,setup(n,{emit:s}){const _=n,C=r("backtop"),{handleClick:S,visible:A}=((n,s,r)=>{const i=e(),u=e(),c=t(!1),d=()=>{i.value&&(c.value=i.value.scrollTop>=n.visibilityHeight)},p=o(d,300,!0);return a(u,"scroll",p),l(()=>{var e;u.value=document,i.value=document.documentElement,n.target&&(i.value=null!=(e=document.querySelector(n.target))?e:void 0,i.value||Xe(r,`target does not exist: ${n.target}`),u.value=i.value),d()}),{visible:c,handleClick:e=>{var t;null==(t=i.value)||t.scrollTo({top:0,behavior:"smooth"}),s("click",e)}}})(_,s,Nt),M=i(()=>({right:`${_.right}px`,bottom:`${_.bottom}px`}));return(e,t)=>(c(),u(k,{name:`${h(C).namespace.value}-fade-in`},{default:d(()=>[h(A)?(c(),p("div",{key:0,style:g(h(M)),class:f(h(C).b()),onClick:m(h(S),["stop"])},[b(e.$slots,"default",{},()=>[y(h(x),{class:f(h(C).e("icon"))},{default:d(()=>[y(h(w))]),_:1},8,["class"])])],14,["onClick"])):v("v-if",!0)]),_:3},8,["name"]))}}),[["__file","backtop.vue"]])),Ut=Symbol("breadcrumbKey"),zt=C({separator:{type:String,default:"/"},separatorIcon:{type:S}}),Ht=s({name:"ElBreadcrumb"});var qt=n(s({...Ht,props:zt,setup(e){const a=e,{t:o}=A(),n=r("breadcrumb"),s=t();return M(Ut,a),l(()=>{const e=s.value.querySelectorAll(`.${n.e("item")}`);e.length&&e[e.length-1].setAttribute("aria-current","page")}),(e,t)=>(c(),p("div",{ref_key:"breadcrumb",ref:s,class:f(h(n).b()),"aria-label":h(o)("el.breadcrumb.label"),role:"navigation"},[b(e.$slots,"default")],10,["aria-label"]))}}),[["__file","breadcrumb.vue"]]);const Gt=C({to:{type:E([String,Object]),default:""},replace:Boolean}),Kt=s({name:"ElBreadcrumbItem"});var Yt=n(s({...Kt,props:Gt,setup(e){const a=e,l=T(),o=I(Ut,void 0),n=r("breadcrumb"),s=l.appContext.config.globalProperties.$router,i=t(),v=()=>{a.to&&s&&(a.replace?s.replace(a.to):s.push(a.to))};return(e,t)=>{var a,l;return c(),p("span",{class:f(h(n).e("item"))},[L("span",{ref_key:"link",ref:i,class:f([h(n).e("inner"),h(n).is("link",!!e.to)]),role:"link",onClick:v},[b(e.$slots,"default")],2),(null==(a=h(o))?void 0:a.separatorIcon)?(c(),u(h(x),{key:0,class:f(h(n).e("separator"))},{default:d(()=>[(c(),u(V(h(o).separatorIcon)))]),_:1},8,["class"])):(c(),p("span",{key:1,class:f(h(n).e("separator")),role:"presentation"},P(null==(l=h(o))?void 0:l.separator),3))],2)}}}),[["__file","breadcrumb-item.vue"]]);const Zt=_(qt,{BreadcrumbItem:Yt}),Qt=j(Yt),Jt=C({color:{type:E(Object),required:!0},vertical:Boolean});let Xt=!1;function ea(e,t){if(!O)return;const a=function(e){var a;null==(a=t.drag)||a.call(t,e)},l=function(e){var o;document.removeEventListener("mousemove",a),document.removeEventListener("mouseup",l),document.removeEventListener("touchmove",a),document.removeEventListener("touchend",l),document.onselectstart=null,document.ondragstart=null,Xt=!1,null==(o=t.end)||o.call(t,e)},o=function(e){var o;Xt||(e.preventDefault(),document.onselectstart=()=>!1,document.ondragstart=()=>!1,document.addEventListener("mousemove",a),document.addEventListener("mouseup",l),document.addEventListener("touchmove",a),document.addEventListener("touchend",l),Xt=!0,null==(o=t.start)||o.call(t,e))};e.addEventListener("mousedown",o),e.addEventListener("touchstart",o,{passive:!1})}const ta=(e,{bar:a,thumb:o,handleDrag:n})=>{const s=T(),u=r("color-alpha-slider"),c=t(0),d=t(0),p=t();function v(){c.value=function(){if(!o.value)return 0;if(e.vertical)return 0;const t=s.vnode.el,a=e.color.get("alpha");return t?Math.round(a*(t.offsetWidth-o.value.offsetWidth/2)/100):0}(),d.value=function(){if(!o.value)return 0;const t=s.vnode.el;if(!e.vertical)return 0;const a=e.color.get("alpha");return t?Math.round(a*(t.offsetHeight-o.value.offsetHeight/2)/100):0}(),p.value=function(){if(e.color&&e.color.value){const{r:t,g:a,b:l}=e.color.toRgb();return`linear-gradient(to right, rgba(${t}, ${a}, ${l}, 0) 0%, rgba(${t}, ${a}, ${l}, 1) 100%)`}return""}()}l(()=>{if(!a.value||!o.value)return;const e={drag:e=>{n(e)},end:e=>{n(e)}};ea(a.value,e),ea(o.value,e),v()}),B(()=>e.color.get("alpha"),()=>v()),B(()=>e.color.value,()=>v());const h=i(()=>[u.b(),u.is("vertical",e.vertical)]),m=i(()=>u.e("bar")),f=i(()=>u.e("thumb"));return{rootKls:h,barKls:m,barStyle:i(()=>({background:p.value})),thumbKls:f,thumbStyle:i(()=>({left:$(c.value),top:$(d.value)})),update:v}},aa=s({name:"ElColorAlphaSlider"});var la=n(s({...aa,props:Jt,setup(t,{expose:a}){const l=t,{alpha:o,alphaLabel:n,bar:s,thumb:r,handleDrag:u,handleClick:d,handleKeydown:v}=(t=>{const a=T(),{t:l}=A(),o=e(),n=e(),s=i(()=>t.color.get("alpha")),r=i(()=>l("el.colorpicker.alphaLabel"));function u(e){if(!n.value||!o.value)return;const l=a.vnode.el.getBoundingClientRect(),{clientX:s,clientY:r}=Et(e);if(t.vertical){let e=r-l.top;e=Math.max(o.value.offsetHeight/2,e),e=Math.min(e,l.height-o.value.offsetHeight/2),t.color.set("alpha",Math.round((e-o.value.offsetHeight/2)/(l.height-o.value.offsetHeight)*100))}else{let e=s-l.left;e=Math.max(o.value.offsetWidth/2,e),e=Math.min(e,l.width-o.value.offsetWidth/2),t.color.set("alpha",Math.round((e-o.value.offsetWidth/2)/(l.width-o.value.offsetWidth)*100))}}function c(e){let a=s.value+e;a=a<0?0:a>100?100:a,t.color.set("alpha",a)}return{thumb:o,bar:n,alpha:s,alphaLabel:r,handleDrag:u,handleClick:function(e){var t;e.target!==o.value&&u(e),null==(t=o.value)||t.focus()},handleKeydown:function(e){const{code:t,shiftKey:a}=e,l=a?10:1;switch(t){case D.left:case D.down:e.preventDefault(),e.stopPropagation(),c(-l);break;case D.right:case D.up:e.preventDefault(),e.stopPropagation(),c(l)}}}})(l),{rootKls:m,barKls:b,barStyle:y,thumbKls:x,thumbStyle:w,update:k}=ta(l,{bar:s,thumb:r,handleDrag:u});return a({update:k,bar:s,thumb:r}),(e,t)=>(c(),p("div",{class:f(h(m))},[L("div",{ref_key:"bar",ref:s,class:f(h(b)),style:g(h(y)),onClick:h(d)},null,14,["onClick"]),L("div",{ref_key:"thumb",ref:r,class:f(h(x)),style:g(h(w)),"aria-label":h(n),"aria-valuenow":h(o),"aria-orientation":e.vertical?"vertical":"horizontal","aria-valuemin":"0","aria-valuemax":"100",role:"slider",tabindex:"0",onKeydown:h(v)},null,46,["aria-label","aria-valuenow","aria-orientation","onKeydown"])],2))}}),[["__file","alpha-slider.vue"]]);var oa=n(s({name:"ElColorHueSlider",props:{color:{type:Object,required:!0},vertical:Boolean},setup(e){const a=r("color-hue-slider"),o=T(),n=t(),s=t(),u=t(0),c=t(0),d=i(()=>e.color.get("hue"));function p(t){if(!s.value||!n.value)return;const a=o.vnode.el.getBoundingClientRect(),{clientX:l,clientY:r}=Et(t);let i;if(e.vertical){let e=r-a.top;e=Math.min(e,a.height-n.value.offsetHeight/2),e=Math.max(n.value.offsetHeight/2,e),i=Math.round((e-n.value.offsetHeight/2)/(a.height-n.value.offsetHeight)*360)}else{let e=l-a.left;e=Math.min(e,a.width-n.value.offsetWidth/2),e=Math.max(n.value.offsetWidth/2,e),i=Math.round((e-n.value.offsetWidth/2)/(a.width-n.value.offsetWidth)*360)}e.color.set("hue",i)}function v(){u.value=function(){if(!n.value)return 0;const t=o.vnode.el;if(e.vertical)return 0;const a=e.color.get("hue");return t?Math.round(a*(t.offsetWidth-n.value.offsetWidth/2)/360):0}(),c.value=function(){if(!n.value)return 0;const t=o.vnode.el;if(!e.vertical)return 0;const a=e.color.get("hue");return t?Math.round(a*(t.offsetHeight-n.value.offsetHeight/2)/360):0}()}return B(()=>d.value,()=>{v()}),l(()=>{if(!s.value||!n.value)return;const e={drag:e=>{p(e)},end:e=>{p(e)}};ea(s.value,e),ea(n.value,e),v()}),{bar:s,thumb:n,thumbLeft:u,thumbTop:c,hueValue:d,handleClick:function(e){e.target!==n.value&&p(e)},update:v,ns:a}}}),[["render",function(e,t,a,l,o,n){return c(),p("div",{class:f([e.ns.b(),e.ns.is("vertical",e.vertical)])},[L("div",{ref:"bar",class:f(e.ns.e("bar")),onClick:e.handleClick},null,10,["onClick"]),L("div",{ref:"thumb",class:f(e.ns.e("thumb")),style:g({left:e.thumbLeft+"px",top:e.thumbTop+"px"})},null,6)],2)}],["__file","hue-slider.vue"]]);const na=C({modelValue:{type:E(String),default:void 0},id:String,showAlpha:Boolean,colorFormat:String,disabled:Boolean,size:W,popperClass:{type:String,default:""},tabindex:{type:[String,Number],default:0},teleported:ct.teleported,predefine:{type:E(Array)},validateEvent:{type:Boolean,default:!0},...R,...It(["ariaLabel"])}),sa={[Lt]:e=>F(e)||N(e),[Tt]:e=>F(e)||N(e),activeChange:e=>F(e)||N(e),focus:e=>e instanceof FocusEvent,blur:e=>e instanceof FocusEvent},ra=Symbol("colorPickerContextKey");class ia{constructor(e={}){this._hue=0,this._saturation=100,this._value=100,this._alpha=100,this._tiny=new ft,this._isValid=!1,this.enableAlpha=!1,this.format="",this.value="";for(const t in e)U(e,t)&&(this[t]=e[t]);e.value?this.fromString(e.value):this.doOnChange()}set(e,t){if(1!==arguments.length||"object"!=typeof e)this[`_${e}`]=t,this._isValid=!0,this.doOnChange();else for(const a in e)U(e,a)&&this.set(a,e[a])}get(e){return["hue","saturation","value","alpha"].includes(e)?Math.round(this[`_${e}`]):this[`_${e}`]}toRgb(){return this._isValid?this._tiny.toRgb():{r:255,g:255,b:255,a:0}}fromString(e){const t=new ft(e);if(this._isValid=t.isValid,t.isValid){const{h:e,s:a,v:l,a:o}=t.toHsv();this._hue=e,this._saturation=100*a,this._value=100*l,this._alpha=100*o}else this._hue=0,this._saturation=100,this._value=100,this._alpha=100;this.doOnChange()}compare(e){const t=new ft({h:e._hue,s:e._saturation/100,v:e._value/100,a:e._alpha/100});return this._tiny.equals(t)}doOnChange(){const{_hue:e,_saturation:t,_value:a,_alpha:l,format:o,enableAlpha:n}=this;let s=o||(n?"rgb":"hex");"hex"===o&&n&&(s="hex8"),this._tiny=new ft({h:e,s:t/100,v:a/100,a:l/100}),this.value=this._isValid?this._tiny.toString(s):""}}var ua=n(s({props:{colors:{type:Array,required:!0},color:{type:Object,required:!0},enableAlpha:{type:Boolean,required:!0}},setup(e){const a=r("color-predefine"),{currentColor:l}=I(ra),o=t(n(e.colors,e.color));function n(e,t){return e.map(e=>{const a=new ia({value:e});return a.selected=a.compare(t),a})}return B(()=>l.value,e=>{const t=new ia({value:e});o.value.forEach(e=>{e.selected=t.compare(e)})}),q(()=>{o.value=n(e.colors,e.color)}),{rgbaColors:o,handleSelect:function(t){e.color.fromString(e.colors[t])},ns:a}}}),[["render",function(e,t,a,l,o,n){return c(),p("div",{class:f(e.ns.b())},[L("div",{class:f(e.ns.e("colors"))},[(c(!0),p(z,null,H(e.rgbaColors,(t,a)=>(c(),p("div",{key:e.colors[a],class:f([e.ns.e("color-selector"),e.ns.is("alpha",t.get("alpha")<100),{selected:t.selected}]),onClick:t=>e.handleSelect(a)},[L("div",{style:g({backgroundColor:t.value})},null,4)],10,["onClick"]))),128))],2)],2)}],["__file","predefine.vue"]]);var ca=n(s({name:"ElSlPanel",props:{color:{type:Object,required:!0}},setup(e){const a=r("color-svpanel"),o=T(),n=t(0),s=t(0),u=t("hsl(0, 100%, 50%)"),c=i(()=>({hue:e.color.get("hue"),value:e.color.get("value")}));function d(){const t=e.color.get("saturation"),a=e.color.get("value"),l=o.vnode.el,{clientWidth:r,clientHeight:i}=l;s.value=t*r/100,n.value=(100-a)*i/100,u.value=`hsl(${e.color.get("hue")}, 100%, 50%)`}function p(t){const a=o.vnode.el.getBoundingClientRect(),{clientX:l,clientY:r}=Et(t);let i=l-a.left,u=r-a.top;i=Math.max(0,i),i=Math.min(i,a.width),u=Math.max(0,u),u=Math.min(u,a.height),s.value=i,n.value=u,e.color.set({saturation:i/a.width*100,value:100-u/a.height*100})}return B(()=>c.value,()=>{d()}),l(()=>{ea(o.vnode.el,{drag:e=>{p(e)},end:e=>{p(e)}}),d()}),{cursorTop:n,cursorLeft:s,background:u,colorValue:c,handleDrag:p,update:d,ns:a}}}),[["render",function(e,t,a,l,o,n){return c(),p("div",{class:f(e.ns.b()),style:g({backgroundColor:e.background})},[L("div",{class:f(e.ns.e("white"))},null,2),L("div",{class:f(e.ns.e("black"))},null,2),L("div",{class:f(e.ns.e("cursor")),style:g({top:e.cursorTop+"px",left:e.cursorLeft+"px"})},[L("div")],6)],6)}],["__file","sv-panel.vue"]]);const da=s({name:"ElColorPicker"});const pa=_(n(s({...da,props:na,emits:sa,setup(e,{expose:a,emit:o}){const n=e,{t:s}=A(),m=r("color"),{formItem:b}=Vt(),w=Pt(),k=jt(),{valueOnClear:_,isEmptyValue:C}=G(n,null),{inputId:S,isLabeledByFormItem:E}=Ot(n,{formItemContext:b}),I=t(),T=t(),V=t(),j=t(),O=t(),$=t(),{isFocused:F,handleFocus:N,handleBlur:R}=Bt(O,{disabled:k,beforeBlur(e){var t;return null==(t=j.value)?void 0:t.isFocusInsideContent(e)},afterBlur(){ie(!1),pe()}});let W=!0;const U=K(new ia({enableAlpha:n.showAlpha,format:n.colorFormat||"",value:n.modelValue})),z=t(!1),H=t(!1),q=t(""),le=i(()=>n.modelValue||H.value?function(e,t){const{r:a,g:l,b:o,a:n}=e.toRgb();return t?`rgba(${a}, ${l}, ${o}, ${n})`:`rgb(${a}, ${l}, ${o})`}(U,n.showAlpha):"transparent"),oe=i(()=>n.modelValue||H.value?U.value:""),ne=i(()=>E.value?void 0:n.ariaLabel||s("el.colorpicker.defaultLabel")),se=i(()=>E.value?null==b?void 0:b.labelId:void 0),re=i(()=>[m.b("picker"),m.is("disabled",k.value),m.bm("picker",w.value),m.is("focused",F.value)]);function ie(e){z.value=e}const ue=$t(ie,100,{leading:!0});function ce(){k.value||ie(!0)}function de(){ue(!1),pe()}function pe(){ae(()=>{n.modelValue?U.fromString(n.modelValue):(U.value="",!oe.value&&q.value&&(q.value=""),ae(()=>{H.value=!1}))})}function ve(){k.value||(z.value&&pe(),ue(!z.value))}function he(){U.fromString(q.value),U.value!==q.value&&(q.value=U.value)}function me(){const e=C(U.value)?_.value:U.value;o(Lt,e),o(Tt,e),n.validateEvent&&(null==b||b.validate("change").catch(e=>et())),ue(!1),ae(()=>{const e=new ia({enableAlpha:n.showAlpha,format:n.colorFormat||"",value:n.modelValue});U.compare(e)||pe()})}function fe(){ue(!1),o(Lt,_.value),o(Tt,_.value),n.modelValue!==_.value&&n.validateEvent&&(null==b||b.validate("change").catch(e=>et())),pe()}function ge(){z.value&&(de(),F.value&&xe())}function be(e){e.preventDefault(),e.stopPropagation(),ie(!1),pe()}function ye(e){switch(e.code){case D.enter:case D.numpadEnter:case D.space:e.preventDefault(),e.stopPropagation(),ce(),$.value.focus();break;case D.esc:be(e)}}function xe(){O.value.focus()}return l(()=>{n.modelValue&&(q.value=oe.value)}),B(()=>n.modelValue,e=>{e?e&&e!==U.value&&(W=!1,U.fromString(e)):H.value=!1}),B(()=>[n.colorFormat,n.showAlpha],()=>{U.enableAlpha=n.showAlpha,U.format=n.colorFormat||U.format,U.doOnChange(),o(Lt,U.value)}),B(()=>oe.value,e=>{q.value=e,W&&o("activeChange",e),W=!0}),B(()=>U.value,()=>{n.modelValue||H.value||(H.value=!0)}),B(()=>z.value,()=>{ae(()=>{var e,t,a;null==(e=I.value)||e.update(),null==(t=T.value)||t.update(),null==(a=V.value)||a.update()})}),M(ra,{currentColor:oe}),a({color:U,show:ce,hide:de,focus:xe,blur:function(){O.value.blur()}}),(e,t)=>(c(),u(h(dt),{ref_key:"popper",ref:j,visible:z.value,"show-arrow":!1,"fallback-placements":["bottom","top","right","left"],offset:0,"gpu-acceleration":!1,"popper-class":[h(m).be("picker","panel"),h(m).b("dropdown"),e.popperClass],"stop-popper-mouse-event":!1,effect:"light",trigger:"click",teleported:e.teleported,transition:`${h(m).namespace.value}-zoom-in-top`,persistent:"",onHide:e=>ie(!1)},{content:d(()=>[Z((c(),p("div",{onKeydown:ee(be,["esc"])},[L("div",{class:f(h(m).be("dropdown","main-wrapper"))},[y(oa,{ref_key:"hue",ref:I,class:"hue-slider",color:h(U),vertical:""},null,8,["color"]),y(ca,{ref_key:"sv",ref:T,color:h(U)},null,8,["color"])],2),e.showAlpha?(c(),u(la,{key:0,ref_key:"alpha",ref:V,color:h(U)},null,8,["color"])):v("v-if",!0),e.predefine?(c(),u(ua,{key:1,ref:"predefine","enable-alpha":e.showAlpha,color:h(U),colors:e.predefine},null,8,["enable-alpha","color","colors"])):v("v-if",!0),L("div",{class:f(h(m).be("dropdown","btns"))},[L("span",{class:f(h(m).be("dropdown","value"))},[y(h(mt),{ref_key:"inputRef",ref:$,modelValue:q.value,"onUpdate:modelValue":e=>q.value=e,"validate-event":!1,size:"small",onChange:he},null,8,["modelValue","onUpdate:modelValue"])],2),y(h(gt),{class:f(h(m).be("dropdown","link-btn")),text:"",size:"small",onClick:fe},{default:d(()=>[te(P(h(s)("el.colorpicker.clear")),1)]),_:1},8,["class"]),y(h(gt),{plain:"",size:"small",class:f(h(m).be("dropdown","btn")),onClick:me},{default:d(()=>[te(P(h(s)("el.colorpicker.confirm")),1)]),_:1},8,["class"])],2)],40,["onKeydown"])),[[h(kt),ge,O.value]])]),default:d(()=>[L("div",Y({id:h(S),ref_key:"triggerRef",ref:O},e.$attrs,{class:h(re),role:"button","aria-label":h(ne),"aria-labelledby":h(se),"aria-description":h(s)("el.colorpicker.description",{color:e.modelValue||""}),"aria-disabled":h(k),tabindex:h(k)?void 0:e.tabindex,onKeydown:ye,onFocus:h(N),onBlur:h(R)}),[L("div",{class:f(h(m).be("picker","trigger")),onClick:ve},[L("span",{class:f([h(m).be("picker","color"),h(m).is("alpha",e.showAlpha)])},[L("span",{class:f(h(m).be("picker","color-inner")),style:g({backgroundColor:h(le)})},[Z(y(h(x),{class:f([h(m).be("picker","icon"),h(m).is("icon-arrow-down")])},{default:d(()=>[y(h(Q))]),_:1},8,["class"]),[[J,e.modelValue||H.value]]),Z(y(h(x),{class:f([h(m).be("picker","empty"),h(m).is("icon-close")])},{default:d(()=>[y(h(X))]),_:1},8,["class"]),[[J,!e.modelValue&&!H.value]])],6)],2)],2)],16,["id","aria-label","aria-labelledby","aria-description","aria-disabled","tabindex","onFocus","onBlur"])]),_:1},8,["visible","popper-class","teleported","transition","onHide"]))}}),[["__file","color-picker.vue"]]));let va=class{constructor(e,t){this.parent=e,this.domNode=t,this.subIndex=0,this.subIndex=0,this.init()}init(){this.subMenuItems=this.domNode.querySelectorAll("li"),this.addListeners()}gotoSubIndex(e){e===this.subMenuItems.length?e=0:e<0&&(e=this.subMenuItems.length-1),this.subMenuItems[e].focus(),this.subIndex=e}addListeners(){const e=this.parent.domNode;Array.prototype.forEach.call(this.subMenuItems,t=>{t.addEventListener("keydown",t=>{let a=!1;switch(t.code){case D.down:this.gotoSubIndex(this.subIndex+1),a=!0;break;case D.up:this.gotoSubIndex(this.subIndex-1),a=!0;break;case D.tab:tt(e,"mouseleave");break;case D.enter:case D.numpadEnter:case D.space:a=!0,t.currentTarget.click()}return a&&(t.preventDefault(),t.stopPropagation()),!1})})}},ha=class{constructor(e,t){this.domNode=e,this.submenu=null,this.submenu=null,this.init(t)}init(e){this.domNode.setAttribute("tabindex","0");const t=this.domNode.querySelector(`.${e}-menu`);t&&(this.submenu=new va(this,t)),this.addListeners()}addListeners(){this.domNode.addEventListener("keydown",e=>{let t=!1;switch(e.code){case D.down:tt(e.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(0),t=!0;break;case D.up:tt(e.currentTarget,"mouseenter"),this.submenu&&this.submenu.gotoSubIndex(this.submenu.subMenuItems.length-1),t=!0;break;case D.tab:tt(e.currentTarget,"mouseleave");break;case D.enter:case D.numpadEnter:case D.space:t=!0,e.currentTarget.click()}t&&e.preventDefault()})}},ma=class{constructor(e,t){this.domNode=e,this.init(t)}init(e){const t=this.domNode.childNodes;Array.from(t).forEach(t=>{1===t.nodeType&&new ha(t,e)})}};const fa=s({name:"ElMenuCollapseTransition"});var ga=n(s({...fa,setup(e){const t=r("menu"),a={onBeforeEnter:e=>e.style.opacity="0.2",onEnter(e,a){le(e,`${t.namespace.value}-opacity-transition`),e.style.opacity="1",a()},onAfterEnter(e){ne(e,`${t.namespace.value}-opacity-transition`),e.style.opacity=""},onBeforeLeave(e){e.dataset||(e.dataset={}),oe(e,t.m("collapse"))?(ne(e,t.m("collapse")),e.dataset.oldOverflow=e.style.overflow,e.dataset.scrollWidth=e.clientWidth.toString(),le(e,t.m("collapse"))):(le(e,t.m("collapse")),e.dataset.oldOverflow=e.style.overflow,e.dataset.scrollWidth=e.clientWidth.toString(),ne(e,t.m("collapse"))),e.style.width=`${e.scrollWidth}px`,e.style.overflow="hidden"},onLeave(e){le(e,"horizontal-collapse-transition"),e.style.width=`${e.dataset.scrollWidth}px`}};return(e,t)=>(c(),u(k,Y({mode:"out-in"},h(a)),{default:d(()=>[b(e.$slots,"default")]),_:3},16))}}),[["__file","menu-collapse-transition.vue"]]);function ba(e,t){const a=i(()=>{let a=e.parent;const l=[t.value];for(;"ElMenu"!==a.type.name;)a.props.index&&l.unshift(a.props.index),a=a.parent;return l});return{parentMenu:i(()=>{let t=e.parent;for(;t&&!["ElMenu","ElSubMenu"].includes(t.type.name);)t=t.parent;return t}),indexPath:a}}function ya(e){return i(()=>{const t=e.backgroundColor;return t?new ft(t).shade(20).toString():""})}const xa=(e,t)=>{const a=r("menu");return i(()=>a.cssVarBlock({"text-color":e.textColor||"","hover-text-color":e.textColor||"","bg-color":e.backgroundColor||"","hover-bg-color":ya(e).value||"","active-color":e.activeTextColor||"",level:`${t}`}))},wa="rootMenu",ka="subMenu:",_a=C({index:{type:String,required:!0},showTimeout:Number,hideTimeout:Number,popperClass:String,disabled:Boolean,teleported:{type:Boolean,default:void 0},popperOffset:Number,expandCloseIcon:{type:S},expandOpenIcon:{type:S},collapseCloseIcon:{type:S},collapseOpenIcon:{type:S}}),Ca="ElSubMenu";var Sa=s({name:Ca,props:_a,setup(e,{slots:a,expose:o}){const n=T(),{indexPath:s,parentMenu:u}=ba(n,i(()=>e.index)),c=r("menu"),d=r("sub-menu"),p=I(wa);p||Xe(Ca,"can not inject root menu");const v=I(`${ka}${u.value.uid}`);v||Xe(Ca,"can not inject sub menu");const h=t({}),m=t({});let f;const g=t(!1),b=t(),y=t(),w=i(()=>"horizontal"===V.value&&_.value?"bottom-start":"right-start"),k=i(()=>"horizontal"===V.value&&_.value||"vertical"===V.value&&!p.props.collapse?e.expandCloseIcon&&e.expandOpenIcon?E.value?e.expandOpenIcon:e.expandCloseIcon:Q:e.collapseCloseIcon&&e.collapseOpenIcon?E.value?e.collapseOpenIcon:e.collapseCloseIcon:se),_=i(()=>0===v.level),C=i(()=>{const t=e.teleported;return re(t)?_.value:t}),S=i(()=>p.props.collapse?`${c.namespace.value}-zoom-in-left`:`${c.namespace.value}-zoom-in-top`),A=i(()=>"horizontal"===V.value&&_.value?["bottom-start","bottom-end","top-start","top-end","right-start","left-start"]:["right-start","right","right-end","left-start","bottom-start","bottom-end","top-start","top-end"]),E=i(()=>p.openedMenus.includes(e.index)),L=i(()=>[...Object.values(h.value),...Object.values(m.value)].some(({active:e})=>e)),V=i(()=>p.props.mode),P=i(()=>p.props.persistent),j=K({index:e.index,indexPath:s,active:L}),O=xa(p.props,v.level+1),$=i(()=>{var t;return null!=(t=e.popperOffset)?t:p.props.popperOffset}),D=i(()=>{var t;return null!=(t=e.popperClass)?t:p.props.popperClass}),N=i(()=>{var t;return null!=(t=e.showTimeout)?t:p.props.showTimeout}),R=i(()=>{var t;return null!=(t=e.hideTimeout)?t:p.props.hideTimeout}),W=e=>{var t,a,l;e||null==(l=null==(a=null==(t=y.value)?void 0:t.popperRef)?void 0:a.popperInstanceRef)||l.destroy()},U=()=>{"hover"===p.props.menuTrigger&&"horizontal"===p.props.mode||p.props.collapse&&"vertical"===p.props.mode||e.disabled||p.handleSubMenuClick({index:e.index,indexPath:s.value,active:L.value})},H=(t,a=N.value)=>{var l;"focus"!==t.type&&("click"===p.props.menuTrigger&&"horizontal"===p.props.mode||!p.props.collapse&&"vertical"===p.props.mode||e.disabled?v.mouseInChild.value=!0:(v.mouseInChild.value=!0,null==f||f(),({stop:f}=ce(()=>{p.openMenu(e.index,s.value)},a)),C.value&&(null==(l=u.value.vnode.el)||l.dispatchEvent(new MouseEvent("mouseenter")))))},q=(t=!1)=>{var a;"click"===p.props.menuTrigger&&"horizontal"===p.props.mode||!p.props.collapse&&"vertical"===p.props.mode?v.mouseInChild.value=!1:(null==f||f(),v.mouseInChild.value=!1,({stop:f}=ce(()=>!g.value&&p.closeMenu(e.index,s.value),R.value)),C.value&&t&&(null==(a=v.handleMouseleave)||a.call(v,!0)))};B(()=>p.props.collapse,e=>W(Boolean(e)));{const e=e=>{m.value[e.index]=e},t=e=>{delete m.value[e.index]};M(`${ka}${n.uid}`,{addSubMenu:e,removeSubMenu:t,handleMouseleave:q,mouseInChild:g,level:v.level+1})}return o({opened:E}),l(()=>{p.addSubMenu(j),v.addSubMenu(j)}),ie(()=>{v.removeSubMenu(j),p.removeSubMenu(j)}),()=>{var t;const l=[null==(t=a.title)?void 0:t.call(a),ue(x,{class:d.e("icon-arrow"),style:{transform:E.value?e.expandCloseIcon&&e.expandOpenIcon||e.collapseCloseIcon&&e.collapseOpenIcon&&p.props.collapse?"none":"rotateZ(180deg)":"none"}},{default:()=>F(k.value)?ue(n.appContext.components[k.value]):ue(k.value)})],o=p.isMenuPopup?ue(dt,{ref:y,visible:E.value,effect:"light",pure:!0,offset:$.value,showArrow:!1,persistent:P.value,popperClass:D.value,placement:w.value,teleported:C.value,fallbackPlacements:A.value,transition:S.value,gpuAcceleration:!1},{content:()=>{var e;return ue("div",{class:[c.m(V.value),c.m("popup-container"),D.value],onMouseenter:e=>H(e,100),onMouseleave:()=>q(!0),onFocus:e=>H(e,100)},[ue("ul",{class:[c.b(),c.m("popup"),c.m(`popup-${w.value}`)],style:O.value},[null==(e=a.default)?void 0:e.call(a)])])},default:()=>ue("div",{class:d.e("title"),onClick:U},l)}):ue(z,{},[ue("div",{class:d.e("title"),ref:b,onClick:U},l),ue(wt,{},{default:()=>{var e;return Z(ue("ul",{role:"menu",class:[c.b(),c.m("inline")],style:O.value},[null==(e=a.default)?void 0:e.call(a)]),[[J,E.value]])}})]);return ue("li",{class:[d.b(),d.is("active",L.value),d.is("opened",E.value),d.is("disabled",e.disabled)],role:"menuitem",ariaHaspopup:!0,ariaExpanded:E.value,onMouseenter:H,onMouseleave:()=>q(),onFocus:H},[o])}}});const Aa=C({mode:{type:String,values:["horizontal","vertical"],default:"vertical"},defaultActive:{type:String,default:""},defaultOpeneds:{type:E(Array),default:()=>me([])},uniqueOpened:Boolean,router:Boolean,menuTrigger:{type:String,values:["hover","click"],default:"hover"},collapse:Boolean,backgroundColor:String,textColor:String,activeTextColor:String,closeOnClickOutside:Boolean,collapseTransition:{type:Boolean,default:!0},ellipsis:{type:Boolean,default:!0},popperOffset:{type:Number,default:6},ellipsisIcon:{type:S,default:()=>he},popperEffect:{type:E(String),default:"dark"},popperClass:String,showTimeout:{type:Number,default:300},hideTimeout:{type:Number,default:300},persistent:{type:Boolean,default:!0}}),Ma=e=>de(e)&&e.every(e=>F(e));var Ea=s({name:"ElMenu",props:Aa,emits:{close:(e,t)=>F(e)&&Ma(t),open:(e,t)=>F(e)&&Ma(t),select:(e,t,a,l)=>F(e)&&Ma(t)&&pe(a)&&(re(l)||l instanceof Promise)},setup(e,{emit:a,slots:o,expose:n}){const s=T(),u=s.appContext.config.globalProperties.$router,c=t(),d=r("menu"),p=r("sub-menu"),v=t(-1),h=t(e.defaultOpeneds&&!e.collapse?e.defaultOpeneds.slice(0):[]),m=t(e.defaultActive),f=t({}),g=t({}),b=i(()=>"horizontal"===e.mode||"vertical"===e.mode&&e.collapse),y=(t,l)=>{h.value.includes(t)||(e.uniqueOpened&&(h.value=h.value.filter(e=>l.includes(e))),h.value.push(t),a("open",t,l))},w=e=>{const t=h.value.indexOf(e);-1!==t&&h.value.splice(t,1)},k=(e,t)=>{w(e),a("close",e,t)},_=({index:e,indexPath:t})=>{h.value.includes(e)?k(e,t):y(e,t)},C=t=>{("horizontal"===e.mode||e.collapse)&&(h.value=[]);const{index:l,indexPath:o}=t;if(!N(l)&&!N(o))if(e.router&&u){const e=t.route||l,n=u.push(e).then(e=>(e||(m.value=l),e));a("select",l,o,{index:l,indexPath:o,route:e},n)}else m.value=l,a("select",l,o,{index:l,indexPath:o})},S=t=>{var a;const l=f.value,o=l[t]||m.value&&l[m.value]||l[e.defaultActive];m.value=null!=(a=null==o?void 0:o.index)?a:t},A=()=>{var e,t;if(!c.value)return-1;const a=Array.from(null!=(t=null==(e=c.value)?void 0:e.childNodes)?t:[]).filter(e=>"#text"!==e.nodeName||e.nodeValue),l=getComputedStyle(c.value),o=Number.parseInt(l.paddingLeft,10),n=Number.parseInt(l.paddingRight,10),s=c.value.clientWidth-o-n;let r=0,i=0;return a.forEach((e,t)=>{"#comment"!==e.nodeName&&(r+=(e=>{const t=getComputedStyle(e),a=Number.parseInt(t.marginLeft,10),l=Number.parseInt(t.marginRight,10);return e.offsetWidth+a+l||0})(e),r<=s-64&&(i=t+1))}),i===a.length?-1:i};let E=!0;const I=()=>{if(v.value===A())return;const e=()=>{v.value=-1,ae(()=>{v.value=A()})};E?e():((e,t=33.34)=>{let a;return()=>{a&&clearTimeout(a),a=setTimeout(()=>{e()},t)}})(e)(),E=!1};let L;B(()=>e.defaultActive,e=>{f.value[e]||(m.value=""),S(e)}),B(()=>e.collapse,e=>{e&&(h.value=[])}),B(f.value,()=>{const t=m.value&&f.value[m.value];if(!t||"horizontal"===e.mode||e.collapse)return;t.indexPath.forEach(e=>{const t=g.value[e];t&&y(e,t.indexPath)})}),q(()=>{"horizontal"===e.mode&&e.ellipsis?L=ve(c,I).stop:null==L||L()});const V=t(!1);{const t=e=>{g.value[e.index]=e},a=e=>{delete g.value[e.index]},l=e=>{f.value[e.index]=e},o=e=>{delete f.value[e.index]};M(wa,K({props:e,openedMenus:h,items:f,subMenus:g,activeIndex:m,isMenuPopup:b,addMenuItem:l,removeMenuItem:o,addSubMenu:t,removeSubMenu:a,openMenu:y,closeMenu:k,handleMenuItemClick:C,handleSubMenuClick:_})),M(`${ka}${s.uid}`,{addSubMenu:t,removeSubMenu:a,mouseInChild:V,level:0})}l(()=>{"horizontal"===e.mode&&new ma(s.vnode.el,d.namespace.value)});n({open:e=>{const{indexPath:t}=g.value[e];t.forEach(e=>y(e,t))},close:w,updateActiveIndex:S,handleResize:I});const P=xa(e,0);return()=>{var t,l;let n=null!=(l=null==(t=o.default)?void 0:t.call(o))?l:[];const s=[];if("horizontal"===e.mode&&c.value){const t=_t(n),a=-1===v.value?t:t.slice(0,v.value),l=-1===v.value?[]:t.slice(v.value);(null==l?void 0:l.length)&&e.ellipsis&&(n=a,s.push(ue(Sa,{index:"sub-menu-more",class:p.e("hide-arrow"),popperOffset:e.popperOffset},{title:()=>ue(x,{class:p.e("icon-more")},{default:()=>ue(e.ellipsisIcon)}),default:()=>l})))}const r=e.closeOnClickOutside?[[kt,()=>{h.value.length&&(V.value||(h.value.forEach(e=>{return a("close",e,(t=e,g.value[t].indexPath));var t}),h.value=[]))}]]:[],i=Z(ue("ul",{key:String(e.collapse),role:"menubar",ref:c,style:P.value,class:{[d.b()]:!0,[d.m(e.mode)]:!0,[d.m("collapse")]:e.collapse}},[...n,...s]),r);return e.collapseTransition&&"vertical"===e.mode?ue(ga,()=>i):i}}});const Ia=C({index:{type:E([String,null]),default:null},route:{type:E([String,Object])},disabled:Boolean}),Ta={click:e=>F(e.index)&&de(e.indexPath)},La="ElMenuItem",Va=s({name:La});var Pa=n(s({...Va,props:Ia,emits:Ta,setup(e,{expose:t,emit:a}){const o=e;fe(o.index)&&et();const n=T(),s=I(wa),v=r("menu"),m=r("menu-item");s||Xe(La,"can not inject root menu");const{parentMenu:g,indexPath:y}=ba(n,ge(o,"index")),x=I(`${ka}${g.value.uid}`);x||Xe(La,"can not inject sub menu");const w=i(()=>o.index===s.activeIndex),k=K({index:o.index,indexPath:y,active:w}),_=()=>{o.disabled||(s.handleMenuItemClick({index:o.index,indexPath:y.value,route:o.route}),a("click",k))};return l(()=>{x.addSubMenu(k),s.addMenuItem(k)}),ie(()=>{x.removeSubMenu(k),s.removeMenuItem(k)}),t({parentMenu:g,rootMenu:s,active:w,nsMenu:v,nsMenuItem:m,handleClick:_}),(e,t)=>(c(),p("li",{class:f([h(m).b(),h(m).is("active",h(w)),h(m).is("disabled",e.disabled)]),role:"menuitem",tabindex:"-1",onClick:_},["ElMenu"===h(g).type.name&&h(s).props.collapse&&e.$slots.title?(c(),u(h(dt),{key:0,effect:h(s).props.popperEffect,placement:"right","fallback-placements":["left"],persistent:h(s).props.persistent},{content:d(()=>[b(e.$slots,"title")]),default:d(()=>[L("div",{class:f(h(v).be("tooltip","trigger"))},[b(e.$slots,"default")],2)]),_:3},8,["effect","persistent"])):(c(),p(z,{key:1},[b(e.$slots,"default"),b(e.$slots,"title")],64))],2))}}),[["__file","menu-item.vue"]]);const ja={title:String},Oa=s({name:"ElMenuItemGroup"});var Ba=n(s({...Oa,props:ja,setup(e){const t=r("menu-item-group");return(e,a)=>(c(),p("li",{class:f(h(t).b())},[L("div",{class:f(h(t).e("title"))},[e.$slots.title?b(e.$slots,"title",{key:1}):(c(),p(z,{key:0},[te(P(e.title),1)],64))],2),L("ul",null,[b(e.$slots,"default")])],2))}}),[["__file","menu-item-group.vue"]]);const $a=_(Ea,{MenuItem:Pa,MenuItemGroup:Ba,SubMenu:Sa}),Da=j(Pa);j(Ba);const Fa=j(Sa);function Na(){const e=be(),t=ye();return{currentLayout:i(()=>t.layout),isSidebarOpen:i(()=>e.sidebar.opened),isShowTagsView:i(()=>t.showTagsView),isShowSettings:i(()=>xe.showSettings),isShowLogo:i(()=>t.showAppLogo),isMobile:i(()=>"mobile"===e.device),layoutClass:i(()=>({hideSidebar:!e.sidebar.opened,openSidebar:e.sidebar.opened,mobile:"mobile"===e.device,[`layout-${t.layout}`]:!0})),toggleSidebar:function(){e.toggleSidebar()},closeSidebar:function(){e.closeSideBar()}}}function Ra(){const e=ke(),t=be(),a=we(),l=i(()=>t.activeTopMenuPath);return{routes:i(()=>a.routes),sideMenuRoutes:i(()=>a.sideMenuRoutes),activeMenu:i(()=>{const{meta:t,path:a}=e;return(null==t?void 0:t.activeMenu)?t.activeMenu:a}),activeTopMenuPath:l}}const Wa=at(s({__name:"BaseLayout",setup(e){const{layoutClass:t,isSidebarOpen:a,closeSidebar:l}=Na(),{isMobile:o}=function(){const e=be(),{width:t}=_e();return q(()=>{const a=t.value>=992,l=a?Ce.DESKTOP:Ce.MOBILE;e.toggleDevice(l),a?e.openSideBar():e.closeSideBar()}),{isDesktop:i(()=>t.value>=992),isMobile:i(()=>e.device===Ce.MOBILE)}}();return(e,n)=>{const s=Wt;return c(),p("div",{class:f(["layout",h(t)])},[h(o)&&h(a)?(c(),p("div",{key:0,class:"layout__overlay",onClick:n[0]||(n[0]=(...e)=>h(l)&&h(l)(...e))})):v("",!0),b(e.$slots,"default",{},void 0,!0),y(s,{target:".app-main"},{default:d(()=>n[1]||(n[1]=[L("div",{class:"i-svg:backtop w-6 h-6"},null,-1)])),_:1,__:[1]})],2)}}}),[["__scopeId","data-v-126c59b6"]]),Ua={class:"logo"},za=["src"],Ha={key:0,class:"title"},qa=at(s({__name:"index",props:{collapse:{type:Boolean,required:!0}},setup:e=>(t,a)=>{const l=Se("router-link");return c(),p("div",Ua,[y(k,{"enter-active-class":"animate__animated animate__fadeInLeft"},{default:d(()=>[(c(),u(l,{key:+e.collapse,class:"wh-full flex-center",to:"/"},{default:d(()=>[L("img",{src:h(lt),class:"w20px h20px"},null,8,za),e.collapse?v("",!0):(c(),p("span",Ha,P(h(xe).title),1))]),_:1}))]),_:1})])}}),[["__scopeId","data-v-8ca2532a"]]),Ga=at(s({__name:"index",props:{isActive:{type:Boolean,required:!0}},emits:["toggleClick"],setup(e,{emit:t}){const a=t,l=ye(),o=i(()=>l.layout),n=i(()=>l.theme===Ae.DARK||o.value===Me.MIX&&l.sidebarColorScheme===Ee.CLASSIC_BLUE?"hamburger--white":"");function s(){a("toggleClick")}return(t,a)=>(c(),p("div",{class:"hamburger-wrapper",onClick:s},[L("div",{class:f(["i-svg:collapse",{hamburger:!0,"is-active":e.isActive},h(n)])},null,2)]))}}),[["__scopeId","data-v-a1df49db"]]);var Ka,Ya={};var Za=function(){if(Ka)return Ya;Ka=1,Object.defineProperty(Ya,"__esModule",{value:!0}),Ya.TokenData=void 0,Ya.parse=u,Ya.compile=function(t,a={}){const{encode:l=encodeURIComponent,delimiter:o=e}=a,n=c((t instanceof i?t:u(t,a)).tokens,o,l);return function(e={}){const[t,...a]=n(e);if(a.length)throw new TypeError(`Missing parameters: ${a.join(", ")}`);return t}},Ya.match=function(a,l={}){const{decode:o=decodeURIComponent,delimiter:n=e}=l,{regexp:s,keys:r}=d(a,l),i=r.map(e=>!1===o?t:"param"===e.type?o:e=>e.split(n).map(o));return function(e){const t=s.exec(e);if(!t)return!1;const a=t[0],l=Object.create(null);for(let o=1;o<t.length;o++){if(void 0===t[o])continue;const e=r[o-1],a=i[o-1];l[e.name]=a(t[o])}return{path:a,params:l}}},Ya.pathToRegexp=d,Ya.stringify=function(e){return e.tokens.map(function e(t,o,n){if("text"===t.type)return t.value.replace(/[{}()\[\]+?!:*]/g,"\\$&");if("group"===t.type)return`{${t.tokens.map(e).join("")}}`;const s=function(e){const[t,...o]=e;return!!a.test(t)&&o.every(e=>l.test(e))}(t.name)&&function(e){return"text"!==(null==e?void 0:e.type)||!l.test(e.value[0])}(n[o+1]),r=s?t.name:JSON.stringify(t.name);if("param"===t.type)return`:${r}`;if("wildcard"===t.type)return`*${r}`;throw new TypeError(`Unexpected token: ${t}`)}).join("")};const e="/",t=e=>e,a=/^[$_\p{ID_Start}]$/u,l=/^[$\u200c\u200d\p{ID_Continue}]$/u,o="https://git.new/pathToRegexpError",n={"{":"{","}":"}","(":"(",")":")","[":"[","]":"]","+":"+","?":"?","!":"!"};function s(e){return e.replace(/[.+*?^${}()[\]|/\\]/g,"\\$&")}class r{constructor(e){this.tokens=e}peek(){if(!this._peek){const e=this.tokens.next();this._peek=e.value}return this._peek}tryConsume(e){const t=this.peek();if(t.type===e)return this._peek=void 0,t.value}consume(e){const t=this.tryConsume(e);if(void 0!==t)return t;const{type:a,index:l}=this.peek();throw new TypeError(`Unexpected ${a} at ${l}, expected ${e}: ${o}`)}text(){let e,t="";for(;e=this.tryConsume("CHAR")||this.tryConsume("ESCAPED");)t+=e;return t}}class i{constructor(e){this.tokens=e}}function u(e,s={}){const{encodePath:u=t}=s,c=new r(function*(e){const t=[...e];let s=0;function r(){let e="";if(a.test(t[++s]))for(e+=t[s];l.test(t[++s]);)e+=t[s];else if('"'===t[s]){let a=s;for(;s<t.length;){if('"'===t[++s]){s++,a=0;break}e+="\\"===t[s]?t[++s]:t[s]}if(a)throw new TypeError(`Unterminated quote at ${a}: ${o}`)}if(!e)throw new TypeError(`Missing parameter name at ${s}: ${o}`);return e}for(;s<t.length;){const e=t[s],a=n[e];if(a)yield{type:a,index:s++,value:e};else if("\\"===e)yield{type:"ESCAPED",index:s++,value:t[s++]};else if(":"===e){const e=r();yield{type:"PARAM",index:s,value:e}}else if("*"===e){const e=r();yield{type:"WILDCARD",index:s,value:e}}else yield{type:"CHAR",index:s,value:t[s++]}}return{type:"END",index:s,value:""}}(e));const d=function e(t){const a=[];for(;;){const l=c.text();l&&a.push({type:"text",value:u(l)});const o=c.tryConsume("PARAM");if(o){a.push({type:"param",name:o});continue}const n=c.tryConsume("WILDCARD");if(n){a.push({type:"wildcard",name:n});continue}if(!c.tryConsume("{"))return c.consume(t),a;a.push({type:"group",tokens:e("}")})}}("END");return new i(d)}function c(e,a,l){const o=e.map(e=>function(e,a,l){if("text"===e.type)return()=>[e.value];if("group"===e.type){const t=c(e.tokens,a,l);return e=>{const[a,...l]=t(e);return l.length?[""]:[a]}}const o=l||t;if("wildcard"===e.type&&!1!==l)return t=>{const l=t[e.name];if(null==l)return["",e.name];if(!Array.isArray(l)||0===l.length)throw new TypeError(`Expected "${e.name}" to be a non-empty array`);return[l.map((t,a)=>{if("string"!=typeof t)throw new TypeError(`Expected "${e.name}/${a}" to be a string`);return o(t)}).join(a)]};return t=>{const a=t[e.name];if(null==a)return["",e.name];if("string"!=typeof a)throw new TypeError(`Expected "${e.name}" to be a string`);return[o(a)]}}(e,a,l));return e=>{const t=[""];for(const a of o){const[l,...o]=a(e);t[0]+=l,t.push(...o)}return t}}function d(t,a={}){const{delimiter:l=e,end:o=!0,sensitive:n=!1,trailing:r=!0}=a,c=[],d=[],h=n?"":"i",m=(Array.isArray(t)?t:[t]).map(e=>e instanceof i?e:u(e,a));for(const{tokens:e}of m)for(const t of p(e,0,[])){const e=v(t,l,c);d.push(e)}let f=`^(?:${d.join("|")})`;r&&(f+=`(?:${s(l)}$)?`),f+=o?"$":`(?=${s(l)}|$)`;return{regexp:new RegExp(f,h),keys:c}}function*p(e,t,a){if(t===e.length)return yield a;const l=e[t];if("group"===l.type){const o=a.slice();for(const a of p(l.tokens,0,o))yield*p(e,t+1,a)}else a.push(l);yield*p(e,t+1,a)}function v(e,t,a){let l="",n="",r=!0;for(let i=0;i<e.length;i++){const u=e[i];if("text"!==u.type)if("param"!==u.type&&"wildcard"!==u.type);else{if(!r&&!n)throw new TypeError(`Missing text after "${u.name}": ${o}`);"param"===u.type?l+=`(${h(t,r?"":n)}+)`:l+="([\\s\\S]+)",a.push(u),n="",r=!1}else l+=s(u.value),n+=u.value,r||(r=u.value.includes(t))}return l}function h(e,t){return t.length<2?e.length<2?`[^${s(e+t)}]`:`(?:(?!${s(e)})[^${s(t)}])`:e.length<2?`(?:(?!${s(t)})[^${s(e)}])`:`(?:(?!${s(t)}|${s(e)})[\\s\\S])`}return Ya.TokenData=i,Ya}();function Qa(e){if(Ie.global.te("route."+e)){return Ie.global.t("route."+e)}return e}const Ja={key:0,class:"color-gray-400"},Xa=["onClick"],el=at(s({__name:"index",setup(e){const a=ke(),l=t([]);function o(){let e=a.matched.filter(e=>e.meta&&e.meta.title);(function(e){const t=e&&e.name;if(!t)return!1;return t.toString().trim().toLocaleLowerCase()==="Dashboard".toLocaleLowerCase()})(e[0])||(e=[{path:"/dashboard",meta:{title:"dashboard"}}].concat(e)),l.value=e.filter(e=>e.meta&&e.meta.title&&!1!==e.meta.breadcrumb)}function n(e){const{redirect:t,path:l}=e;t?Le.push(t).catch(e=>{}):Le.push((e=>{const{params:t}=a;return Za.compile(e)(t)})(l)).catch(e=>{})}return B(()=>a.path,e=>{e.startsWith("/redirect/")||o()}),Te(()=>{o()}),(e,t)=>{const a=Qt,o=Zt;return c(),u(o,{class:"flex-y-center"},{default:d(()=>[(c(!0),p(z,null,H(h(l),(e,t)=>(c(),u(a,{key:e.path},{default:d(()=>["noredirect"===e.redirect||t===h(l).length-1?(c(),p("span",Ja,P(h(Qa)(e.meta.title)),1)):(c(),p("a",{key:1,onClick:m(t=>n(e),["prevent"])},P(h(Qa)(e.meta.title)),9,Xa))]),_:2},1024))),128))]),_:1})}}}),[["__scopeId","data-v-0cb4d0ec"]]),tl={getHelp:()=>Ve({url:"/api/sys/help",method:"get",responseType:"blob"})},al={class:"strength-progress"},ll={key:0,class:"strength-desc"},ol={key:1,class:"strength-desc"},nl={slot:"footer",class:"dialog-footer"},sl=at(s({__name:"index",props:Pe({defaultPasswordUpdate:{type:Boolean,default:!1}},{modelValue:{type:Boolean,required:!0,default:!1},modelModifiers:{}}),emits:["update:modelValue"],setup(e){let a=e;const l=je(),o=l.userInfo.userId,n=Oe(e,"modelValue"),s=t();let r=K({currentPassword:"",newPassword:"",surePassword:"",upperCase:!1,lowerCase:!1,number:!1,special:!1,lengthGt8:!1});const m=K({currentPassword:[{required:!0,message:"请输入密码",trigger:"blur"},{validator:(e,t,a)=>{Be.isPasswordCorrect(o,t).then(e=>{e?a():a("当前密码不正确！")})},trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"},{validator:(e,t,a)=>{_(/[A-Z]/.test(t),"upperCase"),_(/[a-z]/.test(t),"lowerCase"),_(/[0-9]/.test(t),"number"),_(t.length>=8,"lengthGt8"),a()},trigger:"change"},{validator:(e,t,a)=>{100!=g.value&&a("新密码不符合要求，请检查密码规则"),a()},trigger:"blur"}],surePassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(e,t,a)=>{r.newPassword==r.surePassword?a():a(new Error("两次输入的密码不一致，请检查"))},trigger:"blur"}]});let g=t(0);function b(e){g.value+=e}let w=i(()=>g.value<50?"exception":g.value<100?"warning":"success"),k=i(()=>g.value<50?"exception":"warning");function _(e,t){e?r[t]||(r[t]=!0,b(25)):r[t]&&(r[t]=!1,b(-25))}function C(){s.value.resetFields(),n.value=!1}return(t,i)=>{const b=Se("Lock"),_=x,S=mt,A=vt,M=ht,E=Se("CircleCheck"),I=Se("CircleClose"),T=pt,V=gt,P=rt;return c(),p("div",null,[y(P,{title:"修改密码",modelValue:n.value,"onUpdate:modelValue":i[5]||(i[5]=e=>n.value=e),width:"30%","show-close":!1,"close-on-click-modal":!1,"close-on-press-escape":!1},{default:d(()=>[i[13]||(i[13]=L("p",{class:"tips"}," 密码需包含以下至少三种字符：大写字母、小写字母、数字、特殊字符，且长度不少于8位。 ",-1)),y(T,{ref_key:"formRef",ref:s,model:h(r),rules:m,"label-width":"100px"},{default:d(()=>[e.defaultPasswordUpdate?v("",!0):(c(),u(A,{key:0,label:"当前密码",prop:"currentPassword"},{default:d(()=>[y(S,{modelValue:h(r).currentPassword,"onUpdate:modelValue":i[0]||(i[0]=e=>h(r).currentPassword=e),clearable:"","show-password":"",placeholder:"请输入密码"},{prefix:d(()=>[y(_,null,{default:d(()=>[y(b)]),_:1})]),_:1},8,["modelValue"])]),_:1})),y(A,{label:"新密码",prop:"newPassword"},{default:d(()=>[y(S,{modelValue:h(r).newPassword,"onUpdate:modelValue":i[1]||(i[1]=e=>h(r).newPassword=e),clearable:"","show-password":"",autocomplete:"off",placeholder:"请输入新密码"},{prefix:d(()=>[y(_,null,{default:d(()=>[y(b)]),_:1})]),_:1},8,["modelValue"])]),_:1}),y(A,null,{default:d(()=>[L("div",al,[y(M,{percentage:h(g),"show-text":!1,status:h(w)},null,8,["percentage","status"]),y(_,null,{default:d(()=>[Z(y(E,{style:{color:"green"}},null,512),[[J,h(g)&&100==h(g)]]),Z(y(I,{class:f(h(k))},null,8,["class"]),[[J,h(g)&&100!=h(g)]])]),_:1})]),100==h(g)?(c(),p("div",ll,i[6]||(i[6]=[L("span",{class:"success"},"密码符合要求",-1)]))):(c(),p("div",ol,[L("span",{class:f({success:h(r).upperCase})},"包含大写字母",2),i[7]||(i[7]=te("   ")),L("span",{class:f({success:h(r).lowerCase})},"包含小写字母",2),i[8]||(i[8]=te("   ")),L("span",{class:f({success:h(r).number})},"包含数字",2),i[9]||(i[9]=te("   ")),L("span",{class:f({success:h(r).special})},"包含特殊字符",2),i[10]||(i[10]=te("   ")),L("span",{class:f({success:h(r).lengthGt8})},"大于8位",2)]))]),_:1}),y(A,{label:"确认新密码",prop:"surePassword"},{default:d(()=>[y(S,{modelValue:h(r).surePassword,"onUpdate:modelValue":i[2]||(i[2]=e=>h(r).surePassword=e),clearable:"","show-password":"",placeholder:"请确认新密码"},{prefix:d(()=>[y(_,null,{default:d(()=>[y(b)]),_:1})]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model","rules"]),L("span",nl,[e.defaultPasswordUpdate?v("",!0):(c(),u(V,{key:0,onClick:i[3]||(i[3]=e=>C())},{default:d(()=>i[11]||(i[11]=[te("暂不修改")])),_:1,__:[11]})),y(V,{type:"primary",onClick:i[4]||(i[4]=e=>{s.value.validate(e=>{e&&Be.resetPassword(o,r.newPassword).then(()=>{C(),a.defaultPasswordUpdate&&l.getUserInfo()})})})},{default:d(()=>i[12]||(i[12]=[te("确认修改")])),_:1,__:[12]})])]),_:1,__:[13]},8,["modelValue"])])}}}),[["__scopeId","data-v-59b991df"]]),rl={class:"navbar-actions__item"},il={class:"user-profile"},ul=["src"],cl={class:"user-profile__name"},dl={slot:"footer",class:"dialog-footer"},pl=at(s({__name:"NavbarActions",setup(e){const{t:a}=$e(),o=be(),n=ye(),s=je(),r=ke(),u=Fe();i(()=>o.device===Ce.DESKTOP);const m=i(()=>{const{theme:e,sidebarColorScheme:t,layout:a}=n;return e===Ae.DARK?"navbar-actions--white-text":e!==Ae.LIGHT||a!==Me.TOP&&a!==Me.MIX?"navbar-actions--dark-text":t===Ee.CLASSIC_BLUE?"navbar-actions--white-text":"navbar-actions--dark-text"});let g=t(!1);function b(){g.value=!0}l(()=>{s.userInfo.isDefaultPwd&&b()});let w=t(!1);function k(){yt.confirm("确定注销并退出系统吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",lockScroll:!1}).then(()=>{s.logout().then(()=>{u.push(`/login?redirect=${r.fullPath}`)})})}function _(){n.settingsVisible=!0}return(e,t)=>{const l=Se("Key"),o=x,n=st,r=Se("QuestionFilled"),i=Se("InfoFilled"),u=Se("Back"),C=nt,S=ot,A=ut,M=it,E=gt,I=rt;return c(),p("div",{class:f(["navbar-actions",h(m)])},[v("",!0),L("div",rl,[y(S,{trigger:"click"},{dropdown:d(()=>[y(C,null,{default:d(()=>[y(n,{onClick:b},{default:d(()=>[y(o,null,{default:d(()=>[y(l)]),_:1}),te(" "+P(h(a)("navbar.changePwd")),1)]),_:1}),y(n,{onClick:t[0]||(t[0]=e=>{tl.getHelp().then(e=>{const t=URL.createObjectURL(new Blob([e.data],{type:"application/pdf"}));window.open(t,"_blank"),URL.revokeObjectURL(t)})})},{default:d(()=>[y(o,null,{default:d(()=>[y(r)]),_:1}),te(" "+P(h(a)("navbar.help")),1)]),_:1}),y(n,{onClick:t[1]||(t[1]=e=>{w.value=!0})},{default:d(()=>[y(o,null,{default:d(()=>[y(i)]),_:1}),te(" "+P(h(a)("navbar.sysInfo")),1)]),_:1}),y(n,{onClick:k},{default:d(()=>[y(o,null,{default:d(()=>[y(u)]),_:1}),te(" "+P(h(a)("navbar.logout")),1)]),_:1})]),_:1})]),default:d(()=>[L("div",il,[L("img",{class:"user-profile__avatar",src:h(s).userInfo.avatar},null,8,ul),L("span",cl,P(h(s).userInfo.username),1)])]),_:1})]),h(xe).showSettings?(c(),p("div",{key:1,class:"navbar-actions__item",onClick:_},t[5]||(t[5]=[L("div",{class:"i-svg:setting"},null,-1)]))):v("",!0),y(I,{title:"系统信息",modelValue:h(w),"onUpdate:modelValue":t[3]||(t[3]=e=>De(w)?w.value=e:w=e),width:"30%"},{default:d(()=>[t[10]||(t[10]=L("div",{class:"info-header"},[L("img",{src:"data:image/png;base64,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"}),L("span",{class:"nse-title"},"网络仿真环境")],-1)),y(M,{class:"sys-desc",direction:"horizontal",column:1},{default:d(()=>[y(A,{label:"版本号"},{default:d(()=>t[6]||(t[6]=[te("RG-NSE_V1.0.0")])),_:1,__:[6]}),y(A,{label:"证书类型"},{default:d(()=>t[7]||(t[7]=[te("正式证书")])),_:1,__:[7]}),y(A,{label:"证书有效期"},{default:d(()=>t[8]||(t[8]=[te("永久")])),_:1,__:[8]})]),_:1}),L("span",dl,[y(E,{type:"primary",onClick:t[2]||(t[2]=e=>De(w)?w.value=!1:w=!1)},{default:d(()=>t[9]||(t[9]=[te("确 定")])),_:1,__:[9]})])]),_:1,__:[10]},8,["modelValue"]),y(sl,{modelValue:h(g),"onUpdate:modelValue":t[4]||(t[4]=e=>De(g)?g.value=e:g=e),"default-password-update":h(s).userInfo.isDefaultPwd},null,8,["modelValue","default-password-update"])],2)}}}),[["__scopeId","data-v-33bdfc91"]]),vl={class:"navbar"},hl={class:"flex-y-center"},ml={class:"navbar__actions"},fl=at(s({__name:"index",setup(e){const t=be(),a=i(()=>t.sidebar.opened);function l(){t.toggleSidebar()}return(e,t)=>(c(),p("div",vl,[L("div",hl,[y(Ga,{"is-active":h(a),onToggleClick:l},null,8,["is-active"]),y(el)]),L("div",ml,[y(pl)])]))}}),[["__scopeId","data-v-b319c6f4"]]);var gl,bl;var yl=function(){if(bl)return gl;function e(e){if("string"!=typeof e)throw new TypeError("Path must be a string. Received "+JSON.stringify(e))}function t(e,t){for(var a,l="",o=0,n=-1,s=0,r=0;r<=e.length;++r){if(r<e.length)a=e.charCodeAt(r);else{if(47===a)break;a=47}if(47===a){if(n===r-1||1===s);else if(n!==r-1&&2===s){if(l.length<2||2!==o||46!==l.charCodeAt(l.length-1)||46!==l.charCodeAt(l.length-2))if(l.length>2){var i=l.lastIndexOf("/");if(i!==l.length-1){-1===i?(l="",o=0):o=(l=l.slice(0,i)).length-1-l.lastIndexOf("/"),n=r,s=0;continue}}else if(2===l.length||1===l.length){l="",o=0,n=r,s=0;continue}t&&(l.length>0?l+="/..":l="..",o=2)}else l.length>0?l+="/"+e.slice(n+1,r):l=e.slice(n+1,r),o=r-n-1;n=r,s=0}else 46===a&&-1!==s?++s:s=-1}return l}bl=1;var a={resolve:function(){for(var a,l="",o=!1,n=arguments.length-1;n>=-1&&!o;n--){var s;n>=0?s=arguments[n]:(void 0===a&&(a=process.cwd()),s=a),e(s),0!==s.length&&(l=s+"/"+l,o=47===s.charCodeAt(0))}return l=t(l,!o),o?l.length>0?"/"+l:"/":l.length>0?l:"."},normalize:function(a){if(e(a),0===a.length)return".";var l=47===a.charCodeAt(0),o=47===a.charCodeAt(a.length-1);return 0!==(a=t(a,!l)).length||l||(a="."),a.length>0&&o&&(a+="/"),l?"/"+a:a},isAbsolute:function(t){return e(t),t.length>0&&47===t.charCodeAt(0)},join:function(){if(0===arguments.length)return".";for(var t,l=0;l<arguments.length;++l){var o=arguments[l];e(o),o.length>0&&(void 0===t?t=o:t+="/"+o)}return void 0===t?".":a.normalize(t)},relative:function(t,l){if(e(t),e(l),t===l)return"";if((t=a.resolve(t))===(l=a.resolve(l)))return"";for(var o=1;o<t.length&&47===t.charCodeAt(o);++o);for(var n=t.length,s=n-o,r=1;r<l.length&&47===l.charCodeAt(r);++r);for(var i=l.length-r,u=s<i?s:i,c=-1,d=0;d<=u;++d){if(d===u){if(i>u){if(47===l.charCodeAt(r+d))return l.slice(r+d+1);if(0===d)return l.slice(r+d)}else s>u&&(47===t.charCodeAt(o+d)?c=d:0===d&&(c=0));break}var p=t.charCodeAt(o+d);if(p!==l.charCodeAt(r+d))break;47===p&&(c=d)}var v="";for(d=o+c+1;d<=n;++d)d!==n&&47!==t.charCodeAt(d)||(0===v.length?v+="..":v+="/..");return v.length>0?v+l.slice(r+c):(r+=c,47===l.charCodeAt(r)&&++r,l.slice(r))},_makeLong:function(e){return e},dirname:function(t){if(e(t),0===t.length)return".";for(var a=t.charCodeAt(0),l=47===a,o=-1,n=!0,s=t.length-1;s>=1;--s)if(47===(a=t.charCodeAt(s))){if(!n){o=s;break}}else n=!1;return-1===o?l?"/":".":l&&1===o?"//":t.slice(0,o)},basename:function(t,a){if(void 0!==a&&"string"!=typeof a)throw new TypeError('"ext" argument must be a string');e(t);var l,o=0,n=-1,s=!0;if(void 0!==a&&a.length>0&&a.length<=t.length){if(a.length===t.length&&a===t)return"";var r=a.length-1,i=-1;for(l=t.length-1;l>=0;--l){var u=t.charCodeAt(l);if(47===u){if(!s){o=l+1;break}}else-1===i&&(s=!1,i=l+1),r>=0&&(u===a.charCodeAt(r)?-1===--r&&(n=l):(r=-1,n=i))}return o===n?n=i:-1===n&&(n=t.length),t.slice(o,n)}for(l=t.length-1;l>=0;--l)if(47===t.charCodeAt(l)){if(!s){o=l+1;break}}else-1===n&&(s=!1,n=l+1);return-1===n?"":t.slice(o,n)},extname:function(t){e(t);for(var a=-1,l=0,o=-1,n=!0,s=0,r=t.length-1;r>=0;--r){var i=t.charCodeAt(r);if(47!==i)-1===o&&(n=!1,o=r+1),46===i?-1===a?a=r:1!==s&&(s=1):-1!==a&&(s=-1);else if(!n){l=r+1;break}}return-1===a||-1===o||0===s||1===s&&a===o-1&&a===l+1?"":t.slice(a,o)},format:function(e){if(null===e||"object"!=typeof e)throw new TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return function(e,t){var a=t.dir||t.root,l=t.base||(t.name||"")+(t.ext||"");return a?a===t.root?a+l:a+e+l:l}("/",e)},parse:function(t){e(t);var a={root:"",dir:"",base:"",ext:"",name:""};if(0===t.length)return a;var l,o=t.charCodeAt(0),n=47===o;n?(a.root="/",l=1):l=0;for(var s=-1,r=0,i=-1,u=!0,c=t.length-1,d=0;c>=l;--c)if(47!==(o=t.charCodeAt(c)))-1===i&&(u=!1,i=c+1),46===o?-1===s?s=c:1!==d&&(d=1):-1!==s&&(d=-1);else if(!u){r=c+1;break}return-1===s||-1===i||0===d||1===d&&s===i-1&&s===r+1?-1!==i&&(a.base=a.name=0===r&&n?t.slice(1,i):t.slice(r,i)):(0===r&&n?(a.name=t.slice(1,s),a.base=t.slice(1,i)):(a.name=t.slice(r,s),a.base=t.slice(r,i)),a.ext=t.slice(s,i)),r>0?a.dir=t.slice(0,r-1):n&&(a.dir="/"),a},sep:"/",delimiter:":",win32:null,posix:null};return a.posix=a,gl=a}();const xl=Ne(yl),wl={class:"tags-container"},kl={class:"tag-text"},_l=["onClick"],Cl=at(s({__name:"index",setup(e){const a=T(),o=null==a?void 0:a.proxy,n=Fe(),s=ke(),r=we(),b=Re(),x=ye(),{visitedViews:w}=We(b),k=i(()=>x.layout),_=t(null),C=K({visible:!1,x:0,y:0}),S=t(),A=i(()=>{const e=new Map;return w.value.forEach(t=>{e.set(t.path,t)}),e}),M=i(()=>{var e;return!!_.value&&("/dashboard"===_.value.path||_.value.fullPath===(null==(e=w.value[1])?void 0:e.fullPath))}),E=i(()=>{var e;return!!_.value&&_.value.fullPath===(null==(e=w.value[w.value.length-1])?void 0:e.fullPath)}),I=()=>{((e,t="/")=>{const a=[],l=(e,t)=>{e.forEach(e=>{var o,n;const s=yl.resolve(t,e.path);(null==(o=e.meta)?void 0:o.affix)&&a.push({path:s,fullPath:s,name:String(e.name||""),title:e.meta.title||"no-name",affix:!0,keepAlive:e.meta.keepAlive||!1}),(null==(n=e.children)?void 0:n.length)&&l(e.children,s)})};return l(e,t),a})(r.routes).forEach(e=>{e.name&&b.addVisitedView(e)})},V=()=>{ae(()=>{var e,t,a;const l=A.value.get(s.path);l&&l.fullPath!==s.fullPath&&b.updateVisitedView({name:s.name,title:(null==(e=s.meta)?void 0:e.title)||"",path:s.path,fullPath:s.fullPath,affix:(null==(t=s.meta)?void 0:t.affix)||!1,keepAlive:(null==(a=s.meta)?void 0:a.keepAlive)||!1,query:s.query})})},j=()=>{C.visible=!1},O=e=>{var t;j();const a=null==(t=S.value)?void 0:t.wrapRef;if(!a)return;if(!(a.scrollWidth>a.clientWidth))return;const l=e.deltaY||-e.wheelDelta||0,o=a.scrollLeft+l;S.value.setScrollLeft(o)},$=e=>{e&&b.delView(e).then(t=>{b.isActive(e)&&b.toLastView(t.visitedViews,e)})},D=()=>{_.value&&b.delLeftViews(_.value).then(e=>{e.visitedViews.some(e=>e.path===s.path)||b.toLastView(e.visitedViews)})},F=()=>{_.value&&b.delRightViews(_.value).then(e=>{e.visitedViews.some(e=>e.path===s.path)||b.toLastView(e.visitedViews)})},N=()=>{_.value&&(n.push(_.value),b.delOtherViews(_.value).then(()=>{V()}))};return B(s,()=>{var e;(null==(e=s.meta)?void 0:e.title)&&b.addView({name:s.name,title:s.meta.title,path:s.path,fullPath:s.fullPath,affix:s.meta.affix||!1,keepAlive:s.meta.keepAlive||!1,query:s.query}),V()},{immediate:!0}),l(()=>{I()}),(()=>{const e=()=>{j()};q(()=>{C.visible?document.addEventListener("click",e):document.removeEventListener("click",e)}),ie(()=>{document.removeEventListener("click",e)})})(),(e,t)=>{var a;const l=Se("router-link"),s=Je;return c(),p("div",wl,[y(s,{ref_key:"scrollbarRef",ref:S,class:"scroll-container",onWheel:O},{default:d(()=>[(c(!0),p(z,null,H(h(w),e=>(c(),u(l,{ref_for:!0,ref:"tagRef",key:e.fullPath,class:f(["tags-item",{active:h(b).isActive(e)}]),to:{path:e.path,query:e.query},onMouseup:m(t=>(e=>{e.affix||$(e)})(e),["middle"]),onContextmenu:m(t=>((e,t)=>{const a=null==o?void 0:o.$el.getBoundingClientRect(),l=(null==a?void 0:a.left)||0,n=((null==o?void 0:o.$el.offsetWidth)||0)-105,s=t.clientX-l+15;C.x=Math.min(s,n),C.y=k.value===Me.MIX?t.clientY-50:t.clientY,C.visible=!0,_.value=e})(e,t),["prevent"])},{default:d(()=>[L("span",kl,P(h(Qa)(e.title)),1),e.affix?v("",!0):(c(),p("span",{key:0,class:"tag-close-btn",onClick:m(t=>$(e),["prevent","stop"])}," × ",8,_l))]),_:2},1032,["class","to","onMouseup","onContextmenu"]))),128))]),_:1},512),Z(L("ul",{class:"contextmenu",style:g({left:h(C).x+"px",top:h(C).y+"px"})},[L("li",{onClick:t[0]||(t[0]=e=>{var t;(t=h(_))&&(b.delCachedView(t),ae(()=>{n.replace("/redirect"+t.fullPath)}))})},t[3]||(t[3]=[L("div",{class:"i-svg:refresh"},null,-1),te(" 刷新 ")])),(null==(a=h(_))?void 0:a.affix)?v("",!0):(c(),p("li",{key:0,onClick:t[1]||(t[1]=e=>$(h(_)))},t[4]||(t[4]=[L("div",{class:"i-svg:close"},null,-1),te(" 关闭 ")]))),L("li",{onClick:N},t[5]||(t[5]=[L("div",{class:"i-svg:close_other"},null,-1),te(" 关闭其它 ")])),h(M)?v("",!0):(c(),p("li",{key:1,onClick:D},t[6]||(t[6]=[L("div",{class:"i-svg:close_left"},null,-1),te(" 关闭左侧 ")]))),h(E)?v("",!0):(c(),p("li",{key:2,onClick:F},t[7]||(t[7]=[L("div",{class:"i-svg:close_right"},null,-1),te(" 关闭右侧 ")]))),L("li",{onClick:t[2]||(t[2]=e=>{return t=h(_),void b.delAllViews().then(e=>{b.toLastView(e.visitedViews,t||void 0)});var t})},t[8]||(t[8]=[L("div",{class:"i-svg:close_all"},null,-1),te(" 关闭所有 ")]))],4),[[J,h(C).visible]])])}}}),[["__scopeId","data-v-b39a0aba"]]),Sl={"sidebar-width":"210px","navbar-height":"50px","tags-view-height":"34px","menu-background":"var(--menu-background)","menu-text":"var(--menu-text)","menu-active-text":"var(--menu-active-text)","menu-hover":"var(--menu-hover)","sidebar-color-blue":"_sidebar-color-blue_1h0dk_16",dark:"_dark_1h0dk_26"},Al=at(s({__name:"index",setup(e){const t=i(()=>Re().cachedViews),a=i(()=>ye().showTagsView?`calc(100vh - ${Sl["navbar-height"]} - ${Sl["tags-view-height"]})`:`calc(100vh - ${Sl["navbar-height"]})`);return(e,l)=>{const o=Se("router-view");return c(),p("section",{class:"app-main",style:g({height:h(a)})},[y(o,null,{default:d(({Component:e,route:a})=>[y(k,{"enter-active-class":"animate__animated animate__fadeIn",mode:"out-in"},{default:d(()=>[(c(),u(Ue,{include:h(t)},[(c(),u(V(e),{key:a.path}))],1032,["include"]))]),_:2},1024)]),_:1})],4)}}}),[["__scopeId","data-v-9bb96b4f"]]),Ml=s({name:"AppLink",inheritAttrs:!1,__name:"index",props:{to:{type:Object,required:!0}},setup(e){const t=e,a=i(()=>xt(t.to.path||"")),l=i(()=>a.value?"a":"router-link");return(t,o)=>{return c(),u(V(h(l)),ze(He((n=e.to,a.value?{href:n.path,target:"_blank",rel:"noopener noreferrer"}:{to:n}))),{default:d(()=>[b(t.$slots,"default")]),_:3},16);var n}}}),El={key:1,class:"i-svg:menu menu-icon"},Il={key:2,class:"menu-title ml-1"},Tl=at(s({__name:"MenuItemContent",props:{icon:{},title:{}},setup(e){const t=e,a=i(()=>{var e;return null==(e=t.icon)?void 0:e.startsWith("el-icon")}),l=i(()=>{var e;return null==(e=t.icon)?void 0:e.replace("el-icon-","")});return(e,t)=>{const o=x;return c(),p(z,null,[e.icon?(c(),p(z,{key:0},[h(a)?(c(),u(o,{key:0,class:"menu-icon"},{default:d(()=>[(c(),u(V(h(l))))]),_:1})):(c(),p("div",{key:1,class:f([`i-svg:${e.icon}`,"menu-icon"])},null,2))],64)):(c(),p("div",El)),e.title?(c(),p("span",Il,P(h(Qa)(e.title)),1)):v("",!0)],64)}}}),[["__scopeId","data-v-d391d0fe"]]),Ll={key:0},Vl=s({name:"MenuItem",inheritAttrs:!1,__name:"MenuItem",props:{item:{type:Object,required:!0},basePath:{type:String,required:!0},isNest:{type:Boolean,default:!1}},setup(e){const a=e,l=t();function o(e=[],t){const a=e.filter(e=>{var t;return!(null==(t=e.meta)?void 0:t.hidden)&&(l.value=e,!0)});return 1===a.length||0===a.length&&(l.value={...t,path:"",noShowingChildren:!0},!0)}function n(e){return xt(e)?e:xt(a.basePath)?a.basePath:xl.resolve(a.basePath,e)}return(t,a)=>{var s,r;const i=Da,m=Ml,g=Se("MenuItem",!0),b=Fa;return e.item.meta&&e.item.meta.hidden?v("",!0):(c(),p("div",Ll,[o(e.item.children,e.item)&&!(null==(s=e.item.meta)?void 0:s.alwaysShow)&&(!h(l).children||h(l).noShowingChildren)||(null==(r=e.item.meta)?void 0:r.alwaysShow)&&!e.item.children?(c(),p(z,{key:0},[h(l).meta?(c(),u(m,{key:0,to:{path:n(h(l).path),query:h(l).meta.params}},{default:d(()=>[y(i,{index:n(h(l).path),class:f({"submenu-title-noDropdown":!e.isNest})},{default:d(()=>{var t;return[h(l).meta?(c(),u(Tl,{key:0,icon:h(l).meta.icon||(null==(t=e.item.meta)?void 0:t.icon),title:h(l).meta.title},null,8,["icon","title"])):v("",!0)]}),_:1},8,["index","class"])]),_:1},8,["to"])):v("",!0)],64)):(c(),u(b,{key:1,index:n(e.item.path),"data-path":e.item.path,teleported:""},{title:d(()=>[e.item.meta?(c(),u(Tl,{key:0,icon:e.item.meta.icon,title:e.item.meta.title},null,8,["icon","title"])):v("",!0)]),default:d(()=>[(c(!0),p(z,null,H(e.item.children,e=>(c(),u(g,{key:e.path,"is-nest":!0,item:e,"base-path":n(e.path)},null,8,["item","base-path"]))),128))]),_:1},8,["index","data-path"]))]))}}}),Pl=s({__name:"BasicMenu",props:{data:{type:Array,default:()=>[]},basePath:{type:String,required:!0,example:"/system"},menuMode:{type:String,default:"vertical",validator:e=>["vertical","horizontal"].includes(e)}},setup(e){const a=e,o=t(),n=ye(),s=be(),r=ke(),v=t([]),m=i(()=>n.theme),f=i(()=>n.sidebarColorScheme),g=i(()=>{const e="dark"===m.value||f.value===Ee.CLASSIC_BLUE;return{backgroundColor:e?Sl["menu-background"]:void 0,textColor:e?Sl["menu-text"]:void 0,activeTextColor:e?Sl["menu-active-text"]:void 0}}),b=i(()=>{const{meta:e,path:t}=r;return(null==e?void 0:e.activeMenu)&&"string"==typeof e.activeMenu?e.activeMenu:t});const y=e=>{v.value.push(e)},x=e=>{v.value=v.value.filter(t=>t!==e)};function w(){var e;(null==(e=o.value)?void 0:e.$el)&&ae(()=>{var e;try{const t=null==(e=o.value)?void 0:e.$el;if(!t)return;const l=t.querySelectorAll(".el-sub-menu");l.forEach(e=>{e.classList.remove("has-active-child")});const n=t.querySelector(".el-menu-item.is-active");if(n){let e=n.parentElement;for(;e&&e!==t;)e.classList.contains("el-sub-menu")&&e.classList.add("has-active-child"),e=e.parentElement}else if("horizontal"===a.menuMode){const e=b.value;l.forEach(t=>{var a;const l=t,o=l.getAttribute("data-path")||(null==(a=l.querySelector(".el-sub-menu__title"))?void 0:a.getAttribute("data-path"));o&&e.startsWith(o)&&l.classList.add("has-active-child")})}}catch(t){}})}return B(()=>a.menuMode,e=>{"horizontal"===e&&o.value&&v.value.forEach(e=>o.value.close(e))}),B(()=>b.value,()=>{ae(()=>{w()})},{immediate:!0}),B(()=>r.path,()=>{ae(()=>{w()})}),l(()=>{w()}),(t,l)=>{const n=$a;return c(),u(n,{ref_key:"menuRef",ref:o,"default-active":h(b),collapse:!h(s).sidebar.opened,"background-color":h(g).backgroundColor,"text-color":h(g).textColor,"active-text-color":h(g).activeTextColor,"popper-effect":h(m),"unique-opened":!1,"collapse-transition":!1,mode:e.menuMode,onOpen:y,onClose:x},{default:d(()=>[(c(!0),p(z,null,H(e.data,e=>{return c(),u(Vl,{key:e.path,item:e,"base-path":(t=e.path,xt(t)?t:xt(a.basePath)?a.basePath:a.basePath&&""!==a.basePath?xl.resolve(a.basePath,t):t)},null,8,["item","base-path"]);var t}),128))]),_:1},8,["default-active","collapse","background-color","text-color","active-text-color","popper-effect","mode"])}}}),jl=at(s({__name:"LeftLayout",setup(e){const{isShowTagsView:t,isShowLogo:a,isSidebarOpen:l}=Na(),{routes:o}=Ra();return(e,n)=>{const s=Je;return c(),u(Wa,null,{default:d(()=>[L("div",{class:f(["layout__sidebar",{"layout__sidebar--collapsed":!h(l)}])},[L("div",{class:f([{"has-logo":h(a)},"layout-sidebar"])},[h(a)?(c(),u(qa,{key:0,collapse:!h(l)},null,8,["collapse"])):v("",!0),y(s,null,{default:d(()=>[y(Pl,{data:h(o),"base-path":""},null,8,["data"])]),_:1})],2)],2),L("div",{class:f([{hasTagsView:h(t),"layout__main--collapsed":!h(l)},"layout__main"])},[y(fl),h(t)?(c(),u(Cl,{key:0})):v("",!0),y(Al)],2)]),_:1})}}}),[["__scopeId","data-v-d62c8d16"]]),Ol={class:"layout__header"},Bl={class:"layout__header-left"},$l={class:"layout__header-right"},Dl=at(s({__name:"TopLayout",setup(e){const{isShowTagsView:t,isShowLogo:a}=Na(),{routes:l}=Ra(),{width:o}=_e(),n=i(()=>o.value<768);return(e,o)=>(c(),u(Wa,null,{default:d(()=>[L("div",Ol,[L("div",Bl,[h(a)?(c(),u(qa,{key:0,collapse:n.value},null,8,["collapse"])):v("",!0),y(Pl,{data:h(l),"menu-mode":"horizontal","base-path":""},null,8,["data"])]),L("div",$l,[y(pl)])]),L("div",{class:f([{hasTagsView:h(t)},"layout__main"])},[h(t)?(c(),u(Cl,{key:0})):v("",!0),y(Al)],2)]),_:1}))}}),[["__scopeId","data-v-ad4d85f1"]]),Fl=at(s({name:"MixTopMenu",__name:"MixTopMenu",setup(e){const a=Fe(),o=be(),n=we(),s=ye(),r=i(()=>s.theme),m=i(()=>s.sidebarColorScheme),f=t([]),g=i(()=>f.value.map(e=>{var t,a,l,o,n;if((null==(t=e.meta)?void 0:t.alwaysShow)||!e.children||0===e.children.length)return e;const s=e.children.filter(e=>{var t;return!(null==(t=e.meta)?void 0:t.hidden)});if(1===s.length){const t=s[0];return{...e,meta:{...e.meta,title:(null==(a=t.meta)?void 0:a.title)||(null==(l=e.meta)?void 0:l.title),icon:(null==(o=t.meta)?void 0:o.icon)||(null==(n=e.meta)?void 0:n.icon)}}}return e})),b=e=>{y(e)},y=(e,t=!1)=>{e!==o.activeTopMenuPath&&(o.activeTopMenu(e),n.updateSideMenu(e)),t||x(n.sideMenuRoutes)},x=e=>{var t;if(0===e.length)return;const[l]=e;l.children&&l.children.length>0?x(l.children):l.name&&a.push({name:l.name,query:"object"==typeof(null==(t=l.meta)?void 0:t.params)?l.meta.params:void 0})},w=i(()=>o.activeTopMenuPath);return l(()=>{var e;f.value=n.routes.filter(e=>!e.meta||!e.meta.hidden);const t=ke().path.split("/").filter(Boolean).length>1&&(null==(e=ke().path.match(/^\/[^/]+/))?void 0:e[0])||"/";o.activeTopMenu(t),n.updateSideMenu(t)}),B(()=>a.currentRoute.value.path,e=>{var t;if(e){const a=e.split("/").filter(Boolean).length>1&&(null==(t=e.match(/^\/[^/]+/))?void 0:t[0])||"/";y(a,!0)}}),(e,t)=>{const a=Da,l=$a;return c(),u(l,{mode:"horizontal","default-active":h(w),"background-color":"dark"===h(r)||h(m)===h(Ee).CLASSIC_BLUE?h(Sl)["menu-background"]:void 0,"text-color":"dark"===h(r)||h(m)===h(Ee).CLASSIC_BLUE?h(Sl)["menu-text"]:void 0,"active-text-color":"dark"===h(r)||h(m)===h(Ee).CLASSIC_BLUE?h(Sl)["menu-active-text"]:void 0,onSelect:b},{default:d(()=>[(c(!0),p(z,null,H(h(g),e=>(c(),u(a,{key:e.path,index:e.path},{default:d(()=>[e.meta?(c(),u(Tl,{key:0,icon:e.meta.icon,title:e.meta.title},null,8,["icon","title"])):v("",!0)]),_:2},1032,["index"]))),128))]),_:1},8,["default-active","background-color","text-color","active-text-color"])}}}),[["__scopeId","data-v-f38ef81a"]]),Nl={class:"layout__header"},Rl={class:"layout__header-content"},Wl={key:0,class:"layout__header-logo"},Ul={class:"layout__header-menu"},zl={class:"layout__header-actions"},Hl={class:"layout__container"},ql={class:"layout__sidebar-toggle"},Gl=at(s({__name:"MixLayout",setup(e){const t=ke(),{isShowTagsView:a,isShowLogo:l,isSidebarOpen:o,toggleSidebar:n}=Na(),{sideMenuRoutes:s,activeTopMenuPath:r}=Ra(),{width:m}=_e(),g=i(()=>m.value<768),b=i(()=>{const{meta:e,path:a}=t;return(null==e?void 0:e.activeMenu)&&"string"==typeof e.activeMenu?e.activeMenu:a});return B(()=>t.path,e=>{var t;const a=e.split("/").filter(Boolean).length>1&&(null==(t=e.match(/^\/[^/]+/))?void 0:t[0])||"/";if(e.startsWith(r.value));else if(a!==r.value){const e=be(),t=we();e.activeTopMenu(a),t.updateSideMenu(a)}},{immediate:!0}),(e,t)=>{const i=$a,m=Je;return c(),u(Wa,null,{default:d(()=>[L("div",Nl,[L("div",Rl,[h(l)?(c(),p("div",Wl,[y(qa,{collapse:g.value},null,8,["collapse"])])):v("",!0),L("div",Ul,[y(Fl)]),L("div",zl,[y(pl)])])]),L("div",Hl,[L("div",{class:f(["layout__sidebar--left",{"layout__sidebar--collapsed":!h(o)}])},[y(m,null,{default:d(()=>[y(i,{"default-active":b.value,collapse:!h(o),"collapse-transition":!1,"unique-opened":!1,"background-color":h(Sl)["menu-background"],"text-color":h(Sl)["menu-text"],"active-text-color":h(Sl)["menu-active-text"]},{default:d(()=>[(c(!0),p(z,null,H(h(s),e=>{return c(),u(Vl,{key:e.path,item:e,"base-path":(t=e.path,xt(t)?t:t.startsWith("/")?r.value+t:`${r.value}/${t}`)},null,8,["item","base-path"]);var t}),128))]),_:1},8,["default-active","collapse","background-color","text-color","active-text-color"])]),_:1}),L("div",ql,[y(Ga,{"is-active":h(o),onToggleClick:h(n)},null,8,["is-active","onToggleClick"])])],2),L("div",{class:f([{hasTagsView:h(a)},"layout__main"])},[h(a)?(c(),u(Cl,{key:0})):v("",!0),y(Al)],2)])]),_:1})}}}),[["__scopeId","data-v-30c6ab4b"]]),Kl={class:"settings-content"},Yl={class:"config-section"},Zl={class:"flex-center"},Ql={class:"config-section"},Jl={class:"config-item flex-x-between"},Xl={class:"text-xs"},eo={class:"config-item flex-x-between"},to={class:"text-xs"},ao={class:"config-item flex-x-between"},lo={class:"text-xs"},oo={class:"config-item flex-x-between"},no={class:"text-xs"},so={key:0,class:"config-item flex-x-between"},ro={class:"text-xs"},io={class:"config-section"},uo={class:"layout-select"},co={class:"layout-grid"},po=["onClick","onKeydown"],vo={class:"layout-preview"},ho={key:0,class:"layout-header"},mo={key:1,class:"layout-sidebar"},fo={class:"layout-name"},go={key:0,class:"layout-check"},bo={class:"action-footer"},yo={class:"action-card"},xo={class:"action-buttons"},wo=at(s({__name:"index",setup(e){const{t:a}=$e(),l=qe(Ge),o=qe(Ke),n=t(!1),s=t(!1),r=[{value:Me.LEFT,label:a("settings.leftLayout"),className:"left"},{value:Me.TOP,label:a("settings.topLayout"),className:"top"},{value:Me.MIX,label:a("settings.mixLayout"),className:"mix"}],m=Ye,g=ye(),b=t(g.theme===Ae.DARK),w=t(g.sidebarColorScheme),k=i({get:()=>g.themeColor,set:e=>g.updateThemeColor(e)}),_=i({get:()=>g.settingsVisible,set:e=>g.settingsVisible=e}),C=e=>{g.updateTheme(e?Ae.DARK:Ae.LIGHT)},S=e=>{g.updateSidebarColorScheme(e)},A=e=>{g.layout!==e&&g.updateLayout(e)},M=async()=>{try{n.value=!0;const e=I();await navigator.clipboard.writeText(e),Qe.success({message:a("settings.copySuccess"),duration:3e3})}catch{Qe.error("复制配置失败")}finally{n.value=!1}},E=async()=>{s.value=!0;try{g.resetSettings(),b.value=g.theme===Ae.DARK,w.value=g.sidebarColorScheme,Qe.success(a("settings.resetSuccess"))}catch{Qe.error("重置配置失败")}finally{s.value=!1}},I=()=>`const defaultSettings: AppSettings = {\n  title: ${"pkg.name"},\n  version: ${"pkg.version"},\n  showSettings: ${!0},\n  showTagsView: ${g.showTagsView},\n  showAppLogo: ${g.showAppLogo},\n  layout: ${`LayoutMode.${g.layout.toUpperCase()}`},\n  theme: ${`ThemeMode.${g.theme.toUpperCase()}`},\n  size: ${"ComponentSize.DEFAULT"},\n  language: ${"LanguageEnum.ZH_CN"},\n  themeColor: ${`"${g.themeColor}"`},\n  showWatermark: ${g.showWatermark},\n  watermarkContent: ${"pkg.name"},\n  sidebarColorScheme: ${`SidebarColor.${g.sidebarColorScheme.toUpperCase().replace("-","_")}`},\n};`,T=()=>{g.settingsVisible=!1};return(e,t)=>{const i=bt,I=Mt,V=pa,j=St,O=At,B=x,$=dt,D=gt,F=Ct;return c(),u(F,{modelValue:h(_),"onUpdate:modelValue":t[6]||(t[6]=e=>De(_)?_.value=e:null),size:"380",title:h(a)("settings.project"),"before-close":T,class:"settings-drawer"},{default:d(()=>[L("div",Kl,[L("section",Yl,[y(i,null,{default:d(()=>[te(P(h(a)("settings.theme")),1)]),_:1}),L("div",Zl,[y(I,{modelValue:h(b),"onUpdate:modelValue":t[0]||(t[0]=e=>De(b)?b.value=e:null),"active-icon":"Moon","inactive-icon":"Sunny",class:"theme-switch",onChange:C},null,8,["modelValue"])])]),L("section",Ql,[y(i,null,{default:d(()=>[te(P(h(a)("settings.interface")),1)]),_:1}),L("div",Jl,[L("span",Xl,P(h(a)("settings.themeColor")),1),y(V,{modelValue:h(k),"onUpdate:modelValue":t[1]||(t[1]=e=>De(k)?k.value=e:null),predefine:h(m),"popper-class":"theme-picker-dropdown"},null,8,["modelValue","predefine"])]),L("div",eo,[L("span",to,P(h(a)("settings.showTagsView")),1),y(I,{modelValue:h(g).showTagsView,"onUpdate:modelValue":t[2]||(t[2]=e=>h(g).showTagsView=e)},null,8,["modelValue"])]),L("div",ao,[L("span",lo,P(h(a)("settings.showAppLogo")),1),y(I,{modelValue:h(g).showAppLogo,"onUpdate:modelValue":t[3]||(t[3]=e=>h(g).showAppLogo=e)},null,8,["modelValue"])]),L("div",oo,[L("span",no,P(h(a)("settings.showWatermark")),1),y(I,{modelValue:h(g).showWatermark,"onUpdate:modelValue":t[4]||(t[4]=e=>h(g).showWatermark=e)},null,8,["modelValue"])]),h(b)?v("",!0):(c(),p("div",so,[L("span",ro,P(h(a)("settings.sidebarColorScheme")),1),y(O,{modelValue:h(w),"onUpdate:modelValue":t[5]||(t[5]=e=>De(w)?w.value=e:null),onChange:S},{default:d(()=>[y(j,{value:h(Ee).CLASSIC_BLUE},{default:d(()=>[te(P(h(a)("settings.classicBlue")),1)]),_:1},8,["value"]),y(j,{value:h(Ee).MINIMAL_WHITE},{default:d(()=>[te(P(h(a)("settings.minimalWhite")),1)]),_:1},8,["value"])]),_:1},8,["modelValue"])]))]),L("section",io,[y(i,null,{default:d(()=>[te(P(h(a)("settings.navigation")),1)]),_:1}),L("div",uo,[L("div",co,[(c(),p(z,null,H(r,e=>y($,{key:e.value,content:e.label,placement:"bottom"},{default:d(()=>[L("div",{role:"button",tabindex:"0",class:f(["layout-item",e.className,{"is-active":h(g).layout===e.value}]),onClick:t=>A(e.value),onKeydown:ee(t=>A(e.value),["enter","space"])},[L("div",vo,[e.value!==h(Me).LEFT?(c(),p("div",ho)):v("",!0),e.value!==h(Me).TOP?(c(),p("div",mo)):v("",!0),t[7]||(t[7]=L("div",{class:"layout-main"},null,-1))]),L("div",fo,P(e.label),1),h(g).layout===e.value?(c(),p("div",go,[y(B,null,{default:d(()=>[y(h(Ze))]),_:1})])):v("",!0)],42,po)]),_:2},1032,["content"])),64))])])])]),L("div",bo,[t[8]||(t[8]=L("div",{class:"action-divider"},null,-1)),L("div",yo,[L("div",xo,[y($,{content:"复制配置将生成当前设置的代码，覆盖 src/settings.ts 下的 defaultSettings 变量",placement:"top"},{default:d(()=>[y(D,{type:"primary",size:"default",icon:h(l),loading:h(n),class:"action-btn",onClick:M},{default:d(()=>[te(P(h(n)?"复制中...":h(a)("settings.copyConfig")),1)]),_:1},8,["icon","loading"])]),_:1}),y($,{content:"重置将恢复所有设置为默认值",placement:"top"},{default:d(()=>[y(D,{type:"warning",size:"default",icon:h(o),loading:h(s),class:"action-btn",onClick:E},{default:d(()=>[te(P(h(s)?"重置中...":h(a)("settings.resetConfig")),1)]),_:1},8,["icon","loading"])]),_:1})])])])]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-a5d27093"]]),ko={class:"layout-wrapper"},_o=at(s({__name:"index",setup(e){const{currentLayout:t}=Na(),a=i(()=>{switch(t.value){case Me.TOP:return Dl;case Me.MIX:return Gl;case Me.LEFT:default:return jl}}),l=i(()=>xe.showSettings);return(e,t)=>(c(),p("div",ko,[(c(),u(V(a.value))),l.value?(c(),u(wo,{key:0})):v("",!0)]))}}),[["__scopeId","data-v-9cb39833"]]);export{_o as default};
