import{u as e,r as t,b as o}from"./popper.DpZVcW1M.js";import{_ as a,d as l,l as n,r,A as i,o as s,i as p,a8 as d,y as u,t as c,z as f,v as m,K as y}from"./index.Ckm1SagX.js";var g=a(l({inheritAttrs:!1}),[["render",function(e,t,o,a,l,r){return n(e.$slots,"default")}],["__file","collection.vue"]]);var E=a(l({name:"ElCollectionItem",inheritAttrs:!1}),[["render",function(e,t,o,a,l,r){return n(e.$slots,"default")}],["__file","collection-item.vue"]]);const C="data-el-collection-item",I=e=>{const t=`El${e}Collection`,o=`${t}Item`,a=Symbol(t),l=Symbol(o),n={...g,name:t,setup(){const e=r(),t=new Map;u(a,{itemMap:t,getItems:()=>{const o=p(e);if(!o)return[];const a=Array.from(o.querySelectorAll(`[${C}]`));return[...t.values()].sort((e,t)=>a.indexOf(e.ref)-a.indexOf(t.ref))},collectionRef:e})}},c={...E,name:o,setup(e,{attrs:t}){const o=r(),n=i(a,void 0);u(l,{collectionItemRef:o}),s(()=>{const e=p(o);e&&n.itemMap.set(e,{ref:e,...t})}),d(()=>{const e=p(o);n.itemMap.delete(e)})}};return{COLLECTION_INJECTION_KEY:a,COLLECTION_ITEM_INJECTION_KEY:l,ElCollection:n,ElCollectionItem:c}},O=c({trigger:o.trigger,triggerKeys:{type:f(Array),default:()=>[y.enter,y.numpadEnter,y.space,y.down]},effect:{...e.effect,default:"light"},type:{type:f(String)},placement:{type:f(String),default:"bottom"},popperOptions:{type:f(Object),default:()=>({})},id:String,size:{type:String,default:""},splitButton:Boolean,hideOnClick:{type:Boolean,default:!0},loop:{type:Boolean,default:!0},showTimeout:{type:Number,default:150},hideTimeout:{type:Number,default:150},tabindex:{type:f([Number,String]),default:0},maxHeight:{type:f([Number,String]),default:""},popperClass:{type:String,default:""},disabled:Boolean,role:{type:String,values:t,default:"menu"},buttonProps:{type:f(Object)},teleported:e.teleported,persistent:{type:Boolean,default:!0}}),b=c({command:{type:[Object,String,Number],default:()=>({})},disabled:Boolean,divided:Boolean,textValue:String,icon:{type:m}}),N=c({onKeydown:{type:f(Function)}}),_=[y.down,y.pageDown,y.home],S=[y.up,y.pageUp,y.end],T=[..._,...S],{ElCollection:v,ElCollectionItem:L,COLLECTION_INJECTION_KEY:h,COLLECTION_ITEM_INJECTION_KEY:B}=I("Dropdown");export{C,v as E,T as F,S as L,b as a,B as b,I as c,O as d,L as e,N as f,h as g};
