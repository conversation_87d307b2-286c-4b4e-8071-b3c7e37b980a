import{b$ as e,aV as a,t as s,z as t,af as o,_ as l,d as n,aK as i,cT as r,cU as c,x as u,b as d,bE as v,r as f,cV as p,s as m,c as g,I as b,o as y,u as k,e as w,f as h,w as x,m as I,T as z,C,k as _,n as O,i as S,h as N,g as E,l as $,j as L,E as T,$ as A,Q as R,cs as j,a6 as B,a1 as P,F as Y,cW as M,cK as X,D,aM as F,cX as K,R as W,cY as V,K as q,a2 as H,q as Z,bb as G,bD as J,H as Q,ab as U,W as ee,bd as ae,aI as se,aJ as te,bB as oe,L as le,a as ne}from"./index.Ckm1SagX.js";import{E as ie}from"./focus-trap.Bd_uzvDY.js";import{E as re}from"./index.C0OsJ5su.js";import{d as ce}from"./debounce.YgIwzEIs.js";import{i as ue}from"./position.D3azAgd1.js";import{u as de}from"./index.DJHzyRe5.js";import{a as ve}from"./scroll.XdyICIdv.js";function fe(a,s,t){var o=!0,l=!0;if("function"!=typeof a)throw new TypeError("Expected a function");return e(t)&&(o="leading"in t?!!t.leading:o,l="trailing"in t?!!t.trailing:l),ce(a,s,{leading:o,maxWait:s,trailing:l})}const pe=s({urlList:{type:t(Array),default:()=>o([])},zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},hideOnClickModal:Boolean,teleported:Boolean,closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},showProgress:Boolean,crossorigin:{type:t(String)}}),me={close:()=>!0,switch:e=>a(e),rotate:e=>a(e)},ge=n({name:"ElImageViewer"});const be=Z(l(n({...ge,props:pe,emits:me,setup(e,{expose:a,emit:s}){var t;const o=e,l={CONTAIN:{name:"contain",icon:i(c)},ORIGINAL:{name:"original",icon:i(r)}};let n,Z="";const{t:G}=u(),J=d("image-viewer"),{nextZIndex:Q}=v(),U=f(),ee=f([]),ae=p(),se=f(!0),te=f(o.initialIndex),oe=m(l.CONTAIN),le=f({scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}),ne=f(null!=(t=o.zIndex)?t:Q()),ce=g(()=>{const{urlList:e}=o;return e.length<=1}),ue=g(()=>0===te.value),de=g(()=>te.value===o.urlList.length-1),ve=g(()=>o.urlList[te.value]),pe=g(()=>[J.e("btn"),J.e("prev"),J.is("disabled",!o.infinite&&ue.value)]),me=g(()=>[J.e("btn"),J.e("next"),J.is("disabled",!o.infinite&&de.value)]),ge=g(()=>{const{scale:e,deg:a,offsetX:s,offsetY:t,enableTransition:o}=le.value;let n=s/e,i=t/e;const r=a*Math.PI/180,c=Math.cos(r),u=Math.sin(r);n=n*c+i*u,i=i*c-s/e*u;const d={transform:`scale(${e}) rotate(${a}deg) translate(${n}px, ${i}px)`,transition:o?"transform .3s":""};return oe.value.name===l.CONTAIN.name&&(d.maxWidth=d.maxHeight="100%"),d}),be=g(()=>`${te.value+1} / ${o.urlList.length}`);function ye(){ae.stop(),null==n||n(),document.body.style.overflow=Z,s("close")}function ke(){se.value=!1}function we(e){se.value=!1,e.target.alt=G("el.image.error")}function he(e){if(se.value||0!==e.button||!U.value)return;le.value.enableTransition=!1;const{offsetX:a,offsetY:s}=le.value,t=e.pageX,o=e.pageY,l=fe(e=>{le.value={...le.value,offsetX:a+e.pageX-t,offsetY:s+e.pageY-o}}),n=k(document,"mousemove",l);k(document,"mouseup",()=>{n()}),e.preventDefault()}function xe(){le.value={scale:1,deg:0,offsetX:0,offsetY:0,enableTransition:!1}}function Ie(){if(se.value)return;const e=V(l),a=Object.values(l),s=oe.value.name,t=(a.findIndex(e=>e.name===s)+1)%e.length;oe.value=l[e[t]],xe()}function ze(e){const a=o.urlList.length;te.value=(e+a)%a}function Ce(){ue.value&&!o.infinite||ze(te.value-1)}function _e(){de.value&&!o.infinite||ze(te.value+1)}function Oe(e,a={}){if(se.value)return;const{minScale:t,maxScale:l}=o,{zoomRate:n,rotateDeg:i,enableTransition:r}={zoomRate:o.zoomRate,rotateDeg:90,enableTransition:!0,...a};switch(e){case"zoomOut":le.value.scale>t&&(le.value.scale=Number.parseFloat((le.value.scale/n).toFixed(3)));break;case"zoomIn":le.value.scale<l&&(le.value.scale=Number.parseFloat((le.value.scale*n).toFixed(3)));break;case"clockwise":le.value.deg+=i,s("rotate",le.value.deg);break;case"anticlockwise":le.value.deg-=i,s("rotate",le.value.deg)}le.value.enableTransition=r}function Se(e){var a;"pointer"===(null==(a=e.detail)?void 0:a.focusReason)&&e.preventDefault()}function Ne(){o.closeOnPressEscape&&ye()}function Ee(e){if(e.ctrlKey)return e.deltaY<0||e.deltaY>0?(e.preventDefault(),!1):void 0}return b(ve,()=>{H(()=>{const e=ee.value[0];(null==e?void 0:e.complete)||(se.value=!0)})}),b(te,e=>{xe(),s("switch",e)}),y(()=>{!function(){const e=fe(e=>{switch(e.code){case q.esc:o.closeOnPressEscape&&ye();break;case q.space:Ie();break;case q.left:Ce();break;case q.up:Oe("zoomIn");break;case q.right:_e();break;case q.down:Oe("zoomOut")}}),a=fe(e=>{Oe((e.deltaY||e.deltaX)<0?"zoomIn":"zoomOut",{zoomRate:o.zoomRate,enableTransition:!1})});ae.run(()=>{k(document,"keydown",e),k(document,"wheel",a)})}(),n=k("wheel",Ee,{passive:!1}),Z=document.body.style.overflow,document.body.style.overflow="hidden"}),a({setActiveItem:ze}),(e,a)=>(h(),w(S(re),{to:"body",disabled:!e.teleported},{default:x(()=>[I(z,{name:"viewer-fade",appear:""},{default:x(()=>[C("div",{ref_key:"wrapper",ref:U,tabindex:-1,class:O(S(J).e("wrapper")),style:_({zIndex:ne.value})},[I(S(ie),{loop:"",trapped:"","focus-trap-el":U.value,"focus-start-el":"container",onFocusoutPrevented:Se,onReleaseRequested:Ne},{default:x(()=>[C("div",{class:O(S(J).e("mask")),onClick:L(a=>e.hideOnClickModal&&ye(),["self"])},null,10,["onClick"]),N(" CLOSE "),C("span",{class:O([S(J).e("btn"),S(J).e("close")]),onClick:ye},[I(S(T),null,{default:x(()=>[I(S(A))]),_:1})],2),N(" ARROW "),S(ce)?N("v-if",!0):(h(),E(R,{key:0},[C("span",{class:O(S(pe)),onClick:Ce},[I(S(T),null,{default:x(()=>[I(S(j))]),_:1})],2),C("span",{class:O(S(me)),onClick:_e},[I(S(T),null,{default:x(()=>[I(S(B))]),_:1})],2)],64)),e.$slots.progress||e.showProgress?(h(),E("div",{key:1,class:O([S(J).e("btn"),S(J).e("progress")])},[$(e.$slots,"progress",{activeIndex:te.value,total:e.urlList.length},()=>[P(Y(S(be)),1)])],2)):N("v-if",!0),N(" ACTIONS "),C("div",{class:O([S(J).e("btn"),S(J).e("actions")])},[C("div",{class:O(S(J).e("actions__inner"))},[$(e.$slots,"toolbar",{actions:Oe,prev:Ce,next:_e,reset:Ie,activeIndex:te.value,setActiveItem:ze},()=>[I(S(T),{onClick:e=>Oe("zoomOut")},{default:x(()=>[I(S(M))]),_:1},8,["onClick"]),I(S(T),{onClick:e=>Oe("zoomIn")},{default:x(()=>[I(S(X))]),_:1},8,["onClick"]),C("i",{class:O(S(J).e("actions__divider"))},null,2),I(S(T),{onClick:Ie},{default:x(()=>[(h(),w(D(S(oe).icon)))]),_:1}),C("i",{class:O(S(J).e("actions__divider"))},null,2),I(S(T),{onClick:e=>Oe("anticlockwise")},{default:x(()=>[I(S(F))]),_:1},8,["onClick"]),I(S(T),{onClick:e=>Oe("clockwise")},{default:x(()=>[I(S(K))]),_:1},8,["onClick"])])],2)],2),N(" CANVAS "),C("div",{class:O(S(J).e("canvas"))},[(h(!0),E(R,null,W(e.urlList,(a,s)=>(h(),E(R,{key:s},[s===te.value?(h(),E("img",{key:0,ref_for:!0,ref:e=>ee.value[s]=e,src:a,style:_(S(ge)),class:O(S(J).e("img")),crossorigin:e.crossorigin,onLoad:ke,onError:we,onMousedown:he},null,46,["src","crossorigin"])):N("v-if",!0)],64))),128))],2),$(e.$slots,"default")]),_:3},8,["focus-trap-el"])],6)]),_:3})]),_:3},8,["disabled"]))}}),[["__file","image-viewer.vue"]])),ye=s({hideOnClickModal:Boolean,src:{type:String,default:""},fit:{type:String,values:["","contain","cover","fill","none","scale-down"],default:""},loading:{type:String,values:["eager","lazy"]},lazy:Boolean,scrollContainer:{type:t([String,Object])},previewSrcList:{type:t(Array),default:()=>o([])},previewTeleported:Boolean,zIndex:{type:Number},initialIndex:{type:Number,default:0},infinite:{type:Boolean,default:!0},closeOnPressEscape:{type:Boolean,default:!0},zoomRate:{type:Number,default:1.2},minScale:{type:Number,default:.2},maxScale:{type:Number,default:7},showProgress:Boolean,crossorigin:{type:t(String)}}),ke={load:e=>e instanceof Event,error:e=>e instanceof Event,switch:e=>a(e),close:()=>!0,show:()=>!0},we=n({name:"ElImage",inheritAttrs:!1});const he=Z(l(n({...we,props:ye,emits:ke,setup(e,{expose:a,emit:s}){const t=e,{t:o}=u(),l=d("image"),n=G(),i=g(()=>J(Object.entries(n).filter(([e])=>/^(data-|on[A-Z])/i.test(e)||["id","style"].includes(e)))),r=de({excludeListeners:!0,excludeKeys:g(()=>Object.keys(i.value))}),c=f(),v=f(!1),p=f(!0),m=f(!1),I=f(),z=f(),_=Q&&"loading"in HTMLImageElement.prototype;let L;const T=g(()=>[l.e("inner"),j.value&&l.e("preview"),p.value&&l.is("loading")]),A=g(()=>{const{fit:e}=t;return Q&&e?{objectFit:e}:{}}),j=g(()=>{const{previewSrcList:e}=t;return U(e)&&e.length>0}),B=g(()=>{const{previewSrcList:e,initialIndex:a}=t;let s=a;return a>e.length-1&&(s=0),s}),P=g(()=>"eager"!==t.loading&&(!_&&"lazy"===t.loading||t.lazy)),M=()=>{Q&&(p.value=!0,v.value=!1,c.value=t.src)};function X(e){p.value=!1,v.value=!1,s("load",e)}function D(e){p.value=!1,v.value=!0,s("error",e)}function F(){ue(I.value,z.value)&&(M(),V())}const K=ne(F,200,!0);async function W(){var e;if(!Q)return;await H();const{scrollContainer:a}=t;oe(a)?z.value=a:le(a)&&""!==a?z.value=null!=(e=document.querySelector(a))?e:void 0:I.value&&(z.value=ve(I.value)),z.value&&(L=k(z,"scroll",K),setTimeout(()=>F(),100))}function V(){Q&&z.value&&K&&(null==L||L(),z.value=void 0)}function q(){j.value&&(m.value=!0,s("show"))}function Z(){m.value=!1,s("close")}function ie(e){s("switch",e)}return b(()=>t.src,()=>{P.value?(p.value=!0,v.value=!1,V(),W()):M()}),y(()=>{P.value?W():M()}),a({showPreview:q}),(e,a)=>(h(),E("div",ee({ref_key:"container",ref:I},S(i),{class:[S(l).b(),e.$attrs.class]}),[v.value?$(e.$slots,"error",{key:0},()=>[C("div",{class:O(S(l).e("error"))},Y(S(o)("el.image.error")),3)]):(h(),E(R,{key:1},[void 0!==c.value?(h(),E("img",ee({key:0},S(r),{src:c.value,loading:e.loading,style:S(A),class:S(T),crossorigin:e.crossorigin,onClick:q,onLoad:X,onError:D}),null,16,["src","loading","crossorigin"])):N("v-if",!0),p.value?(h(),E("div",{key:1,class:O(S(l).e("wrapper"))},[$(e.$slots,"placeholder",{},()=>[C("div",{class:O(S(l).e("placeholder"))},null,2)])],2)):N("v-if",!0)],64)),S(j)?(h(),E(R,{key:2},[m.value?(h(),w(S(be),{key:0,"z-index":e.zIndex,"initial-index":S(B),infinite:e.infinite,"zoom-rate":e.zoomRate,"min-scale":e.minScale,"max-scale":e.maxScale,"show-progress":e.showProgress,"url-list":e.previewSrcList,crossorigin:e.crossorigin,"hide-on-click-modal":e.hideOnClickModal,teleported:e.previewTeleported,"close-on-press-escape":e.closeOnPressEscape,onClose:Z,onSwitch:ie},ae({toolbar:x(a=>[$(e.$slots,"toolbar",se(te(a)))]),default:x(()=>[e.$slots.viewer?(h(),E("div",{key:0},[$(e.$slots,"viewer")])):N("v-if",!0)]),_:2},[e.$slots.progress?{name:"progress",fn:x(a=>[$(e.$slots,"progress",se(te(a)))])}:void 0]),1032,["z-index","initial-index","infinite","zoom-rate","min-scale","max-scale","show-progress","url-list","crossorigin","hide-on-click-modal","teleported","close-on-press-escape"])):N("v-if",!0)],64)):N("v-if",!0)],16))}}),[["__file","image.vue"]]));export{he as E,be as a};
