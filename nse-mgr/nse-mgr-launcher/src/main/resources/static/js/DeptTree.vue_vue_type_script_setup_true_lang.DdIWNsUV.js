import{d as e,r as a,dj as l,S as t,au as o,ap as r,e as s,f as d,w as n,m as i,i as p,aC as u,E as m}from"./index.Ckm1SagX.js";import{E as c}from"./card.BfhlXze7.js";import{E as f}from"./tree.BMRqnaD4.js";import"./checkbox.CyAsOZKA.js";/* empty css             *//* empty css              */import{D as h}from"./dept.api.CHEkLPxS.js";import{E as j}from"./index.4JfkAhur.js";const x=e({__name:"DeptTree",props:{modelValue:{type:[String,Number],default:void 0}},emits:["node-click"],setup(e,{emit:x}){const v=e,_=a(),b=a(),k=a(),V=x,E=l(v,"modelValue",V);function S(e,a){return!e||-1!==a.label.indexOf(e)}function g(e){E.value=e.value,V("node-click")}return t(()=>{b.value.filter(k.value)},{flush:"post"}),o(()=>{h.getOptions().then(e=>{_.value=e})}),(e,a)=>{const l=r("Search"),t=m,o=j,h=f,x=c;return d(),s(x,{shadow:"never"},{default:n(()=>[i(o,{modelValue:p(k),"onUpdate:modelValue":a[0]||(a[0]=e=>u(k)?k.value=e:null),placeholder:"部门名称",clearable:""},{prefix:n(()=>[i(t,null,{default:n(()=>[i(l)]),_:1})]),_:1},8,["modelValue"]),i(h,{ref_key:"deptTreeRef",ref:b,class:"mt-2",data:p(_),props:{children:"children",label:"label",disabled:""},"expand-on-click-node":!1,"filter-node-method":S,"default-expand-all":"",onNodeClick:g},null,8,["data"])]),_:1})}}});export{x as _};
