import{d as t,ap as e,e as o,f as s,w as a,l,m as n,E as r,k as i,aP as c}from"./index.Ckm1SagX.js";/* empty css               */import{E as p}from"./index.CbYeWxT8.js";const u=t({name:"CopyButton",inheritAttrs:!1,__name:"index",props:{text:{type:String,default:""},style:{type:Object,default:()=>({})}},setup(t){const u=t;function d(){if(navigator.clipboard&&navigator.clipboard.writeText)navigator.clipboard.writeText(u.text).then(()=>{c.success("Copy successfully")}).catch(t=>{c.warning("Copy failed")});else{const e=document.createElement("input");e.style.position="absolute",e.style.left="-9999px",e.setAttribute("value",u.text),document.body.appendChild(e),e.select();try{document.execCommand("copy")?c.success("Copy successfully!"):c.warning("Copy failed!")}catch(t){c.error("Copy failed.")}finally{document.body.removeChild(e)}}}return(c,u)=>{const y=e("DocumentCopy"),m=r,f=p;return s(),o(f,{link:"",style:i(t.style),onClick:d},{default:a(()=>[l(c.$slots,"default",{},()=>[n(m,null,{default:a(()=>[n(y,{color:"var(--el-color-primary)"})]),_:1})])]),_:3},8,["style"])}}});export{u as _};
