import{ab as e,M as t,t as s,z as a,b7 as l,af as o,_ as i,d as n,x as r,b as u,r as c,c as d,e as p,f,w as v,g as m,l as y,Q as g,R as b,a0 as h,i as k,n as w,h as F,C as E,j as R,m as T,E as x,b1 as C,F as S,k as $,cd as L,aO as _,$ as U,cK as j,aU as P,bt as O,A as D,s as B,cw as q,cL as A,b6 as H,cM as M,I as K,a8 as z,y as X,ah as N,bd as W,W as G,q as I}from"./index.Ckm1SagX.js";import{E as J}from"./progress.ChnKapv7.js";import{t as Q,d as Y}from"./aria.C1IWO_Rd.js";import{b as V}from"./use-form-common-props.BSYTvb6G.js";import{b as Z}from"./_baseClone.ByRc02qR.js";import{i as ee}from"./isEqual.CZKKciWh.js";function te(e){return Z(e,5)}const se=Symbol("uploadContextKey");class ae extends Error{constructor(e,t,s,a){super(e),this.name="UploadAjaxError",this.status=t,this.method=s,this.url=a}}function le(e,t,s){let a;return a=s.response?`${s.response.error||s.response}`:s.responseText?`${s.responseText}`:`fail to ${t.method} ${e} ${s.status}`,new ae(a,s.status,t.method,e)}const oe=["text","picture","picture-card"];let ie=1;const ne=()=>Date.now()+ie++,re=s({action:{type:String,default:"#"},headers:{type:a(Object)},method:{type:String,default:"post"},data:{type:a([Object,Function,Promise]),default:()=>o({})},multiple:Boolean,name:{type:String,default:"file"},drag:Boolean,withCredentials:Boolean,showFileList:{type:Boolean,default:!0},accept:{type:String,default:""},fileList:{type:a(Array),default:()=>o([])},autoUpload:{type:Boolean,default:!0},listType:{type:String,values:oe,default:"text"},httpRequest:{type:a(Function),default:s=>{"undefined"==typeof XMLHttpRequest&&Q("ElUpload","XMLHttpRequest is undefined");const a=new XMLHttpRequest,l=s.action;a.upload&&a.upload.addEventListener("progress",e=>{const t=e;t.percent=e.total>0?e.loaded/e.total*100:0,s.onProgress(t)});const o=new FormData;if(s.data)for(const[t,n]of Object.entries(s.data))e(n)&&n.length?o.append(t,...n):o.append(t,n);o.append(s.filename,s.file,s.file.name),a.addEventListener("error",()=>{s.onError(le(l,s,a))}),a.addEventListener("load",()=>{if(a.status<200||a.status>=300)return s.onError(le(l,s,a));s.onSuccess(function(e){const t=e.responseText||e.response;if(!t)return t;try{return JSON.parse(t)}catch(s){return t}}(a))}),a.open(s.method,l,!0),s.withCredentials&&"withCredentials"in a&&(a.withCredentials=!0);const i=s.headers||{};if(i instanceof Headers)i.forEach((e,t)=>a.setRequestHeader(t,e));else for(const[e,n]of Object.entries(i))t(n)||a.setRequestHeader(e,String(n));return a.send(o),a}},disabled:Boolean,limit:Number}),ue=s({...re,beforeUpload:{type:a(Function),default:l},beforeRemove:{type:a(Function)},onRemove:{type:a(Function),default:l},onChange:{type:a(Function),default:l},onPreview:{type:a(Function),default:l},onSuccess:{type:a(Function),default:l},onProgress:{type:a(Function),default:l},onError:{type:a(Function),default:l},onExceed:{type:a(Function),default:l},crossorigin:{type:a(String)}}),ce=s({files:{type:a(Array),default:()=>o([])},disabled:Boolean,handlePreview:{type:a(Function),default:l},listType:{type:String,values:oe,default:"text"},crossorigin:{type:a(String)}}),de=n({name:"ElUploadList"});var pe=i(n({...de,props:ce,emits:{remove:e=>!!e},setup(e,{emit:t}){const s=e,{t:a}=r(),l=u("upload"),o=u("icon"),i=u("list"),n=V(),D=c(!1),B=d(()=>[l.b("list"),l.bm("list",s.listType),l.is("disabled",s.disabled)]),q=e=>{t("remove",e)};return(e,t)=>(f(),p(O,{tag:"ul",class:w(k(B)),name:k(i).b()},{default:v(()=>[(f(!0),m(g,null,b(e.files,(t,s)=>(f(),m("li",{key:t.uid||t.name,class:w([k(l).be("list","item"),k(l).is(t.status),{focusing:D.value}]),tabindex:"0",onKeydown:h(e=>!k(n)&&q(t),["delete"]),onFocus:e=>D.value=!0,onBlur:e=>D.value=!1,onClick:e=>D.value=!1},[y(e.$slots,"default",{file:t,index:s},()=>["picture"===e.listType||"uploading"!==t.status&&"picture-card"===e.listType?(f(),m("img",{key:0,class:w(k(l).be("list","item-thumbnail")),src:t.url,crossorigin:e.crossorigin,alt:""},null,10,["src","crossorigin"])):F("v-if",!0),"uploading"===t.status||"picture-card"!==e.listType?(f(),m("div",{key:1,class:w(k(l).be("list","item-info"))},[E("a",{class:w(k(l).be("list","item-name")),onClick:R(s=>e.handlePreview(t),["prevent"])},[T(k(x),{class:w(k(o).m("document"))},{default:v(()=>[T(k(C))]),_:1},8,["class"]),E("span",{class:w(k(l).be("list","item-file-name")),title:t.name},S(t.name),11,["title"])],10,["onClick"]),"uploading"===t.status?(f(),p(k(J),{key:0,type:"picture-card"===e.listType?"circle":"line","stroke-width":"picture-card"===e.listType?6:2,percentage:Number(t.percentage),style:$("picture-card"===e.listType?"":"margin-top: 0.5rem")},null,8,["type","stroke-width","percentage","style"])):F("v-if",!0)],2)):F("v-if",!0),E("label",{class:w(k(l).be("list","item-status-label"))},["text"===e.listType?(f(),p(k(x),{key:0,class:w([k(o).m("upload-success"),k(o).m("circle-check")])},{default:v(()=>[T(k(L))]),_:1},8,["class"])):["picture-card","picture"].includes(e.listType)?(f(),p(k(x),{key:1,class:w([k(o).m("upload-success"),k(o).m("check")])},{default:v(()=>[T(k(_))]),_:1},8,["class"])):F("v-if",!0)],2),k(n)?F("v-if",!0):(f(),p(k(x),{key:2,class:w(k(o).m("close")),onClick:e=>q(t)},{default:v(()=>[T(k(U))]),_:2},1032,["class","onClick"])),F(" Due to close btn only appears when li gets focused disappears after li gets blurred, thus keyboard navigation can never reach close btn"),F(" This is a bug which needs to be fixed "),F(" TODO: Fix the incorrect navigation interaction "),k(n)?F("v-if",!0):(f(),m("i",{key:3,class:w(k(o).m("close-tip"))},S(k(a)("el.upload.deleteTip")),3)),"picture-card"===e.listType?(f(),m("span",{key:4,class:w(k(l).be("list","item-actions"))},[E("span",{class:w(k(l).be("list","item-preview")),onClick:s=>e.handlePreview(t)},[T(k(x),{class:w(k(o).m("zoom-in"))},{default:v(()=>[T(k(j))]),_:1},8,["class"])],10,["onClick"]),k(n)?F("v-if",!0):(f(),m("span",{key:0,class:w(k(l).be("list","item-delete")),onClick:e=>q(t)},[T(k(x),{class:w(k(o).m("delete"))},{default:v(()=>[T(k(P))]),_:1},8,["class"])],10,["onClick"]))],2)):F("v-if",!0)])],42,["onKeydown","onFocus","onBlur","onClick"]))),128)),y(e.$slots,"append")]),_:3},8,["class","name"]))}}),[["__file","upload-list.vue"]]);const fe=s({disabled:Boolean}),ve={file:t=>e(t)},me="ElUploadDrag",ye=n({name:me});var ge=i(n({...ye,props:fe,emits:ve,setup(e,{emit:t}){D(se)||Q(me,"usage: <el-upload><el-upload-dragger /></el-upload>");const s=u("upload"),a=c(!1),l=V(),o=e=>{if(l.value)return;a.value=!1,e.stopPropagation();const s=Array.from(e.dataTransfer.files),o=e.dataTransfer.items||[];s.forEach((e,t)=>{var s;const a=o[t],l=null==(s=null==a?void 0:a.webkitGetAsEntry)?void 0:s.call(a);l&&(e.isDirectory=l.isDirectory)}),t("file",s)},i=()=>{l.value||(a.value=!0)},n=e=>{e.currentTarget.contains(e.relatedTarget)||(a.value=!1)};return(e,t)=>(f(),m("div",{class:w([k(s).b("dragger"),k(s).is("dragover",a.value)]),onDrop:R(o,["prevent"]),onDragover:R(i,["prevent"]),onDragleave:R(n,["prevent"])},[y(e.$slots,"default")],42,["onDrop","onDragover","onDragleave"]))}}),[["__file","upload-dragger.vue"]]);const be=s({...re,beforeUpload:{type:a(Function),default:l},onRemove:{type:a(Function),default:l},onStart:{type:a(Function),default:l},onSuccess:{type:a(Function),default:l},onProgress:{type:a(Function),default:l},onError:{type:a(Function),default:l},onExceed:{type:a(Function),default:l}}),he=n({name:"ElUploadContent",inheritAttrs:!1});var ke=i(n({...he,props:be,setup(e,{expose:t}){const s=e,a=u("upload"),l=V(),o=B({}),i=B(),n=e=>{if(0===e.length)return;const{autoUpload:t,limit:a,fileList:l,multiple:o,onStart:i,onExceed:n}=s;if(a&&l.length+e.length>a)n(e,l);else{o||(e=e.slice(0,1));for(const s of e){const e=s;e.uid=ne(),i(e),t&&r(e)}}},r=async e=>{if(i.value.value="",!s.beforeUpload)return c(e);let t,a={};try{const l=s.data,o=s.beforeUpload(e);a=q(s.data)?te(s.data):s.data,t=await o,q(s.data)&&ee(l,a)&&(a=te(s.data))}catch(o){t=!1}if(!1===t)return void s.onRemove(e);let l=e;t instanceof Blob&&(l=t instanceof File?t:new File([t],e.name,{type:e.type})),c(Object.assign(l,{uid:e.uid}),a)},c=async(e,t)=>{const{headers:a,data:l,method:i,withCredentials:n,name:r,action:u,onProgress:c,onSuccess:d,onError:p,httpRequest:f}=s;try{t=await(async(e,t)=>H(e)?e(t):e)(null!=t?t:l,e)}catch(g){return void s.onRemove(e)}const{uid:v}=e,m={headers:a||{},withCredentials:n,file:e,data:t,method:i,filename:r,action:u,onProgress:t=>{c(t,e)},onSuccess:t=>{d(t,e),delete o.value[v]},onError:t=>{p(t,e),delete o.value[v]}},y=f(m);o.value[v]=y,y instanceof Promise&&y.then(m.onSuccess,m.onError)},d=e=>{const t=e.target.files;t&&n(Array.from(t))},g=()=>{l.value||(i.value.value="",i.value.click())},b=()=>{g()};return t({abort:e=>{A(o.value).filter(e?([t])=>String(e.uid)===t:()=>!0).forEach(([e,t])=>{t instanceof XMLHttpRequest&&t.abort(),delete o.value[e]})},upload:r}),(e,t)=>(f(),m("div",{class:w([k(a).b(),k(a).m(e.listType),k(a).is("drag",e.drag),k(a).is("disabled",k(l))]),tabindex:k(l)?"-1":"0",onClick:g,onKeydown:h(R(b,["self"]),["enter","space"])},[e.drag?(f(),p(ge,{key:0,disabled:k(l),onFile:n},{default:v(()=>[y(e.$slots,"default")]),_:3},8,["disabled"])):y(e.$slots,"default",{key:1}),E("input",{ref_key:"inputRef",ref:i,class:w(k(a).e("input")),name:e.name,disabled:k(l),multiple:e.multiple,accept:e.accept,type:"file",onChange:d,onClick:R(()=>{},["stop"])},null,42,["name","disabled","multiple","accept","onClick"])],42,["tabindex","onKeydown"]))}}),[["__file","upload-content.vue"]]);const we="ElUpload",Fe=e=>{var t;(null==(t=e.url)?void 0:t.startsWith("blob:"))&&URL.revokeObjectURL(e.url)},Ee=n({name:"ElUpload"});const Re=I(i(n({...Ee,props:ue,setup(e,{expose:s}){const a=e,l=V(),o=B(),{abort:i,submit:n,clearFiles:r,uploadFiles:u,handleStart:c,handleError:g,handleRemove:b,handleSuccess:h,handleProgress:w,revokeFileObjectURL:E}=((e,s)=>{const a=M(e,"fileList",void 0,{passive:!0}),l=e=>a.value.find(t=>t.uid===e.uid);function o(e){var t;null==(t=s.value)||t.abort(e)}function i(e){a.value=a.value.filter(t=>t.uid!==e.uid)}return K(()=>e.listType,t=>{"picture-card"!==t&&"picture"!==t||(a.value=a.value.map(t=>{const{raw:s,url:l}=t;if(!l&&s)try{t.url=URL.createObjectURL(s)}catch(o){e.onError(o,t,a.value)}return t}))}),K(a,e=>{for(const t of e)t.uid||(t.uid=ne()),t.status||(t.status="success")},{immediate:!0,deep:!0}),{uploadFiles:a,abort:o,clearFiles:function(e=["ready","uploading","success","fail"]){a.value=a.value.filter(t=>!e.includes(t.status))},handleError:(t,s)=>{const o=l(s);o&&(o.status="fail",i(o),e.onError(t,o,a.value),e.onChange(o,a.value))},handleProgress:(t,s)=>{const o=l(s);o&&(e.onProgress(t,o,a.value),o.status="uploading",o.percentage=Math.round(t.percent))},handleStart:s=>{t(s.uid)&&(s.uid=ne());const l={name:s.name,percentage:0,status:"ready",size:s.size,raw:s,uid:s.uid};if("picture-card"===e.listType||"picture"===e.listType)try{l.url=URL.createObjectURL(s)}catch(o){Y(we,o.message),e.onError(o,l,a.value)}a.value=[...a.value,l],e.onChange(l,a.value)},handleSuccess:(t,s)=>{const o=l(s);o&&(o.status="success",o.response=t,e.onSuccess(t,o,a.value),e.onChange(o,a.value))},handleRemove:async t=>{const s=t instanceof File?l(t):t;s||Q(we,"file to be removed not found");const n=t=>{o(t),i(t),e.onRemove(t,a.value),Fe(t)};e.beforeRemove?!1!==await e.beforeRemove(s,a.value)&&n(s):n(s)},submit:function(){a.value.filter(({status:e})=>"ready"===e).forEach(({raw:e})=>{var t;return e&&(null==(t=s.value)?void 0:t.upload(e))})},revokeFileObjectURL:Fe}})(a,o),R=d(()=>"picture-card"===a.listType),x=d(()=>({...a,fileList:u.value,onStart:c,onProgress:w,onSuccess:h,onError:g,onRemove:b}));return z(()=>{u.value.forEach(E)}),X(se,{accept:N(a,"accept")}),s({abort:i,submit:n,clearFiles:r,handleStart:c,handleRemove:b}),(e,t)=>(f(),m("div",null,[k(R)&&e.showFileList?(f(),p(pe,{key:0,disabled:k(l),"list-type":e.listType,files:k(u),crossorigin:e.crossorigin,"handle-preview":e.onPreview,onRemove:k(b)},W({append:v(()=>[T(ke,G({ref_key:"uploadRef",ref:o},k(x)),{default:v(()=>[e.$slots.trigger?y(e.$slots,"trigger",{key:0}):F("v-if",!0),!e.$slots.trigger&&e.$slots.default?y(e.$slots,"default",{key:1}):F("v-if",!0)]),_:3},16)]),_:2},[e.$slots.file?{name:"default",fn:v(({file:t,index:s})=>[y(e.$slots,"file",{file:t,index:s})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):F("v-if",!0),!k(R)||k(R)&&!e.showFileList?(f(),p(ke,G({key:1,ref_key:"uploadRef",ref:o},k(x)),{default:v(()=>[e.$slots.trigger?y(e.$slots,"trigger",{key:0}):F("v-if",!0),!e.$slots.trigger&&e.$slots.default?y(e.$slots,"default",{key:1}):F("v-if",!0)]),_:3},16)):F("v-if",!0),e.$slots.trigger?y(e.$slots,"default",{key:2}):F("v-if",!0),y(e.$slots,"tip"),!k(R)&&e.showFileList?(f(),p(pe,{key:3,disabled:k(l),"list-type":e.listType,files:k(u),crossorigin:e.crossorigin,"handle-preview":e.onPreview,onRemove:k(b)},W({_:2},[e.$slots.file?{name:"default",fn:v(({file:t,index:s})=>[y(e.$slots,"file",{file:t,index:s})])}:void 0]),1032,["disabled","list-type","files","crossorigin","handle-preview","onRemove"])):F("v-if",!0)]))}}),[["__file","upload.vue"]]));export{Re as E,te as c,ne as g};
