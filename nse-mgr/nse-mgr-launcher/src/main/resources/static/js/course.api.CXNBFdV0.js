import{aw as e}from"./index.Ckm1SagX.js";const t="/api/course-repo",r={getCoursePage:r=>e({url:`${t}/page`,method:"get",params:r}),getCoursePkgList:r=>e({url:`${t}/coursePkgList`,method:"get",params:{coursePkg:r}}),getCourseExpList:(r,o,a)=>e({url:`${t}/courseExpList`,method:"get",params:{coursePkg:r,expType:o,expName:a}}),getCourseDetail:r=>e({url:`${t}/${r}`,method:"get"}),saveCourse(r){const o=new FormData;return Object.keys(r).forEach(e=>{o.append(e,r[e])}),e({url:`${t}`,method:"post",data:o,headers:{"Content-Type":"multipart/form-data"}})},deleteCourse:r=>e({url:`${t}/batch`,method:"delete",data:r}),exportCourse:r=>e({url:`${t}/export`,method:"get",params:r,responseType:"blob"}),importCourse(r){const o=new FormData;return o.append("file",r),e({url:`${t}/import`,method:"post",data:o,headers:{"Content-Type":"multipart/form-data"}})},downloadTemplate:()=>e({url:`${t}/template`,method:"get",responseType:"blob"})};export{r as C};
