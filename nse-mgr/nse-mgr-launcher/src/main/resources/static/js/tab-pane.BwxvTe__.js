import{c8 as e,c5 as a,c6 as t,cA as l,t as s,af as n,z as o,_ as r,d as i,A as u,b as c,r as d,I as v,ad as b,a8 as p,g as f,f as m,k as h,n as g,i as y,a7 as P,a2 as $,c_ as C,c$ as x,s as R,c as w,o as N,by as k,cN as T,m as E,E as B,cs as S,a6 as j,K as A,$ as _,L as F,aV as V,l as L,cS as K,B as O,y as q,b8 as z,bH as H,V as I,b9 as M,X as U,h as W,Z as X,q as Y,G}from"./index.Ckm1SagX.js";import{c as Z}from"./strings.By8NVWWL.js";import{t as D}from"./aria.C1IWO_Rd.js";import{U as J}from"./event.BwRzfsZt.js";import{u as Q}from"./index.DeprZc7T.js";import{g as ee,b as ae}from"./_baseClone.ByRc02qR.js";import{c as te}from"./_initCloneObject.BsGr3vVr.js";import{i as le}from"./isPlainObject.Ct3iyI-U.js";import{a as se}from"./index.BLy3nyPI.js";function ne(a,t){return t.length<2?a:e(a,function(e,a,t){var l=-1,s=e.length;a<0&&(a=-a>s?0:s+a),(t=t>s?s:t)<0&&(t+=s),s=a>t?0:t-a>>>0,a>>>=0;for(var n=Array(s);++l<s;)n[l]=e[l+a];return n}(t,0,-1))}function oe(e,l){return null==(e=ne(e,l=a(l,e)))||delete e[t((s=l,n=null==s?0:s.length,n?s[n-1]:void 0))];var s,n}function re(e){return le(e)?void 0:e}var ie=se(function(e,t){var s={};if(null==e)return s;var n=!1;t=l(t,function(t){return t=a(t,e),n||(n=t.length>1),t}),te(e,ee(e),s),n&&(s=ae(s,7,re));for(var o=t.length;o--;)oe(s,t[o]);return s});const ue=Symbol("tabsRootContextKey"),ce=s({tabs:{type:o(Array),default:()=>n([])},tabRefs:{type:o(Object),default:()=>n({})}}),de="ElTabBar",ve=i({name:de});var be=r(i({...ve,props:ce,setup(e,{expose:a}){const t=e,l=u(ue);l||D(de,"<el-tabs><el-tab-bar /></el-tabs>");const s=c("tabs"),n=d(),o=d(),r=()=>o.value=(()=>{let e=0,a=0;const s=["top","bottom"].includes(l.props.tabPosition)?"width":"height",n="width"===s?"x":"y",o="x"===n?"left":"top";return t.tabs.every(l=>{if(P(l.paneName))return!1;const n=t.tabRefs[l.paneName];if(!n)return!1;if(!l.active)return!0;e=n[`offset${Z(o)}`],a=n[`client${Z(s)}`];const r=window.getComputedStyle(n);return"width"===s&&(a-=Number.parseFloat(r.paddingLeft)+Number.parseFloat(r.paddingRight),e+=Number.parseFloat(r.paddingLeft)),!1}),{[s]:`${a}px`,transform:`translate${Z(n)}(${e}px)`}})(),i=[];v(()=>t.tabs,async()=>{await $(),r(),i.forEach(e=>e.stop()),i.length=0,Object.values(t.tabRefs).forEach(e=>{i.push(b(e,r))})},{immediate:!0});const C=b(n,()=>r());return p(()=>{i.forEach(e=>e.stop()),i.length=0,C.stop()}),a({ref:n,update:r}),(e,a)=>(m(),f("div",{ref_key:"barRef",ref:n,class:g([y(s).e("active-bar"),y(s).is(y(l).props.tabPosition)]),style:h(o.value)},null,6))}}),[["__file","tab-bar.vue"]]);const pe=s({panes:{type:o(Array),default:()=>n([])},currentName:{type:[String,Number],default:""},editable:Boolean,type:{type:String,values:["card","border-card",""],default:""},stretch:Boolean}),fe="ElTabNav",me=i({name:fe,props:pe,emits:{tabClick:(e,a,t)=>t instanceof Event,tabRemove:(e,a)=>a instanceof Event},setup(e,{expose:a,emit:t}){const l=u(ue);l||D(fe,"<el-tabs><tab-nav /></el-tabs>");const s=c("tabs"),n=C(),o=x(),r=d(),i=d(),p=d(),f=d({}),m=d(),h=d(!1),g=d(0),y=d(!1),P=d(!0),F=R(),V=w(()=>["top","bottom"].includes(l.props.tabPosition)?"width":"height"),L=w(()=>({transform:`translate${"width"===V.value?"X":"Y"}(-${g.value}px)`})),K=()=>{if(!r.value)return;const e=r.value[`offset${Z(V.value)}`],a=g.value;if(!a)return;const t=a>e?a-e:0;g.value=t},O=()=>{if(!r.value||!i.value)return;const e=i.value[`offset${Z(V.value)}`],a=r.value[`offset${Z(V.value)}`],t=g.value;if(e-t<=a)return;const l=e-t>2*a?t+a:e-a;g.value=l},q=async()=>{const a=i.value;if(!(h.value&&p.value&&r.value&&a))return;await $();const t=f.value[e.currentName];if(!t)return;const s=r.value,n=["top","bottom"].includes(l.props.tabPosition),o=t.getBoundingClientRect(),u=s.getBoundingClientRect(),c=n?a.offsetWidth-u.width:a.offsetHeight-u.height,d=g.value;let v=d;n?(o.left<u.left&&(v=d-(u.left-o.left)),o.right>u.right&&(v=d+o.right-u.right)):(o.top<u.top&&(v=d-(u.top-o.top)),o.bottom>u.bottom&&(v=d+(o.bottom-u.bottom))),v=Math.max(v,0),g.value=Math.min(v,c)},z=()=>{var a;if(!i.value||!r.value)return;e.stretch&&(null==(a=m.value)||a.update());const t=i.value[`offset${Z(V.value)}`],l=r.value[`offset${Z(V.value)}`],s=g.value;l<t?(h.value=h.value||{},h.value.prev=s,h.value.next=s+l<t,t-s<l&&(g.value=t-l)):(h.value=!1,s>0&&(g.value=0))},H=e=>{let a=0;switch(e.code){case A.left:case A.up:a=-1;break;case A.right:case A.down:a=1;break;default:return}const t=Array.from(e.currentTarget.querySelectorAll("[role=tab]:not(.is-disabled)"));let l=t.indexOf(e.target)+a;l<0?l=t.length-1:l>=t.length&&(l=0),t[l].focus({preventScroll:!0}),t[l].click(),I()},I=()=>{P.value&&(y.value=!0)},M=()=>y.value=!1;return v(n,e=>{"hidden"===e?P.value=!1:"visible"===e&&setTimeout(()=>P.value=!0,50)}),v(o,e=>{e?setTimeout(()=>P.value=!0,50):P.value=!1}),b(p,z),N(()=>setTimeout(()=>q(),0)),k(()=>z()),a({scrollToActiveTab:q,removeFocus:M,focusActiveTab:async()=>{await $();const a=f.value[e.currentName];null==a||a.focus({preventScroll:!0})},tabListRef:i,tabBarRef:m,scheduleRender:()=>T(F)}),()=>{const a=h.value?[E("span",{class:[s.e("nav-prev"),s.is("disabled",!h.value.prev)],onClick:K},[E(B,null,{default:()=>[E(S,null,null)]})]),E("span",{class:[s.e("nav-next"),s.is("disabled",!h.value.next)],onClick:O},[E(B,null,{default:()=>[E(j,null,null)]})])]:null,n=e.panes.map((a,n)=>{var o,r,i,u;const c=a.uid,d=a.props.disabled,v=null!=(r=null!=(o=a.props.name)?o:a.index)?r:`${n}`,b=!d&&(a.isClosable||e.editable);a.index=`${n}`;const p=b?E(B,{class:"is-icon-close",onClick:e=>t("tabRemove",a,e)},{default:()=>[E(_,null,null)]}):null,m=(null==(u=(i=a.slots).label)?void 0:u.call(i))||a.props.label,h=!d&&a.active?0:-1;return E("div",{ref:e=>((e,a)=>{f.value[a]=e})(e,v),class:[s.e("item"),s.is(l.props.tabPosition),s.is("active",a.active),s.is("disabled",d),s.is("closable",b),s.is("focus",y.value)],id:`tab-${v}`,key:`tab-${c}`,"aria-controls":`pane-${v}`,role:"tab","aria-selected":a.active,tabindex:h,onFocus:()=>I(),onBlur:()=>M(),onClick:e=>{M(),t("tabClick",a,v,e)},onKeydown:e=>{!b||e.code!==A.delete&&e.code!==A.backspace||t("tabRemove",a,e)}},[m,p])});return F.value,E("div",{ref:p,class:[s.e("nav-wrap"),s.is("scrollable",!!h.value),s.is(l.props.tabPosition)]},[a,E("div",{class:s.e("nav-scroll"),ref:r},[e.panes.length>0?E("div",{class:[s.e("nav"),s.is(l.props.tabPosition),s.is("stretch",e.stretch&&["top","bottom"].includes(l.props.tabPosition))],ref:i,style:L.value,role:"tablist",onKeydown:H},[e.type?null:E(be,{ref:m,tabs:[...e.panes],tabRefs:f.value},null),n]):null])])}}}),he=s({type:{type:String,values:["card","border-card",""],default:""},closable:Boolean,addable:Boolean,modelValue:{type:[String,Number]},editable:Boolean,tabPosition:{type:String,values:["top","right","bottom","left"],default:"top"},beforeLeave:{type:o(Function),default:()=>!0},stretch:Boolean}),ge=e=>F(e)||V(e);var ye=i({name:"ElTabs",props:he,emits:{[J]:e=>ge(e),tabClick:(e,a)=>a instanceof Event,tabChange:e=>ge(e),edit:(e,a)=>["remove","add"].includes(a),tabRemove:e=>ge(e),tabAdd:()=>!0},setup(e,{emit:a,slots:t,expose:l}){var s;const n=c("tabs"),o=w(()=>["left","right"].includes(e.tabPosition)),{children:r,addChild:i,removeChild:u,ChildrenSorter:b}=Q(O(),"ElTabPane"),p=d(),f=d(null!=(s=e.modelValue)?s:"0"),m=async(t,l=!1)=>{var s,n,o,i;if(f.value!==t&&!P(t))try{let u;if(e.beforeLeave){const a=e.beforeLeave(t,f.value);u=a instanceof Promise?await a:a}else u=!0;if(!1!==u){const e=null==(s=r.value.find(e=>e.paneName===f.value))?void 0:s.isFocusInsidePane();f.value=t,l&&(a(J,t),a("tabChange",t)),null==(o=null==(n=p.value)?void 0:n.removeFocus)||o.call(n),e&&(null==(i=p.value)||i.focusActiveTab())}}catch(u){}},h=(e,t,l)=>{e.props.disabled||(a("tabClick",e,l),m(t,!0))},g=(e,t)=>{e.props.disabled||P(e.props.name)||(t.stopPropagation(),a("edit",e.props.name,"remove"),a("tabRemove",e.props.name))},y=()=>{a("edit",void 0,"add"),a("tabAdd")},C=a=>{const t=a.el.firstChild,l=["bottom","right"].includes(e.tabPosition)?a.children[0].el:a.children[1].el;t!==l&&t.before(l)};return v(()=>e.modelValue,e=>m(e)),v(f,async()=>{var e;await $(),null==(e=p.value)||e.scrollToActiveTab()}),q(ue,{props:e,currentName:f,registerPane:i,unregisterPane:u,nav$:p}),l({currentName:f,get tabNavRef(){return ie(p.value,["scheduleRender"])}}),()=>{const a=t["add-icon"],l=e.editable||e.addable?E("div",{class:[n.e("new-tab"),o.value&&n.e("new-tab-vertical")],tabindex:"0",onClick:y,onKeydown:e=>{[A.enter,A.numpadEnter].includes(e.code)&&y()}},[a?L(t,"add-icon"):E(B,{class:n.is("icon-plus")},{default:()=>[E(K,null,null)]})]):null,s=E("div",{class:[n.e("header"),o.value&&n.e("header-vertical"),n.is(e.tabPosition)]},[E(b,null,{default:()=>E(me,{ref:p,currentName:f.value,editable:e.editable,type:e.type,panes:r.value,stretch:e.stretch,onTabClick:h,onTabRemove:g},null),$stable:!0}),l]),i=E("div",{class:n.e("content")},[L(t,"default")]);return E("div",{class:[n.b(),n.m(e.tabPosition),{[n.m("card")]:"card"===e.type,[n.m("border-card")]:"border-card"===e.type}],onVnodeMounted:C,onVnodeUpdated:C},[i,s])}}});const Pe=s({label:{type:String,default:""},name:{type:[String,Number]},closable:Boolean,disabled:Boolean,lazy:Boolean}),$e="ElTabPane",Ce=i({name:$e});var xe=r(i({...Ce,props:Pe,setup(e){const a=e,t=O(),l=z(),s=u(ue);s||D($e,"usage: <el-tabs><el-tab-pane /></el-tabs/>");const n=c("tab-pane"),o=d(),r=d(),i=w(()=>a.closable||s.props.closable),b=H(()=>{var e;return s.currentName.value===(null!=(e=a.name)?e:r.value)}),h=d(b.value),P=w(()=>{var e;return null!=(e=a.name)?e:r.value}),$=H(()=>!a.lazy||h.value||b.value);v(b,e=>{e&&(h.value=!0)});const C=I({uid:t.uid,getVnode:()=>t.vnode,slots:l,props:a,paneName:P,active:b,index:r,isClosable:i,isFocusInsidePane:()=>{var e;return null==(e=o.value)?void 0:e.contains(document.activeElement)}});return s.registerPane(C),p(()=>{s.unregisterPane(C)}),M(()=>{var e;l.label&&(null==(e=s.nav$.value)||e.scheduleRender())}),(e,a)=>y($)?U((m(),f("div",{key:0,id:`pane-${y(P)}`,ref_key:"paneRef",ref:o,class:g(y(n).b()),role:"tabpanel","aria-hidden":!y(b),"aria-labelledby":`tab-${y(P)}`},[L(e.$slots,"default")],10,["id","aria-hidden","aria-labelledby"])),[[X,y(b)]]):W("v-if",!0)}}),[["__file","tab-pane.vue"]]);const Re=Y(ye,{TabPane:xe}),we=G(xe);export{we as E,Re as a};
