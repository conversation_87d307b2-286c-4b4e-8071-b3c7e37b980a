import{o as e,a8 as t,r as n,bB as o,H as s,K as a,_ as r,d as u,l as c,I as d,i,M as l,a2 as f,L as v,y as p}from"./index.Ckm1SagX.js";import{b as m}from"./aria.C1IWO_Rd.js";const E="focus-trap.focus-after-trapped",y="focus-trap.focus-after-released",L={cancelable:!0,bubbles:!1},b={cancelable:!0,bubbles:!1},w="focusAfterTrapped",h="focusAfterReleased",T=Symbol("elFocusTrap"),g=n(),F=n(0),R=n(0);let K=0;const k=e=>{const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0||e===document.activeElement?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t},N=(e,t)=>{for(const n of e)if(!P(n,t))return n},P=(e,t)=>{if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(t&&e===t)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1},S=(e,t)=>{if(e&&e.focus){const n=document.activeElement;let s=!1;!o(e)||m(e)||e.getAttribute("tabindex")||(e.setAttribute("tabindex","-1"),s=!0),e.focus({preventScroll:!0}),R.value=window.performance.now(),e!==n&&(e=>e instanceof HTMLInputElement&&"select"in e)(e)&&t&&e.select(),o(e)&&s&&e.removeAttribute("tabindex")}};function I(e,t){const n=[...e],o=e.indexOf(t);return-1!==o&&n.splice(o,1),n}const x=(()=>{let e=[];return{push:t=>{const n=e[0];n&&t!==n&&n.pause(),e=I(e,t),e.unshift(t)},remove:t=>{var n,o;e=I(e,t),null==(o=null==(n=e[0])?void 0:n.resume)||o.call(n)}}})(),A=()=>{g.value="pointer",F.value=window.performance.now()},_=()=>{g.value="keyboard",F.value=window.performance.now()},C=e=>new CustomEvent("focus-trap.focusout-prevented",{...b,detail:e});let j=[];const O=e=>{e.code===a.esc&&j.forEach(t=>t(e))};var B=r(u({name:"ElFocusTrap",inheritAttrs:!1,props:{loop:Boolean,trapped:Boolean,focusTrapEl:Object,focusStartEl:{type:[Object,String],default:"first"}},emits:[w,h,"focusin","focusout","focusout-prevented","release-requested"],setup(o,{emit:r}){const u=n();let c,m;const{focusReason:b}=(e(()=>{0===K&&(document.addEventListener("mousedown",A),document.addEventListener("touchstart",A),document.addEventListener("keydown",_)),K++}),t(()=>{K--,K<=0&&(document.removeEventListener("mousedown",A),document.removeEventListener("touchstart",A),document.removeEventListener("keydown",_))}),{focusReason:g,lastUserFocusTimestamp:F,lastAutomatedFocusTimestamp:R});var P;P=e=>{o.trapped&&!I.paused&&r("release-requested",e)},e(()=>{0===j.length&&document.addEventListener("keydown",O),s&&j.push(P)}),t(()=>{j=j.filter(e=>e!==P),0===j.length&&s&&document.removeEventListener("keydown",O)});const I={paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}},B=e=>{if(!o.loop&&!o.trapped)return;if(I.paused)return;const{code:t,altKey:n,ctrlKey:s,metaKey:u,currentTarget:c,shiftKey:d}=e,{loop:i}=o,l=t===a.tab&&!n&&!s&&!u,f=document.activeElement;if(l&&f){const t=c,[n,o]=(e=>{const t=k(e);return[N(t,e),N(t.reverse(),e)]})(t);if(n&&o)if(d||f!==o){if(d&&[n,t].includes(f)){const t=C({focusReason:b.value});r("focusout-prevented",t),t.defaultPrevented||(e.preventDefault(),i&&S(o,!0))}}else{const t=C({focusReason:b.value});r("focusout-prevented",t),t.defaultPrevented||(e.preventDefault(),i&&S(n,!0))}else if(f===t){const t=C({focusReason:b.value});r("focusout-prevented",t),t.defaultPrevented||e.preventDefault()}}};p(T,{focusTrapRef:u,onKeydown:B}),d(()=>o.focusTrapEl,e=>{e&&(u.value=e)},{immediate:!0}),d([u],([e],[t])=>{e&&(e.addEventListener("keydown",B),e.addEventListener("focusin",M),e.addEventListener("focusout",q)),t&&(t.removeEventListener("keydown",B),t.removeEventListener("focusin",M),t.removeEventListener("focusout",q))});const D=e=>{r(w,e)},H=e=>r(h,e),M=e=>{const t=i(u);if(!t)return;const n=e.target,s=e.relatedTarget,a=n&&t.contains(n);if(!o.trapped){s&&t.contains(s)||(c=s)}a&&r("focusin",e),I.paused||o.trapped&&(a?m=n:S(m,!0))},q=e=>{const t=i(u);if(!I.paused&&t)if(o.trapped){const n=e.relatedTarget;l(n)||t.contains(n)||setTimeout(()=>{if(!I.paused&&o.trapped){const e=C({focusReason:b.value});r("focusout-prevented",e),e.defaultPrevented||S(m,!0)}},0)}else{const n=e.target;n&&t.contains(n)||r("focusout",e)}};async function U(){await f();const e=i(u);if(e){x.push(I);const t=e.contains(document.activeElement)?c:document.activeElement;c=t;if(!e.contains(t)){const n=new Event(E,L);e.addEventListener(E,D),e.dispatchEvent(n),n.defaultPrevented||f(()=>{let n=o.focusStartEl;v(n)||(S(n),document.activeElement!==n&&(n="first")),"first"===n&&((e,t=!1)=>{const n=document.activeElement;for(const o of e)if(S(o,t),document.activeElement!==n)return})(k(e),!0),document.activeElement!==t&&"container"!==n||S(e)})}}}function W(){const e=i(u);if(e){e.removeEventListener(E,D);const t=new CustomEvent(y,{...L,detail:{focusReason:b.value}});e.addEventListener(y,H),e.dispatchEvent(t),t.defaultPrevented||"keyboard"!=b.value&&F.value>R.value&&!e.contains(document.activeElement)||S(null!=c?c:document.body),e.removeEventListener(y,H),x.remove(I)}}return e(()=>{o.trapped&&U(),d(()=>o.trapped,e=>{e?U():W()})}),t(()=>{o.trapped&&W(),u.value&&(u.value.removeEventListener("keydown",B),u.value.removeEventListener("focusin",M),u.value.removeEventListener("focusout",q),u.value=void 0)}),{onKeydown:B}}}),[["render",function(e,t,n,o,s,a){return c(e.$slots,"default",{handleKeydown:e.onKeydown})}],["__file","focus-trap.vue"]]);export{B as E,T as F,S as t};
