import{c2 as r,c3 as n,c4 as a,c5 as t,c6 as e,c7 as u,c8 as i,c9 as o,t as l}from"./index.Ckm1SagX.js";import{i as c,a as s,b as f}from"./_arrayPush.Dbwejsrt.js";function v(r){return r}var h=Date.now;var g,p,b,d=r?function(n,a){return r(n,"toString",{configurable:!0,enumerable:!1,value:(t=a,function(){return t}),writable:!0});var t}:v,m=(g=d,p=0,b=0,function(){var r=h(),n=16-(r-b);if(b=r,n>0){if(++p>=800)return arguments[0]}else p=0;return g.apply(void 0,arguments)}),y=Math.max;function S(r,n,a){return n=y(void 0===n?r.length-1:n,0),function(){for(var t=arguments,e=-1,u=y(t.length-n,0),i=Array(u);++e<u;)i[e]=t[n+e];e=-1;for(var o=Array(n+1);++e<n;)o[e]=t[e];return o[n]=a(i),function(r,n,a){switch(a.length){case 0:return r.call(n);case 1:return r.call(n,a[0]);case 2:return r.call(n,a[0],a[1]);case 3:return r.call(n,a[0],a[1],a[2])}return r.apply(n,a)}(r,this,o)}}var j=n?n.isConcatSpreadable:void 0;function w(r){return a(r)||c(r)||!!(j&&r&&r[j])}function x(r,n,a,t,e){var u=-1,i=r.length;for(a||(a=w),e||(e=[]);++u<i;){var o=r[u];n>0&&a(o)?n>1?x(o,n-1,a,t,e):s(e,o):t||(e[e.length]=o)}return e}function A(r){return(null==r?0:r.length)?x(r,1):[]}function C(r){return m(S(r,void 0,A),r+"")}function O(r,n){return null!=r&&n in Object(r)}function k(r,n){return null!=r&&function(r,n,i){for(var o=-1,l=(n=t(n,r)).length,s=!1;++o<l;){var v=e(n[o]);if(!(s=null!=r&&i(r,v)))break;r=r[v]}return s||++o!=l?s:!!(l=null==r?0:r.length)&&f(l)&&u(v,l)&&(a(r)||c(r))}(r,n,O)}function z(r,n){return function(r,n,a){for(var e=-1,u=n.length,l={};++e<u;){var c=n[e],s=i(r,c);a(s,c)&&o(l,t(c,r),s)}return l}(r,n,function(n,a){return k(r,a)})}var D=C(function(r,n){return null==r?{}:z(r,n)});const G=l({ariaLabel:String,ariaOrientation:{type:String,values:["horizontal","vertical","undefined"]},ariaControls:String}),L=r=>D(G,r);export{C as a,x as b,A as f,k as h,v as i,S as o,D as p,m as s,L as u};
