import{t as e,z as a,v as l,aV as s,aW as r,_ as t,d as i,b as o,r as d,c as n,L as u,J as m,I as c,g as p,f,e as v,l as _,k as b,i as y,w as g,D as w,E as j,n as k,q as h,V,o as x,m as C,aA as P,C as E,aX as z,F,ap as U,a1 as q,h as A,aP as I}from"./index.Ckm1SagX.js";import{E as S}from"./dialog.TtqHlFhB.js";import"./overlay.CXfNA60T.js";import{a as Z,E as $}from"./form-item.CUMILu98.js";import{_ as R}from"./index.vue_vue_type_script_setup_true_lang.8dFNY07J.js";/* empty css              */import{E as B,a as M}from"./col.Bi-hDQ18.js";import{E as N,a as O}from"./descriptions-item.Ds_fDr-2.js";import{E as X}from"./card.BfhlXze7.js";import{E as D}from"./divider.Cky69QLC.js";/* empty css               */import{F as T}from"./file.api.td1NUiAp.js";import{E as G}from"./index.CbYeWxT8.js";import{E as H}from"./index.4JfkAhur.js";import{_ as J}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./aria.C1IWO_Rd.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./refs.biN0GvkM.js";import"./index.BRUQ9gWw.js";import"./event.BwRzfsZt.js";import"./index.Dh_vcBr5.js";import"./use-form-common-props.BSYTvb6G.js";import"./castArray.Chmjnshw.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./checkbox.CyAsOZKA.js";import"./index.BLy3nyPI.js";import"./isEqual.CZKKciWh.js";import"./radio.D_DAkhYS.js";/* empty css            */import"./select.DHkh6uhw.js";import"./popper.DpZVcW1M.js";import"./index.Cn1QDWeG.js";import"./scrollbar.6rbryiG1.js";import"./index.BPj3iklg.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./index.Byj-i824.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.B0geSHq7.js";import"./index.DJHzyRe5.js";const K=e({size:{type:[Number,String],values:r,default:"",validator:e=>s(e)},shape:{type:String,values:["circle","square"],default:"circle"},icon:{type:l},src:{type:String,default:""},alt:String,srcSet:String,fit:{type:a(String),default:"cover"}}),L={error:e=>e instanceof Event},W=i({name:"ElAvatar"});const Y=h(t(i({...W,props:K,emits:L,setup(e,{emit:a}){const l=e,r=o("avatar"),t=d(!1),i=n(()=>{const{size:e,icon:a,shape:s}=l,t=[r.b()];return u(e)&&t.push(r.m(e)),a&&t.push(r.m("icon")),s&&t.push(r.m(s)),t}),h=n(()=>{const{size:e}=l;return s(e)?r.cssVarBlock({size:m(e)||""}):void 0}),V=n(()=>({objectFit:l.fit}));function x(e){t.value=!0,a("error",e)}return c(()=>l.src,()=>t.value=!1),(e,a)=>(f(),p("span",{class:k(y(i)),style:b(y(h))},[!e.src&&!e.srcSet||t.value?e.icon?(f(),v(y(j),{key:1},{default:g(()=>[(f(),v(w(e.icon)))]),_:1})):_(e.$slots,"default",{key:2}):(f(),p("img",{key:0,src:e.src,alt:e.alt,srcset:e.srcSet,style:b(y(V)),onError:x},null,44,["src","alt","srcset"]))],6))}}),[["__file","avatar.vue"]])),Q={class:"profile-container"},ee={class:"user-info"},ae={class:"avatar-wrapper"},le={class:"user-name"},se={class:"nickname"},re={class:"user-role"},te={class:"security-item"},ie={class:"dialog-footer"},oe=J(i({__name:"index",setup(e){const a=d({}),l=V({visible:!1,title:"",type:""}),s=d(),r=d(),t=d(),i=d(),o=V({}),n=V({}),u=V({}),m=V({}),c=d(0),_=d(),b=d(0),w=d(),k={oldPassword:[{required:!0,message:"请输入原密码",trigger:"blur"}],newPassword:[{required:!0,message:"请输入新密码",trigger:"blur"}],confirmPassword:[{required:!0,message:"请再次输入新密码",trigger:"blur"}]},h={mobile:[{required:!0,message:"请输入手机号",trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"请输入正确的手机号码",trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}]},J={email:[{required:!0,message:"请输入邮箱",trigger:"blur"},{pattern:/\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/,message:"请输入正确的邮箱地址",trigger:"blur"}],code:[{required:!0,message:"请输入验证码",trigger:"blur"}]},K=e=>{switch(l.type=e,l.visible=!0,e){case"account":l.title="账号资料",o.id=a.value.id,o.nickname=a.value.nickname,o.gender=a.value.gender;break;case"password":l.title="修改密码";break;case"mobile":l.title="绑定手机";break;case"email":l.title="绑定邮箱"}};function L(){if(!u.mobile)return void I.error("请输入手机号");/^1[3-9]\d{9}$/.test(u.mobile)?P.sendMobileCode(u.mobile).then(()=>{I.success("验证码发送成功"),c.value=60,_.value=setInterval(()=>{c.value>0?c.value-=1:clearInterval(_.value)},1e3)}):I.error("手机号格式不正确")}function W(){if(!m.email)return void I.error("请输入邮箱");/\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}/.test(m.email)?P.sendEmailCode(m.email).then(()=>{I.success("验证码发送成功"),b.value=60,w.value=setInterval(()=>{b.value>0?b.value-=1:clearInterval(w.value)},1e3)}):I.error("邮箱格式不正确")}const oe=async()=>{if("account"===l.type)P.updateProfile(o).then(()=>{I.success("账号资料修改成功"),l.visible=!1,ce()});else if("password"===l.type){if(n.newPassword!==n.confirmPassword)return void I.error("两次输入的密码不一致");P.changePassword(n).then(()=>{I.success("密码修改成功"),l.visible=!1})}else"mobile"===l.type?P.bindOrChangeMobile(u).then(()=>{I.success("手机号绑定成功"),l.visible=!1,ce()}):"email"===l.type&&P.bindOrChangeEmail(m).then(()=>{I.success("邮箱绑定成功"),l.visible=!1,ce()})},de=()=>{var e,a,o,d;l.visible=!1,"account"===l.type?null==(e=s.value)||e.resetFields():"password"===l.type?null==(a=r.value)||a.resetFields():"mobile"===l.type?null==(o=t.value)||o.resetFields():"email"===l.type&&(null==(d=i.value)||d.resetFields())},ne=d(null),ue=()=>{var e;null==(e=ne.value)||e.click()},me=async e=>{const l=e.target,s=l.files?l.files[0]:null;if(s)try{const e=await T.uploadFile(s);a.value.avatar=e.url,await P.updateProfile({avatar:e.url})}catch(r){I.error("头像上传失败")}},ce=async()=>{const e=await P.getProfile();a.value=e};return x(async()=>{_.value&&clearInterval(_.value),w.value&&clearInterval(w.value),await ce()}),(e,d)=>{const _=Y,w=G,V=U("Edit"),x=j,P=D,I=X,T=M,ce=U("Male"),pe=U("Female"),fe=O,ve=N,_e=B,be=H,ye=Z,ge=R,we=$,je=S;return f(),p("div",Q,[C(_e,{gutter:20},{default:g(()=>[C(T,{span:8},{default:g(()=>[C(I,{class:"user-card"},{default:g(()=>[E("div",ee,[E("div",ae,[C(_,{src:y(a).avatar,size:100},null,8,["src"]),C(w,{type:"info",class:"avatar-edit-btn",circle:"",icon:y(z),size:"small",onClick:ue},null,8,["icon"]),E("input",{ref_key:"fileInput",ref:ne,type:"file",style:{display:"none"},onChange:me},null,544)]),E("div",le,[E("span",se,F(y(a).nickname),1),C(x,{class:"edit-icon",onClick:d[0]||(d[0]=e=>K("account"))},{default:g(()=>[C(V)]),_:1})]),E("div",re,F(y(a).roleNames),1)]),C(P),d[16]||(d[16]=E("div",{class:"user-stats"},[E("div",{class:"stat-item"},[E("div",{class:"stat-value"},"0"),E("div",{class:"stat-label"},"待办")]),E("div",{class:"stat-item"},[E("div",{class:"stat-value"},"0"),E("div",{class:"stat-label"},"消息")]),E("div",{class:"stat-item"},[E("div",{class:"stat-value"},"0"),E("div",{class:"stat-label"},"通知")])],-1))]),_:1,__:[16]})]),_:1}),C(T,{span:16},{default:g(()=>[C(I,{class:"info-card"},{header:g(()=>d[17]||(d[17]=[E("div",{class:"card-header"},[E("span",null,"账号信息")],-1)])),default:g(()=>[C(ve,{column:1,border:""},{default:g(()=>[C(fe,{label:"用户名"},{default:g(()=>[q(F(y(a).username)+" ",1),1===y(a).gender?(f(),v(x,{key:0,class:"gender-icon male"},{default:g(()=>[C(ce)]),_:1})):(f(),v(x,{key:1,class:"gender-icon female"},{default:g(()=>[C(pe)]),_:1}))]),_:1}),C(fe,{label:"手机号码"},{default:g(()=>[q(F(y(a).mobile||"未绑定")+" ",1),y(a).mobile?(f(),v(w,{key:0,type:"primary",link:"",onClick:d[1]||(d[1]=()=>K("mobile"))},{default:g(()=>d[18]||(d[18]=[q(" 更换 ")])),_:1,__:[18]})):(f(),v(w,{key:1,type:"primary",link:"",onClick:d[2]||(d[2]=()=>K("mobile"))},{default:g(()=>d[19]||(d[19]=[q(" 绑定 ")])),_:1,__:[19]}))]),_:1}),C(fe,{label:"邮箱"},{default:g(()=>[q(F(y(a).email||"未绑定")+" ",1),y(a).email?(f(),v(w,{key:0,type:"primary",link:"",onClick:d[3]||(d[3]=()=>K("email"))},{default:g(()=>d[20]||(d[20]=[q(" 更换 ")])),_:1,__:[20]})):(f(),v(w,{key:1,type:"primary",link:"",onClick:d[4]||(d[4]=()=>K("email"))},{default:g(()=>d[21]||(d[21]=[q(" 绑定 ")])),_:1,__:[21]}))]),_:1}),C(fe,{label:"部门"},{default:g(()=>[q(F(y(a).deptName),1)]),_:1}),C(fe,{label:"创建时间"},{default:g(()=>[q(F(y(a).createTime),1)]),_:1})]),_:1})]),_:1}),C(I,{class:"security-card"},{header:g(()=>d[22]||(d[22]=[E("div",{class:"card-header"},[E("span",null,"安全设置")],-1)])),default:g(()=>[E("div",te,[d[24]||(d[24]=E("div",{class:"security-info"},[E("div",{class:"security-title"},"账户密码"),E("div",{class:"security-desc"},"定期修改密码有助于保护账户安全")],-1)),C(w,{type:"primary",link:"",onClick:d[5]||(d[5]=()=>K("password"))},{default:g(()=>d[23]||(d[23]=[q(" 修改 ")])),_:1,__:[23]})])]),_:1})]),_:1})]),_:1}),C(je,{modelValue:y(l).visible,"onUpdate:modelValue":d[15]||(d[15]=e=>y(l).visible=e),title:y(l).title,width:500},{footer:g(()=>[E("span",ie,[C(w,{onClick:de},{default:g(()=>d[25]||(d[25]=[q("取消")])),_:1,__:[25]}),C(w,{type:"primary",onClick:oe},{default:g(()=>d[26]||(d[26]=[q("确定")])),_:1,__:[26]})])]),default:g(()=>["account"===y(l).type?(f(),v(we,{key:0,ref_key:"userProfileFormRef",ref:s,model:y(o),"label-width":100},{default:g(()=>[C(ye,{label:"昵称"},{default:g(()=>[C(be,{modelValue:y(o).nickname,"onUpdate:modelValue":d[6]||(d[6]=e=>y(o).nickname=e)},null,8,["modelValue"])]),_:1}),C(ye,{label:"性别"},{default:g(()=>[C(ge,{modelValue:y(o).gender,"onUpdate:modelValue":d[7]||(d[7]=e=>y(o).gender=e),code:"gender"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])):A("",!0),"password"===y(l).type?(f(),v(we,{key:1,ref_key:"passwordChangeFormRef",ref:r,model:y(n),rules:k,"label-width":100},{default:g(()=>[C(ye,{label:"原密码",prop:"oldPassword"},{default:g(()=>[C(be,{modelValue:y(n).oldPassword,"onUpdate:modelValue":d[8]||(d[8]=e=>y(n).oldPassword=e),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),C(ye,{label:"新密码",prop:"newPassword"},{default:g(()=>[C(be,{modelValue:y(n).newPassword,"onUpdate:modelValue":d[9]||(d[9]=e=>y(n).newPassword=e),type:"password","show-password":""},null,8,["modelValue"])]),_:1}),C(ye,{label:"确认密码",prop:"confirmPassword"},{default:g(()=>[C(be,{modelValue:y(n).confirmPassword,"onUpdate:modelValue":d[10]||(d[10]=e=>y(n).confirmPassword=e),type:"password","show-password":""},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])):"mobile"===y(l).type?(f(),v(we,{key:2,ref_key:"mobileBindingFormRef",ref:t,model:y(u),rules:h,"label-width":100},{default:g(()=>[C(ye,{label:"手机号码",prop:"mobile"},{default:g(()=>[C(be,{modelValue:y(u).mobile,"onUpdate:modelValue":d[11]||(d[11]=e=>y(u).mobile=e),style:{width:"250px"}},null,8,["modelValue"])]),_:1}),C(ye,{label:"验证码",prop:"code"},{default:g(()=>[C(be,{modelValue:y(u).code,"onUpdate:modelValue":d[12]||(d[12]=e=>y(u).code=e),style:{width:"250px"}},{append:g(()=>[C(w,{disabled:y(c)>0,onClick:L},{default:g(()=>[q(F(y(c)>0?`${y(c)}s后重新发送`:"发送验证码"),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])):"email"===y(l).type?(f(),v(we,{key:3,ref_key:"emailBindingFormRef",ref:i,model:y(m),rules:J,"label-width":100},{default:g(()=>[C(ye,{label:"邮箱",prop:"email"},{default:g(()=>[C(be,{modelValue:y(m).email,"onUpdate:modelValue":d[13]||(d[13]=e=>y(m).email=e),style:{width:"250px"}},null,8,["modelValue"])]),_:1}),C(ye,{label:"验证码",prop:"code"},{default:g(()=>[C(be,{modelValue:y(m).code,"onUpdate:modelValue":d[14]||(d[14]=e=>y(m).code=e),style:{width:"250px"}},{append:g(()=>[C(w,{disabled:y(b)>0,onClick:W},{default:g(()=>[q(F(y(b)>0?`${y(b)}s后重新发送`:"发送验证码"),1)]),_:1},8,["disabled"])]),_:1},8,["modelValue"])]),_:1})]),_:1},8,["model"])):A("",!0)]),_:1},8,["modelValue","title"])])}}}),[["__scopeId","data-v-94997901"]]);export{oe as default};
