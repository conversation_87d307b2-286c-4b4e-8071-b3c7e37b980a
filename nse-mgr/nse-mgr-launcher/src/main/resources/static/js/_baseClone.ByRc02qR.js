import{k as t,g as e,s as r,b as a,a as n,c as o,n as c,i as s,d as b,e as i}from"./_Uint8Array.BCiDNJWl.js";import{c3 as u,ce as j,b$ as f,c4 as y,cf as l}from"./index.Ckm1SagX.js";import{c as p,k as A,g as v,a as d,b as m,d as g,e as w,i as x}from"./_initCloneObject.BsGr3vVr.js";import{a as O}from"./_arrayPush.Dbwejsrt.js";var S=Object.getOwnPropertySymbols?function(t){for(var r=[];t;)O(r,e(t)),t=v(t);return r}:r;function U(t){return a(t,A,S)}var h=Object.prototype.hasOwnProperty;var I=/\w*$/;var F=u?u.prototype:void 0,E=F?F.valueOf:void 0;function M(t,e,r){var a,n,o,c=t.constructor;switch(e){case"[object ArrayBuffer]":return d(t);case"[object Boolean]":case"[object Date]":return new c(+t);case"[object DataView]":return function(t,e){var r=e?d(t.buffer):t.buffer;return new t.constructor(r,t.byteOffset,t.byteLength)}(t,r);case"[object Float32Array]":case"[object Float64Array]":case"[object Int8Array]":case"[object Int16Array]":case"[object Int32Array]":case"[object Uint8Array]":case"[object Uint8ClampedArray]":case"[object Uint16Array]":case"[object Uint32Array]":return m(t,r);case"[object Map]":case"[object Set]":return new c;case"[object Number]":case"[object String]":return new c(t);case"[object RegExp]":return(o=new(n=t).constructor(n.source,I.exec(n))).lastIndex=n.lastIndex,o;case"[object Symbol]":return a=t,E?Object(E.call(a)):{}}}var B=c&&c.isMap,D=B?o(B):function(t){return j(t)&&"[object Map]"==n(t)};var k=c&&c.isSet,C=k?o(k):function(t){return j(t)&&"[object Set]"==n(t)},P="[object Arguments]",_="[object Function]",G="[object Object]",N={};function R(r,a,o,c,u,j){var v,d=1&a,m=2&a,O=4&a;if(o&&(v=u?o(r,c,u,j):o(r)),void 0!==v)return v;if(!f(r))return r;var I=y(r);if(I){if(v=function(t){var e=t.length,r=new t.constructor(e);return e&&"string"==typeof t[0]&&h.call(t,"index")&&(r.index=t.index,r.input=t.input),r}(r),!d)return g(r,v)}else{var F=n(r),E=F==_||"[object GeneratorFunction]"==F;if(s(r))return w(r,d);if(F==G||F==P||E&&!u){if(v=m||E?{}:x(r),!d)return m?function(t,e){return p(t,S(t),e)}(r,function(t,e){return t&&p(e,A(e),t)}(v,r)):function(t,r){return p(t,e(t),r)}(r,function(e,r){return e&&p(r,t(r),e)}(v,r))}else{if(!N[F])return u?r:{};v=M(r,F,d)}}j||(j=new b);var B=j.get(r);if(B)return B;j.set(r,v),C(r)?r.forEach(function(t){v.add(R(t,a,o,t,r,j))}):D(r)&&r.forEach(function(t,e){v.set(e,R(t,a,o,e,r,j))});var k=I?void 0:(O?m?U:i:m?A:t)(r);return function(t,e){for(var r=-1,a=null==t?0:t.length;++r<a&&!1!==e(t[r],r,t););}(k||r,function(t,e){k&&(t=r[e=t]),l(v,e,R(t,a,o,e,r,j))}),v}N[P]=N["[object Array]"]=N["[object ArrayBuffer]"]=N["[object DataView]"]=N["[object Boolean]"]=N["[object Date]"]=N["[object Float32Array]"]=N["[object Float64Array]"]=N["[object Int8Array]"]=N["[object Int16Array]"]=N["[object Int32Array]"]=N["[object Map]"]=N["[object Number]"]=N[G]=N["[object RegExp]"]=N["[object Set]"]=N["[object String]"]=N["[object Symbol]"]=N["[object Uint8Array]"]=N["[object Uint8ClampedArray]"]=N["[object Uint16Array]"]=N["[object Uint32Array]"]=!0,N["[object Error]"]=N[_]=N["[object WeakMap]"]=!1;export{R as b,U as g};
