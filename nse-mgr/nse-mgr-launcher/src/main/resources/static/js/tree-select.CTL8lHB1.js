import{E as e,a as t}from"./select.DHkh6uhw.js";import{E as l}from"./tree.BMRqnaD4.js";import{b as s,I as a,c as o,bs as d,a2 as n,d as r,B as c,ab as i,b5 as u,M as p,b6 as h,A as v,H as f,_ as k,r as m,V as y,o as C,a9 as b,q as N}from"./index.Ckm1SagX.js";import{U as g}from"./event.BwRzfsZt.js";import{p as K}from"./index.BLy3nyPI.js";import{e as x}from"./strings.By8NVWWL.js";import{i as E}from"./isEqual.CZKKciWh.js";import{s as O}from"./token.DWNpOE8r.js";const V=r({extends:t,setup(e,l){const s=t.setup(e,l);delete s.selectOptionClick;const o=c().proxy;return n(()=>{s.select.states.cachedOptions.get(o.value)||s.select.onOptionCreate(o)}),a(()=>l.attrs.visible,e=>{n(()=>{s.states.visible=e})},{immediate:!0}),s},methods:{selectOptionClick(){this.$el.parentElement.click()}}});function j(e){return e||0===e}function M(e){return i(e)&&e.length}function A(e){return i(e)?e:j(e)?[e]:[]}function S(e,t,l,s,a){for(let o=0;o<e.length;o++){const d=e[o];if(t(d,o,e,a))return s?s(d,o,e,a):d;{const e=l(d);if(M(e)){const a=S(e,t,l,s,d);if(a)return a}}}}function w(e,t,l,s){for(let a=0;a<e.length;a++){const o=e[a];t(o,a,e,s);const d=l(o);M(d)&&w(d,t,l,o)}}var H=r({props:{data:{type:Array,default:()=>[]}},setup(e){const t=v(O);return a(()=>e.data,()=>{var l;e.data.forEach(e=>{t.states.cachedOptions.has(e.value)||t.states.cachedOptions.set(e.value,e)});const s=(null==(l=t.selectRef)?void 0:l.querySelectorAll("input"))||[];f&&!Array.from(s).includes(document.activeElement)&&t.setSelected()},{flush:"post",immediate:!0}),()=>{}}});const L=N(k(r({name:"ElTreeSelect",inheritAttrs:!1,props:{...e.props,...l.props,cacheData:{type:Array,default:()=>[]}},setup(t,r){const{slots:c,expose:i}=r,v=m(),f=m(),k=o(()=>t.nodeKey||t.valueKey||"value"),N=((t,{attrs:l,emit:r},{select:c,tree:i,key:u})=>{const p=s("tree-select");return a(()=>t.data,()=>{t.filterable&&n(()=>{var e,t;null==(t=i.value)||t.filter(null==(e=c.value)?void 0:e.states.inputValue)})},{flush:"post"}),{...K(d(t),Object.keys(e.props)),...l,class:o(()=>l.class),style:o(()=>l.style),"onUpdate:modelValue":e=>r(g,e),valueKey:u,popperClass:o(()=>{const e=[p.e("popper")];return t.popperClass&&e.push(t.popperClass),e.join(" ")}),filterMethod:(e="")=>{var l;t.filterMethod?t.filterMethod(e):t.remoteMethod?t.remoteMethod(e):null==(l=i.value)||l.filter(e)}}})(t,r,{select:v,tree:f,key:k}),{cacheOptions:O,...L}=((e,{attrs:t,slots:s,emit:r},{select:c,tree:i,key:v})=>{a([()=>e.modelValue,i],()=>{e.showCheckbox&&n(()=>{const t=i.value;t&&!E(t.getCheckedKeys(),A(e.modelValue))&&t.setCheckedKeys(A(e.modelValue))})},{immediate:!0,deep:!0});const f=o(()=>({value:v.value,label:"label",children:"children",disabled:"disabled",isLeaf:"isLeaf",...e.props})),k=(e,t)=>{var l;const s=f.value[e];return h(s)?s(t,null==(l=i.value)?void 0:l.getNode(k("value",t))):t[s]},m=A(e.modelValue).map(t=>S(e.data||[],e=>k("value",e)===t,e=>k("children",e),(e,t,l,s)=>s&&k("value",s))).filter(e=>j(e)),y=o(()=>{if(!e.renderAfterExpand&&!e.lazy)return[];const t=[];return w(e.data.concat(e.cacheData),e=>{const l=k("value",e);t.push({value:l,currentLabel:k("label",e),isDisabled:k("disabled",e)})},e=>k("children",e)),t}),C=()=>{var e;return null==(e=i.value)?void 0:e.getCheckedKeys().filter(e=>{var t;const l=null==(t=i.value)?void 0:t.getNode(e);return!p(l)&&u(l.childNodes)})};return{...K(d(e),Object.keys(l.props)),...t,nodeKey:v,expandOnClickNode:o(()=>!e.checkStrictly&&e.expandOnClickNode),defaultExpandedKeys:o(()=>e.defaultExpandedKeys?e.defaultExpandedKeys.concat(m):m),renderContent:(t,{node:l,data:a,store:o})=>t(V,{value:k("value",a),label:k("label",a),disabled:k("disabled",a),visible:l.visible},e.renderContent?()=>e.renderContent(t,{node:l,data:a,store:o}):s.default?()=>s.default({node:l,data:a,store:o}):void 0),filterNodeMethod:(t,l,s)=>e.filterNodeMethod?e.filterNodeMethod(t,l,s):!t||new RegExp(x(t),"i").test(k("label",l)||""),onNodeClick:(l,s,a)=>{var o,d,n,r;if(null==(o=t.onNodeClick)||o.call(t,l,s,a),!e.showCheckbox||!e.checkOnClickNode){if(e.showCheckbox||!e.checkStrictly&&!s.isLeaf)e.expandOnClickNode&&a.proxy.handleExpandIconClick();else if(!k("disabled",l)){const e=null==(d=c.value)?void 0:d.states.options.get(k("value",l));null==(n=c.value)||n.handleOptionSelect(e)}null==(r=c.value)||r.focus()}},onCheck:(l,s)=>{var a;if(!e.showCheckbox)return;const o=k("value",l),d={};w([i.value.store.root],e=>d[e.key]=e,e=>e.childNodes);const u=s.checkedKeys,p=e.multiple?A(e.modelValue).filter(e=>!(e in d)&&!u.includes(e)):[],h=p.concat(u);if(e.checkStrictly)r(g,e.multiple?h:h.includes(o)?o:void 0);else if(e.multiple){const e=C();r(g,p.concat(e))}else{const t=S([l],e=>!M(k("children",e))&&!k("disabled",e),e=>k("children",e)),s=t?k("value",t):void 0,a=j(e.modelValue)&&!!S([l],t=>k("value",t)===e.modelValue,e=>k("children",e));r(g,s===e.modelValue||a?void 0:s)}n(()=>{var s;const a=A(e.modelValue);i.value.setCheckedKeys(a),null==(s=t.onCheck)||s.call(t,l,{checkedKeys:i.value.getCheckedKeys(),checkedNodes:i.value.getCheckedNodes(),halfCheckedKeys:i.value.getHalfCheckedKeys(),halfCheckedNodes:i.value.getHalfCheckedNodes()})}),null==(a=c.value)||a.focus()},onNodeExpand:(l,s,a)=>{var o;null==(o=t.onNodeExpand)||o.call(t,l,s,a),n(()=>{if(!e.checkStrictly&&e.lazy&&e.multiple&&s.checked){const t={},l=i.value.getCheckedKeys();w([i.value.store.root],e=>t[e.key]=e,e=>e.childNodes);const s=A(e.modelValue).filter(e=>!(e in t)&&!l.includes(e)),a=C();r(g,s.concat(a))}})},cacheOptions:y}})(t,r,{select:v,tree:f,key:k}),q=y({});return i(q),C(()=>{Object.assign(q,{...K(f.value,["filter","updateKeyChildren","getCheckedNodes","setCheckedNodes","getCheckedKeys","setCheckedKeys","setChecked","getHalfCheckedNodes","getHalfCheckedKeys","getCurrentKey","getCurrentNode","setCurrentKey","setCurrentNode","getNode","remove","append","insertBefore","insertAfter"]),...K(v.value,["focus","blur","selectedLabel"])})}),()=>b(e,y({...N,ref:e=>v.value=e}),{...c,default:()=>[b(H,{data:O.value}),b(l,y({...L,ref:e=>f.value=e}))]})}}),[["__file","tree-select.vue"]]));export{L as E};
