import{d as e,ax as a,az as t,I as i,e as o,w as p,C as l,m as n,n as u,f as r}from"./index.Ckm1SagX.js";import{E as s}from"./scrollbar.6rbryiG1.js";import{E as d}from"./pagination.TL6aFrlm.js";/* empty css            */import"./select.DHkh6uhw.js";import"./popper.DpZVcW1M.js";/* empty css              */import{_ as g}from"./_plugin-vue_export-helper.BCo6x5W8.js";const m=g(e({__name:"index",props:a({total:{type:Number,default:0},pageSizes:{type:Array,default:()=>[10,20,30,50]},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:<PERSON><PERSON>an,default:!1}},{page:{type:Number,required:!0,default:1},pageModifiers:{},limit:{type:Number,required:!0,default:10},limitModifiers:{}}),emits:a(["pagination"],["update:page","update:limit"]),setup(e,{emit:a}){const g=e,m=a,c=t(e,"page"),f=t(e,"limit");function v(e){c.value=1,m("pagination",{page:c.value,limit:e})}function y(e){m("pagination",{page:e,limit:f.value})}return i(()=>g.total,e=>{const a=Math.ceil(e/f.value);e>0&&c.value>a&&(c.value=a,m("pagination",{page:c.value,limit:f.value}))}),(a,t)=>{const i=d,g=s;return r(),o(g,null,{default:p(()=>[l("div",{class:u([{hidden:e.hidden},"pagination"])},[n(i,{"current-page":c.value,"onUpdate:currentPage":t[0]||(t[0]=e=>c.value=e),"page-size":f.value,"onUpdate:pageSize":t[1]||(t[1]=e=>f.value=e),background:e.background,layout:e.layout,"page-sizes":e.pageSizes,total:e.total,onSizeChange:v,onCurrentChange:y},null,8,["current-page","page-size","background","layout","page-sizes","total"])],2)]),_:1})}}}),[["__scopeId","data-v-ded982e5"]]);export{m as _};
