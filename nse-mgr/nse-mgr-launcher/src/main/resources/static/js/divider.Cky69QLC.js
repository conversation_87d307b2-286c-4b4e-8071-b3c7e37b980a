import{t,z as e,_ as s,d as i,b as a,c as r,g as o,f as l,h as n,n as d,i as c,l as v,k as f,q as u}from"./index.Ckm1SagX.js";const p=t({direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},contentPosition:{type:String,values:["left","center","right"],default:"center"},borderStyle:{type:e(String),default:"solid"}}),y=i({name:"ElDivider"});const b=u(s(i({...y,props:p,setup(t){const e=t,s=a("divider"),i=r(()=>s.cssVar({"border-style":e.borderStyle}));return(t,e)=>(l(),o("div",{class:d([c(s).b(),c(s).m(t.direction)]),style:f(c(i)),role:"separator"},[t.$slots.default&&"vertical"!==t.direction?(l(),o("div",{key:0,class:d([c(s).e("text"),c(s).is(t.contentPosition)])},[v(t.$slots,"default")],2)):n("v-if",!0)],6))}}),[["__file","divider.vue"]]));export{b as E};
