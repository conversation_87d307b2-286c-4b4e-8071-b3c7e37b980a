import{r as e,V as t,cE as n,bs as o,d as s,bQ as l,a9 as i,w as r,X as a,m as u,Z as c,T as d,a5 as v,H as b,a2 as g,L as m,bh as f,a3 as p,ac as x,aC as y,cF as C}from"./index.Ckm1SagX.js";function k(b,g){let m;const f=e(!1),p=t({...b,originalPosition:"",originalOverflow:"",visible:!1});function x(){var e,t;null==(t=null==(e=A.$el)?void 0:e.parentNode)||t.removeChild(A.$el)}function y(){if(!f.value)return;const e=p.parent;f.value=!1,e.vLoadingAddClassList=void 0,function(){const e=p.parent,t=A.ns;if(!e.vLoadingAddClassList){let n=e.getAttribute("loading-number");n=Number.parseInt(n)-1,n?e.setAttribute("loading-number",n.toString()):(v(e,t.bm("parent","relative")),e.removeAttribute("loading-number")),v(e,t.bm("parent","hidden"))}x(),k.unmount()}()}const C=s({name:"ElLoading",setup(e,{expose:t}){const{ns:n,zIndex:o}=l("loading");return t({ns:n,zIndex:o}),()=>{const e=p.spinner||p.svg,t=i("svg",{class:"circular",viewBox:p.svgViewBox?p.svgViewBox:"0 0 50 50",...e?{innerHTML:e}:{}},[i("circle",{class:"path",cx:"25",cy:"25",r:"20",fill:"none"})]),o=p.text?i("p",{class:n.b("text")},[p.text]):void 0;return i(d,{name:n.b("fade"),onAfterLeave:y},{default:r(()=>[a(u("div",{style:{backgroundColor:p.background||""},class:[n.b("mask"),p.customClass,p.fullscreen?"is-fullscreen":""]},[i("div",{class:n.b("spinner")},[t,o])]),[[c,p.visible]])])})}}}),k=n(C);Object.assign(k._context,null!=g?g:{});const A=k.mount(document.createElement("div"));return{...o(p),setText:function(e){p.text=e},removeElLoadingChild:x,close:function(){var e;b.beforeClose&&!b.beforeClose()||(f.value=!0,clearTimeout(m),m=setTimeout(y,400),p.visible=!1,null==(e=b.closed)||e.call(b))},handleAfterLeave:y,vm:A,get $el(){return A.$el}}}let A;const w=function(e={}){if(!b)return;const t=L(e);if(t.fullscreen&&A)return A;const n=k({...t,closed:()=>{var e;null==(e=t.closed)||e.call(t),t.fullscreen&&(A=void 0)}},w._context);h(t,t.parent,n),B(t,t.parent,n),t.parent.vLoadingAddClassList=()=>B(t,t.parent,n);let o=t.parent.getAttribute("loading-number");return o=o?`${Number.parseInt(o)+1}`:"1",t.parent.setAttribute("loading-number",o),t.parent.appendChild(n.$el),g(()=>n.visible.value=t.visible),t.fullscreen&&(A=n),n},L=e=>{var t,n,o,s;let l;return l=m(e.target)?null!=(t=document.querySelector(e.target))?t:document.body:e.target||document.body,{parent:l===document.body||e.body?document.body:l,background:e.background||"",svg:e.svg||"",svgViewBox:e.svgViewBox||"",spinner:e.spinner||!1,text:e.text||"",fullscreen:l===document.body&&(null==(n=e.fullscreen)||n),lock:null!=(o=e.lock)&&o,customClass:e.customClass||"",visible:null==(s=e.visible)||s,beforeClose:e.beforeClose,closed:e.closed,target:l}},h=async(e,t,n)=>{const{nextZIndex:o}=n.vm.zIndex||n.vm._.exposed.zIndex,s={};if(e.fullscreen)n.originalPosition.value=f(document.body,"position"),n.originalOverflow.value=f(document.body,"overflow"),s.zIndex=o();else if(e.parent===document.body){n.originalPosition.value=f(document.body,"position"),await g();for(const t of["top","left"]){const n="top"===t?"scrollTop":"scrollLeft";s[t]=e.target.getBoundingClientRect()[t]+document.body[n]+document.documentElement[n]-Number.parseInt(f(document.body,`margin-${t}`),10)+"px"}for(const t of["height","width"])s[t]=`${e.target.getBoundingClientRect()[t]}px`}else n.originalPosition.value=f(t,"position");for(const[l,i]of Object.entries(s))n.$el.style[l]=i},B=(e,t,n)=>{const o=n.vm.ns||n.vm._.exposed.ns;["absolute","fixed","sticky"].includes(n.originalPosition.value)?v(t,o.bm("parent","relative")):p(t,o.bm("parent","relative")),e.fullscreen&&e.lock?p(t,o.bm("parent","hidden")):v(t,o.bm("parent","hidden"))};w._context=null;const $=Symbol("ElLoading"),I=e=>`element-loading-${C(e)}`,V=(t,n)=>{var o,s,l,i;const r=n.instance,a=e=>x(n.value)?n.value[e]:void 0,u=n=>(t=>{const n=m(t)&&(null==r?void 0:r[t])||t;return e(n)})(a(n)||t.getAttribute(I(n))),c=null!=(o=a("fullscreen"))?o:n.modifiers.fullscreen,d={text:u("text"),svg:u("svg"),svgViewBox:u("svgViewBox"),spinner:u("spinner"),background:u("background"),customClass:u("customClass"),fullscreen:c,target:null!=(s=a("target"))?s:c?void 0:t,body:null!=(l=a("body"))?l:n.modifiers.body,lock:null!=(i=a("lock"))?i:n.modifiers.lock},v=w(d);v._context=_._context,t[$]={options:d,instance:v}},_={mounted(e,t){t.value&&V(e,t)},updated(e,t){const n=e[$];if(!t.value)return null==n||n.instance.close(),void(e[$]=null);n?((e,t)=>{for(const n of Object.keys(e))y(e[n])&&(e[n].value=t[n])})(n.options,x(t.value)?t.value:{text:e.getAttribute(I("text")),svg:e.getAttribute(I("svg")),svgViewBox:e.getAttribute(I("svgViewBox")),spinner:e.getAttribute(I("spinner")),background:e.getAttribute(I("background")),customClass:e.getAttribute(I("customClass"))}):V(e,t)},unmounted(e){var t;null==(t=e[$])||t.instance.close(),e[$]=null},_context:null};export{w as L,_ as v};
