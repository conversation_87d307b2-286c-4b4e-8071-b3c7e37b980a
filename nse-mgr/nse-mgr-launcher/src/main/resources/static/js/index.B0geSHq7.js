import{ab as n,bB as e,H as t}from"./index.Ckm1SagX.js";const o=new Map;if(t){let n;document.addEventListener("mousedown",e=>n=e),document.addEventListener("mouseup",e=>{if(n){for(const t of o.values())for(const{documentHandler:o}of t)o(e,n);n=void 0}})}function s(t,o){let s=[];return n(o.arg)?s=o.arg:e(o.arg)&&s.push(o.arg),function(n,e){const a=o.instance.popperRef,d=n.target,i=null==e?void 0:e.target,u=!o||!o.instance,c=!d||!i,l=t.contains(d)||t.contains(i),r=t===d,g=s.length&&s.some(n=>null==n?void 0:n.contains(d))||s.length&&s.includes(i),f=a&&(a.contains(d)||a.contains(i));u||c||l||r||g||f||o.value(n,e)}}const a={beforeMount(n,e){o.has(n)||o.set(n,[]),o.get(n).push({documentHandler:s(n,e),bindingFn:e.value})},updated(n,e){o.has(n)||o.set(n,[]);const t=o.get(n),a=t.findIndex(n=>n.bindingFn===e.oldValue),d={documentHandler:s(n,e),bindingFn:e.value};a>=0?t.splice(a,1,d):t.push(d)},unmounted(n){o.delete(n)}};export{a as C};
