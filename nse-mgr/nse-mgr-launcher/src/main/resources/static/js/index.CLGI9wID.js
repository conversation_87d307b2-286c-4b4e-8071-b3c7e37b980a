import{d as e,aD as a,am as l,aF as t,c as s,r as o,ap as i,g as r,f as n,C as p,h as u,e as c,m as d,w as m,a1 as v,E as f,X as j,i as y,b1 as b,F as x,Z as _,k as g,b2 as h,b3 as k,Q as C,aP as w,a2 as E}from"./index.Ckm1SagX.js";import{E as T}from"./pagination.TL6aFrlm.js";/* empty css            */import"./select.DHkh6uhw.js";import"./scrollbar.6rbryiG1.js";import"./popper.DpZVcW1M.js";/* empty css              */import{a as U,E as z}from"./table-column.DQa6-hu-.js";import"./checkbox.CyAsOZKA.js";/* empty css                */import{E as L}from"./upload.CnbKCtkP.js";import"./progress.ChnKapv7.js";import{E as A,a as R}from"./step.BCyHofXl.js";/* empty css               */import{A as I}from"./account.api.C-wwvVop.js";import{E as D}from"./index.CbYeWxT8.js";import{E as F}from"./index.DCgMX_7R.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./isEqual.CZKKciWh.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./index.4JfkAhur.js";import"./index.BLy3nyPI.js";import"./event.BwRzfsZt.js";import"./index.DJHzyRe5.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./index.Byj-i824.js";import"./aria.C1IWO_Rd.js";import"./index.BRUQ9gWw.js";import"./index.BPj3iklg.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./castArray.Chmjnshw.js";import"./scroll.XdyICIdv.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.B0geSHq7.js";import"./vnode.BkZiIFpS.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./_baseClone.ByRc02qR.js";import"./index.DeprZc7T.js";import"./directive.C7vihscI.js";const $={class:"import-container"},O={class:"upload-area"},P={key:0,style:{display:"flex","align-items":"center"}},H={key:1,class:"upload-area"},V={class:"upload-box"},q={class:"upload-trigger"},B={key:0,class:"file-info"},M={class:"file-name"},N={class:"file-actions"},X={class:"step-actions"},Z={key:2,class:"preview-area"},G=["innerHTML"],J={class:"step-actions"},K={key:3,class:"complete-area"},Q={class:"complete-message"},W={key:0,class:"preview-table"},Y={class:"pagination-container"},ee=S(e({__name:"index",setup(e){const S=a(),ee=l(),ae=t(),le=s(()=>ee.query.userType),te=o(1),se=()=>{te.value<3&&(te.value++,2===te.value&&xe())},oe=()=>{te.value>1&&te.value--},ie=o(""),re=s(()=>"上传成功"===ie.value),ne=s(()=>{if(!fe.value||!fe.value.length)return"暂无数据";const e=fe.value.reduce((e,a)=>{const l=a.importInstruction;return e[l]=(e[l]||0)+1,e},{}),a=fe.value.length,l=e["正常"]||0;if(a===l)return`请核对下列数据，本次一共创建${a}个账号，如需修改请点击“上一步”，确认无误请点击导入`;let t=`请核对下列数据，本次一共创建${a}个账号，<span style="color: rgba(245,108,108,1);">其中`;const s=[];for(const[o,i]of Object.entries(e))"正常"!==o&&s.push(`${i}个账号${o}`);return t+=s.join("，")+`，将创建${l}个账号</span>，如需修改请点击"上一步"，确认无误请点击导入`,t}),pe=o(),ue=o(),ce=e=>{const a=e.raw;if(!a)return;if(!/\.(xlsx|xls)$/.test(a.name.toLowerCase()))return void w.error("只能上传 Excel 文件！");const l=F.service({});I.importData(a,le.value).then(e=>{var a;ie.value=e.message,fe.value=e.data,be.value=null==(a=e.data)?void 0:a.length,je.value=1,ye.value.pageSize=10}).finally(()=>{ue.value=a,l.close()})},de=()=>{ue.value=void 0,pe.value&&pe.value.clearFiles()},me=()=>{I.downloadTemplate(le.value).then(e=>{const a=e.data,l=decodeURI(e.headers["content-disposition"].split(";")[1].split("=")[1]),t=new Blob([a],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=utf-8"}),s=window.URL.createObjectURL(t),o=document.createElement("a");o.href=s,o.download=l,document.body.appendChild(o),o.click(),document.body.removeChild(o),window.URL.revokeObjectURL(s)})},ve=o(1),fe=o([]),je=o(1),ye=o(10),be=o(0),xe=()=>{ve.value=fe.value.slice((je.value-1)*ye.value,je.value*ye.value)},_e=e=>{ye.value=e,xe()},ge=e=>{je.value=e,xe()},he=k(()=>{const e=fe.value.filter(e=>"正常"===e.importInstruction);if(!e.length)return void w.error("没有可导入的数据");const a=F.service({});I.batchAdd(e).then(()=>{te.value=3}).finally(()=>{a.close()})},500),ke=()=>{if(ae.closeCurrentView(),"TEACHER"===le.value){const e={name:"Administrator"};return ae.delCachedView(e),void E(()=>{S.push("/account/administrator")})}ae.delCachedView({name:"NormalUser"}),E(()=>{S.push("/account/normal-user")})};return(e,a)=>{const l=i("ArrowLeftBold"),t=f,s=D,o=A,k=R,w=i("UploadFilled"),E=L,I=i("CircleCheck"),F=i("CircleClose"),S=i("Close"),ee=i("CircleCloseFilled"),ae=U,fe=z,xe=T;return n(),r("div",$,[p("div",O,[3===te.value?(n(),c(s,{key:0,onClick:ke},{default:m(()=>[d(t,null,{default:m(()=>[d(l)]),_:1}),a[2]||(a[2]=v(" 返回 "))]),_:1,__:[2]})):u("",!0),d(k,{active:te.value,"align-center":"",class:"import-steps"},{default:m(()=>[d(o,{title:"选择文件"},{description:m(()=>[1===te.value?(n(),r("div",P,[a[4]||(a[4]=p("span",{style:{color:"#161614"}},"导入模板下载：",-1)),d(s,{style:{position:"relative","font-size":"12px"},type:"primary",link:"",onClick:me},{default:m(()=>a[3]||(a[3]=[v(" 账号导入模板.xlsx ")])),_:1,__:[3]})])):u("",!0)]),_:1}),d(o,{title:"预览数据"}),d(o,{title:"完成"})]),_:1},8,["active"]),1===te.value?(n(),r("div",H,[p("div",V,[d(E,{ref_key:"uploadRef",ref:pe,drag:"",class:"upload-component",action:"","auto-upload":!1,"show-file-list":!1,"on-change":ce,accept:".xls,.xlsx"},{default:m(()=>[p("div",q,[d(t,{class:"upload-icon"},{default:m(()=>[d(w)]),_:1}),a[5]||(a[5]=p("div",{class:"upload-text"},[v(" 将文件拖到此处，或 "),p("em",null,"点击上传")],-1))])]),_:1},512),a[6]||(a[6]=p("div",{class:"upload-tip"},[p("p",null,"为保证数据正确，请务必按照模板格式导入！"),p("p",null,"每次仅允许导入一份文件，新上传的文件会自动替换旧文件~")],-1)),ue.value?(n(),r("div",B,[p("div",M,[d(t,null,{default:m(()=>[d(y(b))]),_:1}),p("span",null,x(ue.value.name),1)]),p("div",N,[y(re)?(n(),c(t,{key:0,style:{color:"rgba(103, 194, 58, 1)"}},{default:m(()=>[d(I)]),_:1})):(n(),c(t,{key:1},{default:m(()=>[d(F)]),_:1})),d(t,{onClick:de},{default:m(()=>[d(S)]),_:1})])])):u("",!0),j(p("div",{style:g({color:y(re)?"rgba(103, 194, 58, 1)":""}),class:"upload-result"},[y(re)?(n(),c(t,{key:0},{default:m(()=>[d(y(h))]),_:1})):(n(),c(t,{key:1},{default:m(()=>[d(ee)]),_:1})),v(" "+x(ie.value),1)],4),[[_,ie.value&&ue.value]])]),p("div",X,[d(s,{type:"primary",disabled:!y(re),onClick:se},{default:m(()=>a[7]||(a[7]=[v("下一步")])),_:1,__:[7]},8,["disabled"])])])):u("",!0),2===te.value?(n(),r("div",Z,[p("div",{style:{"margin-top":"20px","margin-bottom":"30px","font-size":"12px","text-align":"center"},innerHTML:y(ne)},null,8,G),p("div",J,[d(s,{onClick:oe},{default:m(()=>a[8]||(a[8]=[v("上一步")])),_:1,__:[8]}),d(s,{type:"primary",onClick:y(he)},{default:m(()=>a[9]||(a[9]=[v("导入")])),_:1,__:[9]},8,["onClick"])])])):u("",!0),3===te.value?(n(),r("div",K,[p("div",Q,[d(t,{class:"success-icon"},{default:m(()=>[d(y(h))]),_:1}),a[10]||(a[10]=v(" 导入成功 "))]),p("p",null,"登录账号为"+x("STUDENT"===y(le)?"学号":"工号")+"，默认密码为：admin123",1)])):u("",!0)]),2===te.value?(n(),r("div",W,[a[11]||(a[11]=p("p",null,"预览数据",-1)),d(fe,{data:ve.value,border:"",style:{width:"100%",height:"calc(100% - 90px)"}},{default:m(()=>[d(ae,{type:"index",label:"序号",width:"70"}),"STUDENT"===y(le)?(n(),r(C,{key:0},[d(ae,{prop:"account",label:"学号"}),d(ae,{prop:"userTypeText",label:"角色"}),d(ae,{prop:"name",label:"姓名"}),d(ae,{prop:"classes",label:"班级"}),d(ae,{prop:"validDate",label:"账号失效日期"})],64)):(n(),r(C,{key:1},[d(ae,{prop:"account",label:"工号"}),d(ae,{prop:"userTypeText",label:"角色"}),d(ae,{prop:"name",label:"姓名"}),d(ae,{prop:"mobilePhone",label:"联系方式"})],64)),d(ae,{prop:"importInstruction",label:"导入说明"})]),_:1},8,["data"]),p("div",Y,[d(xe,{"current-page":je.value,"onUpdate:currentPage":a[0]||(a[0]=e=>je.value=e),"page-size":ye.value,"onUpdate:pageSize":a[1]||(a[1]=e=>ye.value=e),total:be.value,"page-sizes":[10,30,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:_e,onCurrentChange:ge},null,8,["current-page","page-size","total"])])])):u("",!0)])}}}),[["__scopeId","data-v-8d1c8413"]]);export{ee as default};
