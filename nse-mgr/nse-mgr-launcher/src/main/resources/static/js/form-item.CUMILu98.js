import{a as e,d as r,f as t}from"./use-form-common-props.BSYTvb6G.js";import{ab as n,L as i,ba as a,t as l,z as s,aW as u,r as o,c as f,d,V as c,b as p,I as v,y as g,bs as h,g as y,f as m,l as b,n as w,i as q,b6 as F,_ as O,A as x,o as j,a8 as E,by as A,ad as P,m as S,Q as k,a2 as R,b8 as $,ca as _,J as I,cb as V,C as W,w as M,e as B,h as z,D as C,k as D,a1 as L,F as N,bt as T,q as J,G as Z}from"./index.Ckm1SagX.js";import{c as G}from"./castArray.Chmjnshw.js";import{d as Y,t as Q}from"./aria.C1IWO_Rd.js";import{u as U}from"./index.Dh_vcBr5.js";import{b as H}from"./_baseClone.ByRc02qR.js";function K(e){return H(e,4)}const X=l({size:{type:String,values:u},disabled:Boolean}),ee=l({...X,model:Object,rules:{type:s(Object)},labelPosition:{type:String,values:["left","right","top"],default:"right"},requireAsteriskPosition:{type:String,values:["left","right"],default:"left"},labelWidth:{type:[String,Number],default:""},labelSuffix:{type:String,default:""},inline:Boolean,inlineMessage:Boolean,statusIcon:Boolean,showMessage:{type:Boolean,default:!0},validateOnRuleChange:{type:Boolean,default:!0},hideRequiredAsterisk:Boolean,scrollToError:Boolean,scrollIntoViewOptions:{type:[Object,Boolean],default:!0}}),re={validate:(e,r,t)=>(n(e)||i(e))&&a(r)&&i(t)};function te(){const e=o([]),r=f(()=>{if(!e.value.length)return"0";const r=Math.max(...e.value);return r?`${r}px`:""});function t(t){const n=e.value.indexOf(t);return-1===n&&r.value,n}return{autoLabelWidth:r,registerLabelWidth:function(r,n){if(r&&n){const i=t(n);e.value.splice(i,1,r)}else r&&e.value.push(r)},deregisterLabelWidth:function(r){const n=t(r);n>-1&&e.value.splice(n,1)}}}const ne=(e,r)=>{const t=G(r).map(e=>n(e)?e.join("."):e);return t.length>0?e.filter(e=>e.propString&&t.includes(e.propString)):e},ie=d({name:"ElForm"});var ae=O(d({...ie,props:ee,emits:re,setup(t,{expose:n,emit:i}){const a=t,l=o(),s=c([]),u=e(),d=p("form"),O=f(()=>{const{labelPosition:e,inline:r}=a;return[d.b(),d.m(u.value||"default"),{[d.m(`label-${e}`)]:e,[d.m("inline")]:r}]}),x=e=>ne(s,[e])[0],j=(e=[])=>{a.model&&ne(s,e).forEach(e=>e.resetField())},E=(e=[])=>{ne(s,e).forEach(e=>e.clearValidate())},A=f(()=>!!a.model),P=async e=>k(void 0,e),S=async(e=[])=>{if(!A.value)return!1;const r=(e=>{if(0===s.length)return[];const r=ne(s,e);return r.length?r:[]})(e);if(0===r.length)return!0;let t={};for(const i of r)try{await i.validate(""),"error"===i.validateState&&i.resetField()}catch(n){t={...t,...n}}return 0===Object.keys(t).length||Promise.reject(t)},k=async(e=[],r)=>{let t=!1;const n=!F(r);try{return t=await S(e),!0===t&&await(null==r?void 0:r(t)),t}catch(i){if(i instanceof Error)throw i;const e=i;if(a.scrollToError&&l.value){const e=l.value.querySelector(`.${d.b()}-item.is-error`);null==e||e.scrollIntoView(a.scrollIntoViewOptions)}return!t&&await(null==r?void 0:r(!1,e)),n&&Promise.reject(e)}};return v(()=>a.rules,()=>{a.validateOnRuleChange&&P().catch(e=>Y())},{deep:!0,flush:"post"}),g(r,c({...h(a),emit:i,resetFields:j,clearValidate:E,validateField:k,getField:x,addField:e=>{s.push(e)},removeField:e=>{e.prop&&s.splice(s.indexOf(e),1)},...te()})),n({validate:P,validateField:k,resetFields:j,clearValidate:E,scrollToField:e=>{var r;const t=x(e);t&&(null==(r=t.$el)||r.scrollIntoView(a.scrollIntoViewOptions))},getField:x,fields:s}),(e,r)=>(m(),y("form",{ref_key:"formRef",ref:l,class:w(q(O))},[b(e.$slots,"default")],2))}}),[["__file","form.vue"]]);function le(){return le=Object.assign?Object.assign.bind():function(e){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])}return e},le.apply(this,arguments)}function se(e){return(se=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)})(e)}function ue(e,r){return(ue=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,r){return e.__proto__=r,e})(e,r)}function oe(e,r,t){return(oe=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch(e){return!1}}()?Reflect.construct.bind():function(e,r,t){var n=[null];n.push.apply(n,r);var i=new(Function.bind.apply(e,n));return t&&ue(i,t.prototype),i}).apply(null,arguments)}function fe(e){var r="function"==typeof Map?new Map:void 0;return fe=function(e){if(null===e||(t=e,-1===Function.toString.call(t).indexOf("[native code]")))return e;var t;if("function"!=typeof e)throw new TypeError("Super expression must either be null or a function");if(void 0!==r){if(r.has(e))return r.get(e);r.set(e,n)}function n(){return oe(e,arguments,se(this).constructor)}return n.prototype=Object.create(e.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),ue(n,e)},fe(e)}var de=/%[sdj%]/g;function ce(e){if(!e||!e.length)return null;var r={};return e.forEach(function(e){var t=e.field;r[t]=r[t]||[],r[t].push(e)}),r}function pe(e){for(var r=arguments.length,t=new Array(r>1?r-1:0),n=1;n<r;n++)t[n-1]=arguments[n];var i=0,a=t.length;return"function"==typeof e?e.apply(null,t):"string"==typeof e?e.replace(de,function(e){if("%%"===e)return"%";if(i>=a)return e;switch(e){case"%s":return String(t[i++]);case"%d":return Number(t[i++]);case"%j":try{return JSON.stringify(t[i++])}catch(r){return"[Circular]"}break;default:return e}}):e}function ve(e,r){return null==e||(!("array"!==r||!Array.isArray(e)||e.length)||!(!function(e){return"string"===e||"url"===e||"hex"===e||"email"===e||"date"===e||"pattern"===e}(r)||"string"!=typeof e||e))}function ge(e,r,t){var n=0,i=e.length;!function a(l){if(l&&l.length)t(l);else{var s=n;n+=1,s<i?r(e[s],a):t([])}}([])}var he=function(e){var r,t;function n(r,t){var n;return(n=e.call(this,"Async Validation Error")||this).errors=r,n.fields=t,n}return t=e,(r=n).prototype=Object.create(t.prototype),r.prototype.constructor=r,ue(r,t),n}(fe(Error));function ye(e,r,t,n,i){if(r.first){var a=new Promise(function(r,a){var l=function(e){var r=[];return Object.keys(e).forEach(function(t){r.push.apply(r,e[t]||[])}),r}(e);ge(l,t,function(e){return n(e),e.length?a(new he(e,ce(e))):r(i)})});return a.catch(function(e){return e}),a}var l=!0===r.firstFields?Object.keys(e):r.firstFields||[],s=Object.keys(e),u=s.length,o=0,f=[],d=new Promise(function(r,a){var d=function(e){if(f.push.apply(f,e),++o===u)return n(f),f.length?a(new he(f,ce(f))):r(i)};s.length||(n(f),r(i)),s.forEach(function(r){var n=e[r];-1!==l.indexOf(r)?ge(n,t,d):function(e,r,t){var n=[],i=0,a=e.length;function l(e){n.push.apply(n,e||[]),++i===a&&t(n)}e.forEach(function(e){r(e,l)})}(n,t,d)})});return d.catch(function(e){return e}),d}function me(e,r){return function(t){var n,i;return n=e.fullFields?function(e,r){for(var t=e,n=0;n<r.length;n++){if(null==t)return t;t=t[r[n]]}return t}(r,e.fullFields):r[t.field||e.fullField],(i=t)&&void 0!==i.message?(t.field=t.field||e.fullField,t.fieldValue=n,t):{message:"function"==typeof t?t():t,fieldValue:n,field:t.field||e.fullField}}}function be(e,r){if(r)for(var t in r)if(r.hasOwnProperty(t)){var n=r[t];"object"==typeof n&&"object"==typeof e[t]?e[t]=le({},e[t],n):e[t]=n}return e}var we,qe=function(e,r,t,n,i,a){!e.required||t.hasOwnProperty(e.field)&&!ve(r,a||e.type)||n.push(pe(i.messages.required,e.fullField))},Fe=/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+\.)+[a-zA-Z\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]{2,}))$/,Oe=/^#?([a-f0-9]{6}|[a-f0-9]{3})$/i,xe={integer:function(e){return xe.number(e)&&parseInt(e,10)===e},float:function(e){return xe.number(e)&&!xe.integer(e)},array:function(e){return Array.isArray(e)},regexp:function(e){if(e instanceof RegExp)return!0;try{return!!new RegExp(e)}catch(r){return!1}},date:function(e){return"function"==typeof e.getTime&&"function"==typeof e.getMonth&&"function"==typeof e.getYear&&!isNaN(e.getTime())},number:function(e){return!isNaN(e)&&"number"==typeof e},object:function(e){return"object"==typeof e&&!xe.array(e)},method:function(e){return"function"==typeof e},email:function(e){return"string"==typeof e&&e.length<=320&&!!e.match(Fe)},url:function(e){return"string"==typeof e&&e.length<=2048&&!!e.match(function(){if(we)return we;var e="[a-fA-F\\d:]",r=function(r){return r&&r.includeBoundaries?"(?:(?<=\\s|^)(?="+e+")|(?<="+e+")(?=\\s|$))":""},t="(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}",n="[a-fA-F\\d]{1,4}",i=("\n(?:\n(?:"+n+":){7}(?:"+n+"|:)|                                    // 1:2:3:4:5:6:7::  1:2:3:4:5:6:7:8\n(?:"+n+":){6}(?:"+t+"|:"+n+"|:)|                             // 1:2:3:4:5:6::    1:2:3:4:5:6::8   1:2:3:4:5:6::8  1:2:3:4:5:6::1.2.3.4\n(?:"+n+":){5}(?::"+t+"|(?::"+n+"){1,2}|:)|                   // 1:2:3:4:5::      1:2:3:4:5::7:8   1:2:3:4:5::8    1:2:3:4:5::7:1.2.3.4\n(?:"+n+":){4}(?:(?::"+n+"){0,1}:"+t+"|(?::"+n+"){1,3}|:)| // 1:2:3:4::        1:2:3:4::6:7:8   1:2:3:4::8      1:2:3:4::6:7:1.2.3.4\n(?:"+n+":){3}(?:(?::"+n+"){0,2}:"+t+"|(?::"+n+"){1,4}|:)| // 1:2:3::          1:2:3::5:6:7:8   1:2:3::8        1:2:3::5:6:7:1.2.3.4\n(?:"+n+":){2}(?:(?::"+n+"){0,3}:"+t+"|(?::"+n+"){1,5}|:)| // 1:2::            1:2::4:5:6:7:8   1:2::8          1:2::4:5:6:7:1.2.3.4\n(?:"+n+":){1}(?:(?::"+n+"){0,4}:"+t+"|(?::"+n+"){1,6}|:)| // 1::              1::3:4:5:6:7:8   1::8            1::3:4:5:6:7:1.2.3.4\n(?::(?:(?::"+n+"){0,5}:"+t+"|(?::"+n+"){1,7}|:))             // ::2:3:4:5:6:7:8  ::2:3:4:5:6:7:8  ::8             ::1.2.3.4\n)(?:%[0-9a-zA-Z]{1,})?                                             // %eth0            %1\n").replace(/\s*\/\/.*$/gm,"").replace(/\n/g,"").trim(),a=new RegExp("(?:^"+t+"$)|(?:^"+i+"$)"),l=new RegExp("^"+t+"$"),s=new RegExp("^"+i+"$"),u=function(e){return e&&e.exact?a:new RegExp("(?:"+r(e)+t+r(e)+")|(?:"+r(e)+i+r(e)+")","g")};u.v4=function(e){return e&&e.exact?l:new RegExp(""+r(e)+t+r(e),"g")},u.v6=function(e){return e&&e.exact?s:new RegExp(""+r(e)+i+r(e),"g")};var o=u.v4().source,f=u.v6().source;return we=new RegExp("(?:^(?:(?:(?:[a-z]+:)?//)|www\\.)(?:\\S+(?::\\S*)?@)?(?:localhost|"+o+"|"+f+'|(?:(?:[a-z\\u00a1-\\uffff0-9][-_]*)*[a-z\\u00a1-\\uffff0-9]+)(?:\\.(?:[a-z\\u00a1-\\uffff0-9]-*)*[a-z\\u00a1-\\uffff0-9]+)*(?:\\.(?:[a-z\\u00a1-\\uffff]{2,})))(?::\\d{2,5})?(?:[/?#][^\\s"]*)?$)',"i")}())},hex:function(e){return"string"==typeof e&&!!e.match(Oe)}},je="enum",Ee={required:qe,whitespace:function(e,r,t,n,i){(/^\s+$/.test(r)||""===r)&&n.push(pe(i.messages.whitespace,e.fullField))},type:function(e,r,t,n,i){if(e.required&&void 0===r)qe(e,r,t,n,i);else{var a=e.type;["integer","float","array","regexp","object","method","email","number","date","url","hex"].indexOf(a)>-1?xe[a](r)||n.push(pe(i.messages.types[a],e.fullField,e.type)):a&&typeof r!==e.type&&n.push(pe(i.messages.types[a],e.fullField,e.type))}},range:function(e,r,t,n,i){var a="number"==typeof e.len,l="number"==typeof e.min,s="number"==typeof e.max,u=r,o=null,f="number"==typeof r,d="string"==typeof r,c=Array.isArray(r);if(f?o="number":d?o="string":c&&(o="array"),!o)return!1;c&&(u=r.length),d&&(u=r.replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"_").length),a?u!==e.len&&n.push(pe(i.messages[o].len,e.fullField,e.len)):l&&!s&&u<e.min?n.push(pe(i.messages[o].min,e.fullField,e.min)):s&&!l&&u>e.max?n.push(pe(i.messages[o].max,e.fullField,e.max)):l&&s&&(u<e.min||u>e.max)&&n.push(pe(i.messages[o].range,e.fullField,e.min,e.max))},enum:function(e,r,t,n,i){e[je]=Array.isArray(e[je])?e[je]:[],-1===e[je].indexOf(r)&&n.push(pe(i.messages[je],e.fullField,e[je].join(", ")))},pattern:function(e,r,t,n,i){if(e.pattern)if(e.pattern instanceof RegExp)e.pattern.lastIndex=0,e.pattern.test(r)||n.push(pe(i.messages.pattern.mismatch,e.fullField,r,e.pattern));else if("string"==typeof e.pattern){new RegExp(e.pattern).test(r)||n.push(pe(i.messages.pattern.mismatch,e.fullField,r,e.pattern))}}},Ae=function(e,r,t,n,i){var a=e.type,l=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r,a)&&!e.required)return t();Ee.required(e,r,n,l,i,a),ve(r,a)||Ee.type(e,r,n,l,i)}t(l)},Pe={string:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r,"string")&&!e.required)return t();Ee.required(e,r,n,a,i,"string"),ve(r,"string")||(Ee.type(e,r,n,a,i),Ee.range(e,r,n,a,i),Ee.pattern(e,r,n,a,i),!0===e.whitespace&&Ee.whitespace(e,r,n,a,i))}t(a)},method:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),void 0!==r&&Ee.type(e,r,n,a,i)}t(a)},number:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(""===r&&(r=void 0),ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),void 0!==r&&(Ee.type(e,r,n,a,i),Ee.range(e,r,n,a,i))}t(a)},boolean:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),void 0!==r&&Ee.type(e,r,n,a,i)}t(a)},regexp:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),ve(r)||Ee.type(e,r,n,a,i)}t(a)},integer:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),void 0!==r&&(Ee.type(e,r,n,a,i),Ee.range(e,r,n,a,i))}t(a)},float:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),void 0!==r&&(Ee.type(e,r,n,a,i),Ee.range(e,r,n,a,i))}t(a)},array:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(null==r&&!e.required)return t();Ee.required(e,r,n,a,i,"array"),null!=r&&(Ee.type(e,r,n,a,i),Ee.range(e,r,n,a,i))}t(a)},object:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),void 0!==r&&Ee.type(e,r,n,a,i)}t(a)},enum:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i),void 0!==r&&Ee.enum(e,r,n,a,i)}t(a)},pattern:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r,"string")&&!e.required)return t();Ee.required(e,r,n,a,i),ve(r,"string")||Ee.pattern(e,r,n,a,i)}t(a)},date:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r,"date")&&!e.required)return t();var l;if(Ee.required(e,r,n,a,i),!ve(r,"date"))l=r instanceof Date?r:new Date(r),Ee.type(e,l,n,a,i),l&&Ee.range(e,l.getTime(),n,a,i)}t(a)},url:Ae,hex:Ae,email:Ae,required:function(e,r,t,n,i){var a=[],l=Array.isArray(r)?"array":typeof r;Ee.required(e,r,n,a,i,l),t(a)},any:function(e,r,t,n,i){var a=[];if(e.required||!e.required&&n.hasOwnProperty(e.field)){if(ve(r)&&!e.required)return t();Ee.required(e,r,n,a,i)}t(a)}};function Se(){return{default:"Validation error on field %s",required:"%s is required",enum:"%s must be one of %s",whitespace:"%s cannot be empty",date:{format:"%s date %s is invalid for format %s",parse:"%s date could not be parsed, %s is invalid ",invalid:"%s date %s is invalid"},types:{string:"%s is not a %s",method:"%s is not a %s (function)",array:"%s is not an %s",object:"%s is not an %s",number:"%s is not a %s",date:"%s is not a %s",boolean:"%s is not a %s",integer:"%s is not an %s",float:"%s is not a %s",regexp:"%s is not a valid %s",email:"%s is not a valid %s",url:"%s is not a valid %s",hex:"%s is not a valid %s"},string:{len:"%s must be exactly %s characters",min:"%s must be at least %s characters",max:"%s cannot be longer than %s characters",range:"%s must be between %s and %s characters"},number:{len:"%s must equal %s",min:"%s cannot be less than %s",max:"%s cannot be greater than %s",range:"%s must be between %s and %s"},array:{len:"%s must be exactly %s in length",min:"%s cannot be less than %s in length",max:"%s cannot be greater than %s in length",range:"%s must be between %s and %s in length"},pattern:{mismatch:"%s value %s does not match pattern %s"},clone:function(){var e=JSON.parse(JSON.stringify(this));return e.clone=this.clone,e}}}var ke=Se(),Re=function(){function e(e){this.rules=null,this._messages=ke,this.define(e)}var r=e.prototype;return r.define=function(e){var r=this;if(!e)throw new Error("Cannot configure a schema with no rules");if("object"!=typeof e||Array.isArray(e))throw new Error("Rules must be an object");this.rules={},Object.keys(e).forEach(function(t){var n=e[t];r.rules[t]=Array.isArray(n)?n:[n]})},r.messages=function(e){return e&&(this._messages=be(Se(),e)),this._messages},r.validate=function(r,t,n){var i=this;void 0===t&&(t={}),void 0===n&&(n=function(){});var a=r,l=t,s=n;if("function"==typeof l&&(s=l,l={}),!this.rules||0===Object.keys(this.rules).length)return s&&s(null,a),Promise.resolve(a);if(l.messages){var u=this.messages();u===ke&&(u=Se()),be(u,l.messages),l.messages=u}else l.messages=this.messages();var o={};(l.keys||Object.keys(this.rules)).forEach(function(e){var t=i.rules[e],n=a[e];t.forEach(function(t){var l=t;"function"==typeof l.transform&&(a===r&&(a=le({},a)),n=a[e]=l.transform(n)),(l="function"==typeof l?{validator:l}:le({},l)).validator=i.getValidationMethod(l),l.validator&&(l.field=e,l.fullField=l.fullField||e,l.type=i.getType(l),o[e]=o[e]||[],o[e].push({rule:l,value:n,source:a,field:e}))})});var f={};return ye(o,l,function(r,t){var n,i=r.rule,s=!("object"!==i.type&&"array"!==i.type||"object"!=typeof i.fields&&"object"!=typeof i.defaultField);function u(e,r){return le({},r,{fullField:i.fullField+"."+e,fullFields:i.fullFields?[].concat(i.fullFields,[e]):[e]})}function o(n){void 0===n&&(n=[]);var o=Array.isArray(n)?n:[n];!l.suppressWarning&&o.length&&e.warning("async-validator:",o),o.length&&void 0!==i.message&&(o=[].concat(i.message));var d=o.map(me(i,a));if(l.first&&d.length)return f[i.field]=1,t(d);if(s){if(i.required&&!r.value)return void 0!==i.message?d=[].concat(i.message).map(me(i,a)):l.error&&(d=[l.error(i,pe(l.messages.required,i.field))]),t(d);var c={};i.defaultField&&Object.keys(r.value).map(function(e){c[e]=i.defaultField}),c=le({},c,r.rule.fields);var p={};Object.keys(c).forEach(function(e){var r=c[e],t=Array.isArray(r)?r:[r];p[e]=t.map(u.bind(null,e))});var v=new e(p);v.messages(l.messages),r.rule.options&&(r.rule.options.messages=l.messages,r.rule.options.error=l.error),v.validate(r.value,r.rule.options||l,function(e){var r=[];d&&d.length&&r.push.apply(r,d),e&&e.length&&r.push.apply(r,e),t(r.length?r:null)})}else t(d)}if(s=s&&(i.required||!i.required&&r.value),i.field=r.field,i.asyncValidator)n=i.asyncValidator(i,r.value,o,r.source,l);else if(i.validator){try{n=i.validator(i,r.value,o,r.source,l)}catch(d){console.error,l.suppressValidatorError||setTimeout(function(){throw d},0),o(d.message)}!0===n?o():!1===n?o("function"==typeof i.message?i.message(i.fullField||i.field):i.message||(i.fullField||i.field)+" fails"):n instanceof Array?o(n):n instanceof Error&&o(n.message)}n&&n.then&&n.then(function(){return o()},function(e){return o(e)})},function(e){!function(e){var r=[],t={};function n(e){var t;Array.isArray(e)?r=(t=r).concat.apply(t,e):r.push(e)}for(var i=0;i<e.length;i++)n(e[i]);r.length?(t=ce(r),s(r,t)):s(null,a)}(e)},a)},r.getType=function(e){if(void 0===e.type&&e.pattern instanceof RegExp&&(e.type="pattern"),"function"!=typeof e.validator&&e.type&&!Pe.hasOwnProperty(e.type))throw new Error(pe("Unknown rule type %s",e.type));return e.type||"string"},r.getValidationMethod=function(e){if("function"==typeof e.validator)return e.validator;var r=Object.keys(e),t=r.indexOf("message");return-1!==t&&r.splice(t,1),1===r.length&&"required"===r[0]?Pe.required:Pe[this.getType(e)]||void 0},e}();Re.register=function(e,r){if("function"!=typeof r)throw new Error("Cannot register a validator by type, validator is not a function");Pe[e]=r},Re.warning=function(){},Re.messages=ke,Re.validators=Pe;const $e=l({label:String,labelWidth:{type:[String,Number],default:""},labelPosition:{type:String,values:["left","right","top",""],default:""},prop:{type:s([String,Array])},required:{type:Boolean,default:void 0},rules:{type:s([Object,Array])},error:String,validateStatus:{type:String,values:["","error","validating","success"]},for:String,inlineMessage:{type:[String,Boolean],default:""},showMessage:{type:Boolean,default:!0},size:{type:String,values:u}}),_e="ElLabelWrap";var Ie=d({name:_e,props:{isAutoWidth:Boolean,updateAll:Boolean},setup(e,{slots:n}){const i=x(r,void 0),a=x(t);a||Q(_e,"usage: <el-form-item><label-wrap /></el-form-item>");const l=p("form"),s=o(),u=o(0),d=(r="update")=>{R(()=>{n.default&&e.isAutoWidth&&("update"===r?u.value=(()=>{var e;if(null==(e=s.value)?void 0:e.firstElementChild){const e=window.getComputedStyle(s.value.firstElementChild).width;return Math.ceil(Number.parseFloat(e))}return 0})():"remove"===r&&(null==i||i.deregisterLabelWidth(u.value)))})},c=()=>d("update");return j(()=>{c()}),E(()=>{d("remove")}),A(()=>c()),v(u,(r,t)=>{e.updateAll&&(null==i||i.registerLabelWidth(r,t))}),P(f(()=>{var e,r;return null!=(r=null==(e=s.value)?void 0:e.firstElementChild)?r:null}),c),()=>{var r,t;if(!n)return null;const{isAutoWidth:o}=e;if(o){const e=null==i?void 0:i.autoLabelWidth,t={};if((null==a?void 0:a.hasLabel)&&e&&"auto"!==e){const r=Math.max(0,Number.parseInt(e,10)-u.value),n=a.labelPosition||i.labelPosition;r&&(t["left"===n?"marginRight":"marginLeft"]=`${r}px`)}return S("div",{ref:s,class:[l.be("item","label-wrap")],style:t},[null==(r=n.default)?void 0:r.call(n)])}return S(k,{ref:s},[null==(t=n.default)?void 0:t.call(n)])}}});const Ve=d({name:"ElFormItem"});var We=O(d({...Ve,props:$e,setup(i,{expose:l}){const s=i,u=$(),d=x(r,void 0),O=x(t,void 0),A=e(void 0,{formItem:!1}),P=p("form-item"),k=U().value,J=o([]),Z=o(""),Y=_(Z,100),Q=o(""),H=o();let X,ee=!1;const re=f(()=>s.labelPosition||(null==d?void 0:d.labelPosition)),te=f(()=>{if("top"===re.value)return{};const e=I(s.labelWidth||(null==d?void 0:d.labelWidth)||"");return e?{width:e}:{}}),ne=f(()=>{if("top"===re.value||(null==d?void 0:d.inline))return{};if(!s.label&&!s.labelWidth&&de)return{};const e=I(s.labelWidth||(null==d?void 0:d.labelWidth)||"");return s.label||u.label?{}:{marginLeft:e}}),ie=f(()=>[P.b(),P.m(A.value),P.is("error","error"===Z.value),P.is("validating","validating"===Z.value),P.is("success","success"===Z.value),P.is("required",ge.value||s.required),P.is("no-asterisk",null==d?void 0:d.hideRequiredAsterisk),"right"===(null==d?void 0:d.requireAsteriskPosition)?"asterisk-right":"asterisk-left",{[P.m("feedback")]:null==d?void 0:d.statusIcon,[P.m(`label-${re.value}`)]:re.value}]),ae=f(()=>a(s.inlineMessage)?s.inlineMessage:(null==d?void 0:d.inlineMessage)||!1),le=f(()=>[P.e("error"),{[P.em("error","inline")]:ae.value}]),se=f(()=>s.prop?n(s.prop)?s.prop.join("."):s.prop:""),ue=f(()=>!(!s.label&&!u.label)),oe=f(()=>{var e;return null!=(e=s.for)?e:1===J.value.length?J.value[0]:void 0}),fe=f(()=>!oe.value&&ue.value),de=!!O,ce=f(()=>{const e=null==d?void 0:d.model;if(e&&s.prop)return V(e,s.prop).value}),pe=f(()=>{const{required:e}=s,r=[];s.rules&&r.push(...G(s.rules));const t=null==d?void 0:d.rules;if(t&&s.prop){const e=V(t,s.prop).value;e&&r.push(...G(e))}if(void 0!==e){const t=r.map((e,r)=>[e,r]).filter(([e])=>Object.keys(e).includes("required"));if(t.length>0)for(const[n,i]of t)n.required!==e&&(r[i]={...n,required:e});else r.push({required:e})}return r}),ve=f(()=>pe.value.length>0),ge=f(()=>pe.value.some(e=>e.required)),he=f(()=>{var e;return"error"===Y.value&&s.showMessage&&(null==(e=null==d?void 0:d.showMessage)||e)}),ye=f(()=>`${s.label||""}${(null==d?void 0:d.labelSuffix)||""}`),me=e=>{Z.value=e},be=async e=>{const r=se.value;return new Re({[r]:e}).validate({[r]:ce.value},{firstFields:!0}).then(()=>(me("success"),null==d||d.emit("validate",s.prop,!0,""),!0)).catch(e=>((e=>{var r,t;const{errors:n,fields:i}=e;me("error"),Q.value=n?null!=(t=null==(r=null==n?void 0:n[0])?void 0:r.message)?t:`${s.prop} is required`:"",null==d||d.emit("validate",s.prop,!1,Q.value)})(e),Promise.reject(e)))},we=async(e,r)=>{if(ee||!s.prop)return!1;const t=F(r);if(!ve.value)return null==r||r(!1),!1;const i=(e=>pe.value.filter(r=>!r.trigger||!e||(n(r.trigger)?r.trigger.includes(e):r.trigger===e)).map(({trigger:e,...r})=>r))(e);return 0===i.length?(null==r||r(!0),!0):(me("validating"),be(i).then(()=>(null==r||r(!0),!0)).catch(e=>{const{fields:n}=e;return null==r||r(!1,n),!t&&Promise.reject(n)}))},qe=()=>{me(""),Q.value="",ee=!1},Fe=async()=>{const e=null==d?void 0:d.model;if(!e||!s.prop)return;const r=V(e,s.prop);ee=!0,r.value=K(X),await R(),qe(),ee=!1};v(()=>s.error,e=>{Q.value=e||"",me(e?"error":"")},{immediate:!0}),v(()=>s.validateStatus,e=>me(e||""));const Oe=c({...h(s),$el:H,size:A,validateMessage:Q,validateState:Z,labelId:k,inputIds:J,isGroup:fe,hasLabel:ue,fieldValue:ce,addInputId:e=>{J.value.includes(e)||J.value.push(e)},removeInputId:e=>{J.value=J.value.filter(r=>r!==e)},resetField:Fe,clearValidate:qe,validate:we,propString:se});return g(t,Oe),j(()=>{s.prop&&(null==d||d.addField(Oe),X=K(ce.value))}),E(()=>{null==d||d.removeField(Oe)}),l({size:A,validateMessage:Q,validateState:Z,validate:we,clearValidate:qe,resetField:Fe}),(e,r)=>{var t;return m(),y("div",{ref_key:"formItemRef",ref:H,class:w(q(ie)),role:q(fe)?"group":void 0,"aria-labelledby":q(fe)?q(k):void 0},[S(q(Ie),{"is-auto-width":"auto"===q(te).width,"update-all":"auto"===(null==(t=q(d))?void 0:t.labelWidth)},{default:M(()=>[q(ue)?(m(),B(C(q(oe)?"label":"div"),{key:0,id:q(k),for:q(oe),class:w(q(P).e("label")),style:D(q(te))},{default:M(()=>[b(e.$slots,"label",{label:q(ye)},()=>[L(N(q(ye)),1)])]),_:3},8,["id","for","class","style"])):z("v-if",!0)]),_:3},8,["is-auto-width","update-all"]),W("div",{class:w(q(P).e("content")),style:D(q(ne))},[b(e.$slots,"default"),S(T,{name:`${q(P).namespace.value}-zoom-in-top`},{default:M(()=>[q(he)?b(e.$slots,"error",{key:0,error:Q.value},()=>[W("div",{class:w(q(le))},N(Q.value),3)]):z("v-if",!0)]),_:3},8,["name"])],6)],10,["role","aria-labelledby"])}}}),[["__file","form-item.vue"]]);const Me=J(ae,{FormItem:We}),Be=Z(We);export{Me as E,Be as a};
