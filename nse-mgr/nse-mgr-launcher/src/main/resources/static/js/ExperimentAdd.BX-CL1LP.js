import{d as e,r as t,aD as i,V as r,I as a,e as o,f as s,w as p,m,h as l,C as n,a1 as d}from"./index.Ckm1SagX.js";import{E as u}from"./dialog.TtqHlFhB.js";import"./overlay.CXfNA60T.js";/* empty css               */import{E as j,a as x}from"./form-item.CUMILu98.js";/* empty css              *//* empty css            */import{E as v,a as c}from"./select.DHkh6uhw.js";import"./scrollbar.6rbryiG1.js";import"./popper.DpZVcW1M.js";import{E as f}from"./index.4JfkAhur.js";import{E as _}from"./index.CbYeWxT8.js";import{_ as y}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BjYFza3j.js";import"./vnode.BkZiIFpS.js";import"./aria.C1IWO_Rd.js";import"./scroll.XdyICIdv.js";import"./focus-trap.Bd_uzvDY.js";import"./index.C0OsJ5su.js";import"./refs.biN0GvkM.js";import"./index.BRUQ9gWw.js";import"./event.BwRzfsZt.js";import"./index.Dh_vcBr5.js";import"./use-form-common-props.BSYTvb6G.js";import"./castArray.Chmjnshw.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./index.BPj3iklg.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./isEqual.CZKKciWh.js";import"./index.Byj-i824.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.BLy3nyPI.js";import"./index.B0geSHq7.js";import"./index.Cn1QDWeG.js";import"./index.DJHzyRe5.js";const b={class:"dialog-footer"},g=y(e({__name:"ExperimentAdd",props:{visible:{type:Boolean},title:{},initialData:{}},emits:["update:visible","submit","close"],setup(e,{emit:y}){const g=e,h=y,T=t(),V=i(),E=t(g.visible),C=t(g.title),N=r({id:"",experimentName:"",experimentType:""}),k={experimentName:[{required:!0,message:"请输入实验名称",trigger:"blur",validator:(e,t,i)=>{"空白实验"!==N.experimentType||t?i():i(new Error("请输入实验名称"))}}],experimentType:[{required:!0,message:"请选择实验类型",trigger:"change"}]};a(()=>g.visible,e=>{E.value=e}),a(()=>E.value,e=>{e||h("update:visible",!1)}),a(()=>g.title,e=>{C.value=e}),a(()=>g.initialData,e=>{e&&Object.assign(N,e)},{immediate:!0,deep:!0}),a(()=>N.experimentType,e=>{"课程库实验"===e&&(N.experimentName="")});const w=async()=>{var e;try{if("课程库实验"===N.experimentType)return E.value=!1,U(),void V.push({path:"/course/index",query:{from:"experiment"}});await(null==(e=T.value)?void 0:e.validate()),h("submit",{...N})}catch(t){}},q=()=>{E.value=!1,U(),h("close")},U=()=>{var e;null==(e=T.value)||e.resetFields(),Object.assign(N,{id:void 0,experimentName:"",experimentType:"",description:"",content:""})};return(e,t)=>{const i=c,r=v,a=x,y=f,g=j,h=_,V=u;return s(),o(V,{modelValue:E.value,"onUpdate:modelValue":t[2]||(t[2]=e=>E.value=e),title:C.value,width:"30%",onClose:q},{footer:p(()=>[n("div",b,[m(h,{onClick:q},{default:p(()=>t[3]||(t[3]=[d("取消")])),_:1,__:[3]}),m(h,{type:"primary",onClick:w},{default:p(()=>t[4]||(t[4]=[d("确定")])),_:1,__:[4]})])]),default:p(()=>[m(g,{ref_key:"formRef",ref:T,model:N,rules:k,"label-width":"100px","label-position":"top"},{default:p(()=>[m(a,{label:"实验类型",prop:"experimentType"},{default:p(()=>[m(r,{modelValue:N.experimentType,"onUpdate:modelValue":t[0]||(t[0]=e=>N.experimentType=e),placeholder:"请选择实验类型"},{default:p(()=>[m(i,{label:"空白实验",value:"空白实验"}),m(i,{label:"课程库实验",value:"课程库实验"})]),_:1},8,["modelValue"])]),_:1}),"课程库实验"!==N.experimentType?(s(),o(a,{key:0,label:"实验名称",prop:"experimentName"},{default:p(()=>[m(y,{modelValue:N.experimentName,"onUpdate:modelValue":t[1]||(t[1]=e=>N.experimentName=e),placeholder:"请输入实验名称"},null,8,["modelValue"])]),_:1})):l("",!0)]),_:1},8,["model"])]),_:1},8,["modelValue","title"])}}}),[["__scopeId","data-v-1a25de79"]]);export{g as default};
