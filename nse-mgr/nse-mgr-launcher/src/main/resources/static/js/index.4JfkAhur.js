import{H as e,aV as t,L as a,t as o,z as n,af as l,v as s,O as i,_ as r,d as u,bb as p,b8 as d,c,b as f,s as m,r as v,bj as x,bY as h,bZ as g,M as y,ad as b,I as w,o as S,a2 as C,ah as z,g as k,f as I,h as E,Q as P,C as j,n as F,i as B,l as $,e as M,w as N,D as V,E as _,W as R,m as H,be as L,j as O,b7 as T,F as W,k as A,ac as Y,q}from"./index.Ckm1SagX.js";import{u as K}from"./index.BLy3nyPI.js";import{U,I as D,C as Q}from"./event.BwRzfsZt.js";import{u as Z}from"./index.DJHzyRe5.js";import{a as G,b as J,u as X,c as ee}from"./use-form-common-props.BSYTvb6G.js";import{u as te,a as ae}from"./index.Byj-i824.js";import{d as oe}from"./aria.C1IWO_Rd.js";let ne;const le={height:"0",visibility:"hidden",overflow:e&&/firefox/i.test(window.navigator.userAgent)?"":"hidden",position:"absolute","z-index":"-1000",top:"0",right:"0"},se=["letter-spacing","line-height","padding-top","padding-bottom","font-family","font-weight","font-size","text-rendering","text-transform","width","text-indent","padding-left","padding-right","border-width","box-sizing"];function ie(e,a=1,o){var n;ne||(ne=document.createElement("textarea"),document.body.appendChild(ne));const{paddingSize:l,borderSize:s,boxSizing:i,contextStyle:r}=function(e){const t=window.getComputedStyle(e),a=t.getPropertyValue("box-sizing"),o=Number.parseFloat(t.getPropertyValue("padding-bottom"))+Number.parseFloat(t.getPropertyValue("padding-top")),n=Number.parseFloat(t.getPropertyValue("border-bottom-width"))+Number.parseFloat(t.getPropertyValue("border-top-width"));return{contextStyle:se.map(e=>[e,t.getPropertyValue(e)]),paddingSize:o,borderSize:n,boxSizing:a}}(e);r.forEach(([e,t])=>null==ne?void 0:ne.style.setProperty(e,t)),Object.entries(le).forEach(([e,t])=>null==ne?void 0:ne.style.setProperty(e,t,"important")),ne.value=e.value||e.placeholder||"";let u=ne.scrollHeight;const p={};"border-box"===i?u+=s:"content-box"===i&&(u-=l),ne.value="";const d=ne.scrollHeight-l;if(t(a)){let e=d*a;"border-box"===i&&(e=e+l+s),u=Math.max(e,u),p.minHeight=`${e}px`}if(t(o)){let e=d*o;"border-box"===i&&(e=e+l+s),u=Math.min(e,u)}return p.height=`${u}px`,null==(n=ne.parentNode)||n.removeChild(ne),ne=void 0,p}const re=o({id:{type:String,default:void 0},size:i,disabled:Boolean,modelValue:{type:n([String,Number,Object]),default:""},maxlength:{type:[String,Number]},minlength:{type:[String,Number]},type:{type:String,default:"text"},resize:{type:String,values:["none","both","horizontal","vertical"]},autosize:{type:n([Boolean,Object]),default:!1},autocomplete:{type:String,default:"off"},formatter:{type:Function},parser:{type:Function},placeholder:{type:String},form:{type:String},readonly:Boolean,clearable:Boolean,showPassword:Boolean,showWordLimit:Boolean,suffixIcon:{type:s},prefixIcon:{type:s},containerRole:{type:String,default:void 0},tabindex:{type:[String,Number],default:0},validateEvent:{type:Boolean,default:!0},inputStyle:{type:n([Object,Array,String]),default:()=>l({})},autofocus:Boolean,rows:{type:Number,default:2},...K(["ariaLabel"]),inputmode:{type:n(String),default:void 0},name:String}),ue={[U]:e=>a(e),input:e=>a(e),change:e=>a(e),focus:e=>e instanceof FocusEvent,blur:e=>e instanceof FocusEvent,clear:()=>!0,mouseleave:e=>e instanceof MouseEvent,mouseenter:e=>e instanceof MouseEvent,keydown:e=>e instanceof Event,compositionstart:e=>e instanceof CompositionEvent,compositionupdate:e=>e instanceof CompositionEvent,compositionend:e=>e instanceof CompositionEvent};const pe=u({name:"ElInput",inheritAttrs:!1});const de=q(r(u({...pe,props:re,emits:ue,setup(t,{expose:a,emit:o}){const n=t,l=p(),s=Z(),i=d(),r=c(()=>["textarea"===n.type?ue.b():re.b(),re.m(le.value),re.is("disabled",se.value),re.is("exceed",$e.value),{[re.b("group")]:i.prepend||i.append,[re.m("prefix")]:i.prefix||n.prefixIcon,[re.m("suffix")]:i.suffix||n.suffixIcon||n.clearable||n.showPassword,[re.bm("suffix","password-clear")]:Pe.value&&je.value,[re.b("hidden")]:"hidden"===n.type},l.class]),u=c(()=>[re.e("wrapper"),re.is("focus",ge.value)]),{form:q,formItem:K}=X(),{inputId:ne}=ee(n,{formItemContext:K}),le=G(),se=J(),re=f("input"),ue=f("textarea"),pe=m(),de=m(),ce=v(!1),fe=v(!1),me=v(),ve=m(n.inputStyle),xe=c(()=>pe.value||de.value),{wrapperRef:he,isFocused:ge,handleFocus:ye,handleBlur:be}=te(xe,{disabled:se,afterBlur(){var e;n.validateEvent&&(null==(e=null==K?void 0:K.validate)||e.call(K,"blur").catch(e=>oe()))}}),we=c(()=>{var e;return null!=(e=null==q?void 0:q.statusIcon)&&e}),Se=c(()=>(null==K?void 0:K.validateState)||""),Ce=c(()=>Se.value&&x[Se.value]),ze=c(()=>fe.value?h:g),ke=c(()=>[l.style]),Ie=c(()=>[n.inputStyle,ve.value,{resize:n.resize}]),Ee=c(()=>y(n.modelValue)?"":String(n.modelValue)),Pe=c(()=>n.clearable&&!se.value&&!n.readonly&&!!Ee.value&&(ge.value||ce.value)),je=c(()=>n.showPassword&&!se.value&&!!Ee.value),Fe=c(()=>n.showWordLimit&&!!n.maxlength&&("text"===n.type||"textarea"===n.type)&&!se.value&&!n.readonly&&!n.showPassword),Be=c(()=>Ee.value.length),$e=c(()=>!!Fe.value&&Be.value>Number(n.maxlength)),Me=c(()=>!!i.suffix||!!n.suffixIcon||Pe.value||n.showPassword||Fe.value||!!Se.value&&we.value),[Ne,Ve]=function(e){let t;return[function(){if(null==e.value)return;const{selectionStart:a,selectionEnd:o,value:n}=e.value;if(null==a||null==o)return;const l=n.slice(0,Math.max(0,a)),s=n.slice(Math.max(0,o));t={selectionStart:a,selectionEnd:o,value:n,beforeTxt:l,afterTxt:s}},function(){if(null==e.value||null==t)return;const{value:a}=e.value,{beforeTxt:o,afterTxt:n,selectionStart:l}=t;if(null==o||null==n||null==l)return;let s=a.length;if(a.endsWith(n))s=a.length-n.length;else if(a.startsWith(o))s=o.length;else{const e=o[l-1],t=a.indexOf(e,l-1);-1!==t&&(s=t+1)}e.value.setSelectionRange(s,s)}]}(pe);b(de,e=>{if(Re(),!Fe.value||"both"!==n.resize)return;const t=e[0],{width:a}=t.contentRect;me.value={right:`calc(100% - ${a+15+6}px)`}});const _e=()=>{const{type:t,autosize:a}=n;if(e&&"textarea"===t&&de.value)if(a){const e=Y(a)?a.minRows:void 0,t=Y(a)?a.maxRows:void 0,o=ie(de.value,e,t);ve.value={overflowY:"hidden",...o},C(()=>{de.value.offsetHeight,ve.value=o})}else ve.value={minHeight:ie(de.value).minHeight}},Re=(e=>{let t=!1;return()=>{var a;if(t||!n.autosize)return;null===(null==(a=de.value)?void 0:a.offsetParent)||(e(),t=!0)}})(_e),He=()=>{const e=xe.value,t=n.formatter?n.formatter(Ee.value):Ee.value;e&&e.value!==t&&(e.value=t)},Le=async e=>{Ne();let{value:t}=e.target;n.formatter&&n.parser&&(t=n.parser(t)),Te.value||(t!==Ee.value?(o(U,t),o(D,t),await C(),He(),Ve()):He())},Oe=e=>{let{value:t}=e.target;n.formatter&&n.parser&&(t=n.parser(t)),o(Q,t)},{isComposing:Te,handleCompositionStart:We,handleCompositionUpdate:Ae,handleCompositionEnd:Ye}=ae({emit:o,afterComposition:Le}),qe=()=>{Ne(),fe.value=!fe.value,setTimeout(Ve)},Ke=e=>{ce.value=!1,o("mouseleave",e)},Ue=e=>{ce.value=!0,o("mouseenter",e)},De=e=>{o("keydown",e)},Qe=()=>{o(U,""),o(Q,""),o("clear"),o(D,"")};return w(()=>n.modelValue,()=>{var e;C(()=>_e()),n.validateEvent&&(null==(e=null==K?void 0:K.validate)||e.call(K,"change").catch(e=>oe()))}),w(Ee,()=>He()),w(()=>n.type,async()=>{await C(),He(),_e()}),S(()=>{!n.formatter&&n.parser,He(),C(_e)}),a({input:pe,textarea:de,ref:xe,textareaStyle:Ie,autosize:z(n,"autosize"),isComposing:Te,focus:()=>{var e;return null==(e=xe.value)?void 0:e.focus()},blur:()=>{var e;return null==(e=xe.value)?void 0:e.blur()},select:()=>{var e;null==(e=xe.value)||e.select()},clear:Qe,resizeTextarea:_e}),(e,t)=>(I(),k("div",{class:F([B(r),{[B(re).bm("group","append")]:e.$slots.append,[B(re).bm("group","prepend")]:e.$slots.prepend}]),style:A(B(ke)),onMouseenter:Ue,onMouseleave:Ke},[E(" input "),"textarea"!==e.type?(I(),k(P,{key:0},[E(" prepend slot "),e.$slots.prepend?(I(),k("div",{key:0,class:F(B(re).be("group","prepend"))},[$(e.$slots,"prepend")],2)):E("v-if",!0),j("div",{ref_key:"wrapperRef",ref:he,class:F(B(u))},[E(" prefix slot "),e.$slots.prefix||e.prefixIcon?(I(),k("span",{key:0,class:F(B(re).e("prefix"))},[j("span",{class:F(B(re).e("prefix-inner"))},[$(e.$slots,"prefix"),e.prefixIcon?(I(),M(B(_),{key:0,class:F(B(re).e("icon"))},{default:N(()=>[(I(),M(V(e.prefixIcon)))]),_:1},8,["class"])):E("v-if",!0)],2)],2)):E("v-if",!0),j("input",R({id:B(ne),ref_key:"input",ref:pe,class:B(re).e("inner")},B(s),{name:e.name,minlength:e.minlength,maxlength:e.maxlength,type:e.showPassword?fe.value?"text":"password":e.type,disabled:B(se),readonly:e.readonly,autocomplete:e.autocomplete,tabindex:e.tabindex,"aria-label":e.ariaLabel,placeholder:e.placeholder,style:e.inputStyle,form:e.form,autofocus:e.autofocus,role:e.containerRole,inputmode:e.inputmode,onCompositionstart:B(We),onCompositionupdate:B(Ae),onCompositionend:B(Ye),onInput:Le,onChange:Oe,onKeydown:De}),null,16,["id","name","minlength","maxlength","type","disabled","readonly","autocomplete","tabindex","aria-label","placeholder","form","autofocus","role","inputmode","onCompositionstart","onCompositionupdate","onCompositionend"]),E(" suffix slot "),B(Me)?(I(),k("span",{key:1,class:F(B(re).e("suffix"))},[j("span",{class:F(B(re).e("suffix-inner"))},[B(Pe)&&B(je)&&B(Fe)?E("v-if",!0):(I(),k(P,{key:0},[$(e.$slots,"suffix"),e.suffixIcon?(I(),M(B(_),{key:0,class:F(B(re).e("icon"))},{default:N(()=>[(I(),M(V(e.suffixIcon)))]),_:1},8,["class"])):E("v-if",!0)],64)),B(Pe)?(I(),M(B(_),{key:1,class:F([B(re).e("icon"),B(re).e("clear")]),onMousedown:O(B(T),["prevent"]),onClick:Qe},{default:N(()=>[H(B(L))]),_:1},8,["class","onMousedown"])):E("v-if",!0),B(je)?(I(),M(B(_),{key:2,class:F([B(re).e("icon"),B(re).e("password")]),onClick:qe},{default:N(()=>[(I(),M(V(B(ze))))]),_:1},8,["class"])):E("v-if",!0),B(Fe)?(I(),k("span",{key:3,class:F(B(re).e("count"))},[j("span",{class:F(B(re).e("count-inner"))},W(B(Be))+" / "+W(e.maxlength),3)],2)):E("v-if",!0),B(Se)&&B(Ce)&&B(we)?(I(),M(B(_),{key:4,class:F([B(re).e("icon"),B(re).e("validateIcon"),B(re).is("loading","validating"===B(Se))])},{default:N(()=>[(I(),M(V(B(Ce))))]),_:1},8,["class"])):E("v-if",!0)],2)],2)):E("v-if",!0)],2),E(" append slot "),e.$slots.append?(I(),k("div",{key:1,class:F(B(re).be("group","append"))},[$(e.$slots,"append")],2)):E("v-if",!0)],64)):(I(),k(P,{key:1},[E(" textarea "),j("textarea",R({id:B(ne),ref_key:"textarea",ref:de,class:[B(ue).e("inner"),B(re).is("focus",B(ge))]},B(s),{minlength:e.minlength,maxlength:e.maxlength,tabindex:e.tabindex,disabled:B(se),readonly:e.readonly,autocomplete:e.autocomplete,style:B(Ie),"aria-label":e.ariaLabel,placeholder:e.placeholder,form:e.form,autofocus:e.autofocus,rows:e.rows,role:e.containerRole,onCompositionstart:B(We),onCompositionupdate:B(Ae),onCompositionend:B(Ye),onInput:Le,onFocus:B(ye),onBlur:B(be),onChange:Oe,onKeydown:De}),null,16,["id","minlength","maxlength","tabindex","disabled","readonly","autocomplete","aria-label","placeholder","form","autofocus","rows","role","onCompositionstart","onCompositionupdate","onCompositionend","onFocus","onBlur"]),B(Fe)?(I(),k("span",{key:0,style:A(me.value),class:F(B(re).e("count"))},W(B(Be))+" / "+W(e.maxlength),7)):E("v-if",!0)],64))],38))}}),[["__file","input.vue"]]));export{de as E};
