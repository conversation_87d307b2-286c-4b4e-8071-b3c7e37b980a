import{d as t,r as e,o,g as n,f as a,C as s,h as r,m as i,w as l,a1 as c,j as u,i as p,aP as d}from"./index.Ckm1SagX.js";/* empty css               */import{F as h}from"./file.api.td1NUiAp.js";import{E as f}from"./index.CbYeWxT8.js";import{_ as m}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./index.BRUQ9gWw.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";const v={class:"canvas-dom"},g=["src"],_=m(t({__name:"signature",setup(t){const m=e(""),_=e();let w,y=!1;const j=t=>{let e;if(t.offsetX){const{offsetX:o,offsetY:n}=t;e=[o,n]}else{const{top:o,left:n}=_.value.getBoundingClientRect();e=[t.touches[0].clientX-n,t.touches[0].clientY-o]}return e};let k=0,C=0;const x=t=>{[k,C]=j(t),y=!0},R=t=>{if(y){const[e,o]=j(t);!function(t,e,o,n,a){a.beginPath(),a.globalAlpha=1,a.lineWidth=2,a.strokeStyle="#000",a.moveTo(t,e),a.lineTo(o,n),a.closePath(),a.stroke()}(k,C,e,o,w),k=e,C=o}},E=()=>{y&&(y=!1)};o(()=>{w=_.value.getContext("2d")});const T=async()=>{if(b(_.value))return void d({type:"warning",message:"当前签名文件为空"});const t=L(_.value.toDataURL(),"签名.png");if(!t)return;const e=await h.uploadFile(t);U(),m.value=e.url},U=()=>{w.clearRect(0,0,_.value.width,_.value.height)},b=t=>{const e=document.createElement("canvas");return e.width=t.width,e.height=t.height,t.toDataURL()==e.toDataURL()},D=()=>{if(b(_.value))return void d({type:"warning",message:"当前签名文件为空"});const t=document.createElement("a");t.href=_.value.toDataURL(),t.download="签名";const e=new MouseEvent("click");t.dispatchEvent(e)},L=(t,e)=>{const o=t.split(",");if(!o.length)return;const n=o[0].match(/:(.*?);/);if(n){const t=atob(o[1]);let a=t.length;const s=new Uint8Array(a);for(;a--;)s[a]=t.charCodeAt(a);return new File([s],e,{type:n[1]})}};return(t,e)=>{const o=f;return a(),n("div",v,[e[3]||(e[3]=s("h3",null,"基于canvas实现的签名组件",-1)),s("header",null,[i(o,{type:"primary",onClick:D},{default:l(()=>e[0]||(e[0]=[c("保存为图片")])),_:1,__:[0]}),i(o,{onClick:T},{default:l(()=>e[1]||(e[1]=[c("保存到后端")])),_:1,__:[1]}),i(o,{onClick:U},{default:l(()=>e[2]||(e[2]=[c("清空签名")])),_:1,__:[2]})]),s("canvas",{ref_key:"canvas",ref:_,height:"200",width:"500",onMousedown:x,onMousemove:u(R,["stop","prevent"]),onMouseup:E,onTouchstart:x,onTouchmove:u(R,["stop","prevent"]),onTouchend:E},null,544),p(m)?(a(),n("img",{key:0,src:p(m),alt:"签名"},null,8,g)):r("",!0)])}}}),[["__scopeId","data-v-59494ca2"]]);export{_ as default};
