import{d as e,r as a,g as t,f as s,m as l,C as o,w as i,a1 as n,aC as r,i as p,X as m,bf as u}from"./index.Ckm1SagX.js";/* empty css             */import{_ as d}from"./index.vue_vue_type_script_setup_true_lang.UhywIJIZ.js";import{E as _}from"./index.CMOQuMWt.js";import"./file.api.td1NUiAp.js";import"./index.BRUQ9gWw.js";const g={class:"app-container"},x={style:{"margin-top":"10px"}},c=e({__name:"wang-editor",setup(e){const c=a("初始化内容");return(e,a)=>{const f=_;return s(),t("div",g,[l(f,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/wang-editor.vue",type:"primary",target:"_blank",class:"mb-[20px]"},{default:i(()=>a[2]||(a[2]=[n(" 示例源码 请点击>>>> ")])),_:1,__:[2]}),l(d,{modelValue:p(c),"onUpdate:modelValue":a[0]||(a[0]=e=>r(c)?c.value=e:null),height:"400px"},null,8,["modelValue"]),o("div",x,[m(o("textarea",{"onUpdate:modelValue":a[1]||(a[1]=e=>r(c)?c.value=e:null),readonly:"",style:{width:"100%",height:"200px",outline:"none"}},null,512),[[u,p(c)]])])])}}});export{c as default};
