import{aV as e,M as a,t,z as l,O as n,_ as r,d as u,x as i,b as s,r as o,V as m,c as d,a7 as c,I as p,o as v,by as b,g as f,f as N,X as y,h as x,m as V,i as g,a0 as h,n as E,l as I,w as S,e as A,Y as _,cR as w,E as F,b0 as M,cS as $,bd as j,j as k,L as B,q as K}from"./index.Ckm1SagX.js";import{E as O}from"./index.4JfkAhur.js";import{u as R}from"./index.BLy3nyPI.js";import{U as z,I as C,C as G}from"./event.BwRzfsZt.js";import{v as L}from"./index.COAWgEf6.js";import{u as T,a as D,b as X}from"./use-form-common-props.BSYTvb6G.js";import{d as q,t as P}from"./aria.C1IWO_Rd.js";const Y=t({id:{type:String,default:void 0},step:{type:Number,default:1},stepStrictly:Boolean,max:{type:Number,default:Number.MAX_SAFE_INTEGER},min:{type:Number,default:Number.MIN_SAFE_INTEGER},modelValue:{type:[Number,null]},readonly:Boolean,disabled:Boolean,size:n,controls:{type:Boolean,default:!0},controlsPosition:{type:String,default:"",values:["","right"]},valueOnClear:{type:[String,Number,null],validator:a=>null===a||e(a)||["min","max"].includes(a),default:null},name:String,placeholder:String,precision:{type:Number,validator:e=>e>=0&&e===Number.parseInt(`${e}`,10)},validateEvent:{type:Boolean,default:!0},...R(["ariaLabel"]),inputmode:{type:l(String),default:void 0}}),U={[G]:(e,a)=>a!==e,blur:e=>e instanceof FocusEvent,focus:e=>e instanceof FocusEvent,[C]:t=>e(t)||a(t),[z]:t=>e(t)||a(t)},H=u({name:"ElInputNumber"});const J=K(r(u({...H,props:Y,emits:U,setup(t,{expose:l,emit:n}){const r=t,{t:u}=i(),K=s("input-number"),R=o(),Y=m({currentValue:r.modelValue,userInput:null}),{formItem:U}=T(),H=d(()=>e(r.modelValue)&&r.modelValue<=r.min),J=d(()=>e(r.modelValue)&&r.modelValue>=r.max),Q=d(()=>{const e=le(r.step);return c(r.precision)?Math.max(le(r.modelValue),e):(r.precision,r.precision)}),W=d(()=>r.controls&&"right"===r.controlsPosition),Z=D(),ee=X(),ae=d(()=>{if(null!==Y.userInput)return Y.userInput;let t=Y.currentValue;if(a(t))return"";if(e(t)){if(Number.isNaN(t))return"";c(r.precision)||(t=t.toFixed(r.precision))}return t}),te=(e,a)=>{if(c(a)&&(a=Q.value),0===a)return Math.round(e);let t=String(e);const l=t.indexOf(".");if(-1===l)return e;if(!t.replace(".","").split("")[l+a])return e;const n=t.length;return"5"===t.charAt(n-1)&&(t=`${t.slice(0,Math.max(0,n-1))}6`),Number.parseFloat(Number(t).toFixed(a))},le=e=>{if(a(e))return 0;const t=e.toString(),l=t.indexOf(".");let n=0;return-1!==l&&(n=t.length-l-1),n},ne=(a,t=1)=>e(a)?a>=Number.MAX_SAFE_INTEGER&&1===t||a<=Number.MIN_SAFE_INTEGER&&-1===t?a:te(a+r.step*t):Y.currentValue,re=()=>{if(r.readonly||ee.value||J.value)return;const e=Number(ae.value)||0,a=ne(e);se(a),n(C,Y.currentValue),pe()},ue=()=>{if(r.readonly||ee.value||H.value)return;const e=Number(ae.value)||0,a=ne(e,-1);se(a),n(C,Y.currentValue),pe()},ie=(e,t)=>{const{max:l,min:u,step:i,precision:s,stepStrictly:o,valueOnClear:m}=r;l<u&&P("InputNumber","min should not be greater than max.");let d=Number(e);if(a(e)||Number.isNaN(d))return null;if(""===e){if(null===m)return null;d=B(m)?{min:u,max:l}[m]:m}return o&&(d=te(Math.round(d/i)*i,s),d!==e&&t&&n(z,d)),c(s)||(d=te(d,s)),(d>l||d<u)&&(d=d>l?l:u,t&&n(z,d)),d},se=(e,a=!0)=>{var t;const l=Y.currentValue,u=ie(e);a?l===u&&e||(Y.userInput=null,n(z,u),l!==u&&n(G,u,l),r.validateEvent&&(null==(t=null==U?void 0:U.validate)||t.call(U,"change").catch(e=>q())),Y.currentValue=u):n(z,u)},oe=e=>{Y.userInput=e;const a=""===e?null:Number(e);n(C,a),se(a,!1)},me=a=>{const t=""!==a?Number(a):"";(e(t)&&!Number.isNaN(t)||""===a)&&se(t),pe(),Y.userInput=null},de=e=>{n("focus",e)},ce=e=>{var a,t;Y.userInput=null,null===Y.currentValue&&(null==(a=R.value)?void 0:a.input)&&(R.value.input.value=""),n("blur",e),r.validateEvent&&(null==(t=null==U?void 0:U.validate)||t.call(U,"blur").catch(e=>q()))},pe=()=>{Y.currentValue!==r.modelValue&&(Y.currentValue=r.modelValue)},ve=e=>{document.activeElement===e.target&&e.preventDefault()};return p(()=>r.modelValue,(e,a)=>{const t=ie(e,!0);null===Y.userInput&&t!==a&&(Y.currentValue=t)},{immediate:!0}),v(()=>{var a;const{min:t,max:l,modelValue:u}=r,i=null==(a=R.value)?void 0:a.input;if(i.setAttribute("role","spinbutton"),Number.isFinite(l)?i.setAttribute("aria-valuemax",String(l)):i.removeAttribute("aria-valuemax"),Number.isFinite(t)?i.setAttribute("aria-valuemin",String(t)):i.removeAttribute("aria-valuemin"),i.setAttribute("aria-valuenow",Y.currentValue||0===Y.currentValue?String(Y.currentValue):""),i.setAttribute("aria-disabled",String(ee.value)),!e(u)&&null!=u){let e=Number(u);Number.isNaN(e)&&(e=null),n(z,e)}i.addEventListener("wheel",ve,{passive:!1})}),b(()=>{var e,a;const t=null==(e=R.value)?void 0:e.input;null==t||t.setAttribute("aria-valuenow",`${null!=(a=Y.currentValue)?a:""}`)}),l({focus:()=>{var e,a;null==(a=null==(e=R.value)?void 0:e.focus)||a.call(e)},blur:()=>{var e,a;null==(a=null==(e=R.value)?void 0:e.blur)||a.call(e)}}),(e,a)=>(N(),f("div",{class:E([g(K).b(),g(K).m(g(Z)),g(K).is("disabled",g(ee)),g(K).is("without-controls",!e.controls),g(K).is("controls-right",g(W))]),onDragstart:k(()=>{},["prevent"])},[e.controls?y((N(),f("span",{key:0,role:"button","aria-label":g(u)("el.inputNumber.decrease"),class:E([g(K).e("decrease"),g(K).is("disabled",g(H))]),onKeydown:h(ue,["enter"])},[I(e.$slots,"decrease-icon",{},()=>[V(g(F),null,{default:S(()=>[g(W)?(N(),A(g(_),{key:0})):(N(),A(g(w),{key:1}))]),_:1})])],42,["aria-label","onKeydown"])),[[g(L),ue]]):x("v-if",!0),e.controls?y((N(),f("span",{key:1,role:"button","aria-label":g(u)("el.inputNumber.increase"),class:E([g(K).e("increase"),g(K).is("disabled",g(J))]),onKeydown:h(re,["enter"])},[I(e.$slots,"increase-icon",{},()=>[V(g(F),null,{default:S(()=>[g(W)?(N(),A(g(M),{key:0})):(N(),A(g($),{key:1}))]),_:1})])],42,["aria-label","onKeydown"])),[[g(L),re]]):x("v-if",!0),V(g(O),{id:e.id,ref_key:"input",ref:R,type:"number",step:e.step,"model-value":g(ae),placeholder:e.placeholder,readonly:e.readonly,disabled:g(ee),size:g(Z),max:e.max,min:e.min,name:e.name,"aria-label":e.ariaLabel,"validate-event":!1,inputmode:e.inputmode,onKeydown:[h(k(re,["prevent"]),["up"]),h(k(ue,["prevent"]),["down"])],onBlur:ce,onFocus:de,onInput:oe,onChange:me},j({_:2},[e.$slots.prefix?{name:"prefix",fn:S(()=>[I(e.$slots,"prefix")])}:void 0,e.$slots.suffix?{name:"suffix",fn:S(()=>[I(e.$slots,"suffix")])}:void 0]),1032,["id","step","model-value","placeholder","readonly","disabled","size","max","min","name","aria-label","inputmode","onKeydown"])],42,["onDragstart"]))}}),[["__file","input-number.vue"]]));export{J as E};
