import{d as s,ax as e,r as t,bu as o,az as _,c as a,cP as i,o as r,ap as l,g as m,f as p,k as n,m as u,i as c,w as E,C as v,aC as g,Q as d,R as j,n as V,E as L,e as R,D,l as I,h as O,j as A,cQ as P}from"./index.Ckm1SagX.js";import{E as T}from"./popper.DpZVcW1M.js";import{E as f}from"./popover.OzOh_E3J.js";import{a as h,E as b}from"./tab-pane.BwxvTe__.js";import{E as w}from"./scrollbar.6rbryiG1.js";/* empty css                *//* empty css              */import{E as y}from"./index.4JfkAhur.js";import{_ as k}from"./_plugin-vue_export-helper.BCo6x5W8.js";const x={class:"icon-grid"},C=["onClick"],S={class:"icon-grid"},$=["onClick"],q=k(s({__name:"index",props:e({modelValue:{type:String,default:""},width:{type:String,default:"500px"}},{modelValue:{type:String,required:!0,default:""},modelModifiers:{}}),emits:e(["update:modelValue"],["update:modelValue"]),setup(s,{emit:e}){const k=s,q=e,z=t(),U=t(),Q=t(!1),G=t("svg"),M=t([]),W=t(Object.keys(o)),X=_(s,"modelValue"),Z=t(""),B=t([]),F=t(W.value),H=a(()=>X.value&&X.value.startsWith("el-icon"));function J(s){G.value=s.props.name,K()}function K(){"svg"===G.value?B.value=Z.value?M.value.filter(s=>s.toLowerCase().includes(Z.value.toLowerCase())):M.value:F.value=Z.value?W.value.filter(s=>s.toLowerCase().includes(Z.value.toLowerCase())):W.value}function N(s){const e="element"===G.value?"el-icon-"+s:s;q("update:modelValue",e),Q.value=!1}function Y(){Q.value=!Q.value}function ss(){X.value=""}return i(z,()=>Q.value=!1,{ignore:[U]}),r(()=>{!function(){const s=Object.assign({"../../assets/icons/api.svg":()=>P(()=>import("./api.B2FJMEEY.js"),[],import.meta.url),"../../assets/icons/backtop.svg":()=>P(()=>import("./backtop.C5bGCvZX.js"),[],import.meta.url),"../../assets/icons/bell.svg":()=>P(()=>import("./bell.DVAWxzzU.js"),[],import.meta.url),"../../assets/icons/bilibili.svg":()=>P(()=>import("./bilibili.BDqZjNm-.js"),[],import.meta.url),"../../assets/icons/browser.svg":()=>P(()=>import("./browser.DkMvNvZf.js"),[],import.meta.url),"../../assets/icons/captcha.svg":()=>P(()=>import("./captcha.KixZeTLs.js"),[],import.meta.url),"../../assets/icons/cascader.svg":()=>P(()=>import("./cascader.Bxy7lIuJ.js"),[],import.meta.url),"../../assets/icons/client.svg":()=>P(()=>import("./client.fwafuIJ_.js"),[],import.meta.url),"../../assets/icons/close.svg":()=>P(()=>import("./close.3A3CTE27.js"),[],import.meta.url),"../../assets/icons/close_all.svg":()=>P(()=>import("./close_all.CpAPdWg9.js"),[],import.meta.url),"../../assets/icons/close_left.svg":()=>P(()=>import("./close_left.FPCWsnfT.js"),[],import.meta.url),"../../assets/icons/close_other.svg":()=>P(()=>import("./close_other.CtMgS35C.js"),[],import.meta.url),"../../assets/icons/close_right.svg":()=>P(()=>import("./close_right.WYLOaSAD.js"),[],import.meta.url),"../../assets/icons/cnblogs.svg":()=>P(()=>import("./cnblogs.B2Y7s_5c.js"),[],import.meta.url),"../../assets/icons/code.svg":()=>P(()=>import("./code.DbEIL5pZ.js"),[],import.meta.url),"../../assets/icons/collapse.svg":()=>P(()=>import("./collapse.ROHmwAah.js"),[],import.meta.url),"../../assets/icons/course.svg":()=>P(()=>import("./course.tmxzLDDa.js"),[],import.meta.url),"../../assets/icons/csdn.svg":()=>P(()=>import("./csdn.DUJUJ9Ed.js"),[],import.meta.url),"../../assets/icons/dict.svg":()=>P(()=>import("./dict.BlxtrVRf.js"),[],import.meta.url),"../../assets/icons/document.svg":()=>P(()=>import("./document.DqID0E9P.js"),[],import.meta.url),"../../assets/icons/down.svg":()=>P(()=>import("./down.By8c2p9v.js"),[],import.meta.url),"../../assets/icons/download.svg":()=>P(()=>import("./download.BcBrriOr.js"),[],import.meta.url),"../../assets/icons/enter.svg":()=>P(()=>import("./enter.BqL7pZcd.js"),[],import.meta.url),"../../assets/icons/esc.svg":()=>P(()=>import("./esc.eVgXU48X.js"),[],import.meta.url),"../../assets/icons/experiment.svg":()=>P(()=>import("./experiment.Dkd_Pnix.js"),[],import.meta.url),"../../assets/icons/file.svg":()=>P(()=>import("./file.LSCgjjDT.js"),[],import.meta.url),"../../assets/icons/fullscreen-exit.svg":()=>P(()=>import("./fullscreen-exit.DXwCcaMo.js"),[],import.meta.url),"../../assets/icons/fullscreen.svg":()=>P(()=>import("./fullscreen.dczNRBeD.js"),[],import.meta.url),"../../assets/icons/gitcode.svg":()=>P(()=>import("./gitcode.8kuXv4pf.js"),[],import.meta.url),"../../assets/icons/gitee.svg":()=>P(()=>import("./gitee.C7LaIEZ_.js"),[],import.meta.url),"../../assets/icons/github.svg":()=>P(()=>import("./github.DLO2QQQy.js"),[],import.meta.url),"../../assets/icons/homepage.svg":()=>P(()=>import("./homepage.BcyYp1IG.js"),[],import.meta.url),"../../assets/icons/java.svg":()=>P(()=>import("./java.CUWOGSYw.js"),[],import.meta.url),"../../assets/icons/juejin.svg":()=>P(()=>import("./juejin.BBqoLiLP.js"),[],import.meta.url),"../../assets/icons/language.svg":()=>P(()=>import("./language.k0ZPy50U.js"),[],import.meta.url),"../../assets/icons/menu.svg":()=>P(()=>import("./menu.CSAxtYMZ.js"),[],import.meta.url),"../../assets/icons/message.svg":()=>P(()=>import("./message.CTraJOIE.js"),[],import.meta.url),"../../assets/icons/monitor.svg":()=>P(()=>import("./monitor.Cm5WDh5_.js"),[],import.meta.url),"../../assets/icons/project.svg":()=>P(()=>import("./project.CAkqf0A3.js"),[],import.meta.url),"../../assets/icons/qq.svg":()=>P(()=>import("./qq.X0Hffnhw.js"),[],import.meta.url),"../../assets/icons/refresh.svg":()=>P(()=>import("./refresh.B5ExwFoE.js"),[],import.meta.url),"../../assets/icons/role.svg":()=>P(()=>import("./role.DDpGZwDf.js"),[],import.meta.url),"../../assets/icons/search.svg":()=>P(()=>import("./search.BuhcqAYw.js"),[],import.meta.url),"../../assets/icons/setting.svg":()=>P(()=>import("./setting.CfydkD5J.js"),[],import.meta.url),"../../assets/icons/size.svg":()=>P(()=>import("./size.C48ZYpz_.js"),[],import.meta.url),"../../assets/icons/system.svg":()=>P(()=>import("./system.BjyKXCM4.js"),[],import.meta.url),"../../assets/icons/table.svg":()=>P(()=>import("./table.C6Xt0YCE.js"),[],import.meta.url),"../../assets/icons/teaching.svg":()=>P(()=>import("./teaching.COVDRXLK.js"),[],import.meta.url),"../../assets/icons/todo.svg":()=>P(()=>import("./todo.C6Ti5UL0.js"),[],import.meta.url),"../../assets/icons/tree.svg":()=>P(()=>import("./tree.ByH7Oq4A.js"),[],import.meta.url),"../../assets/icons/typescript.svg":()=>P(()=>import("./typescript.C8n9I--t.js"),[],import.meta.url),"../../assets/icons/up.svg":()=>P(()=>import("./up.RRIFBrWs.js"),[],import.meta.url),"../../assets/icons/user.svg":()=>P(()=>import("./user.rrOxQVjB.js"),[],import.meta.url),"../../assets/icons/visitor.svg":()=>P(()=>import("./visitor.C-ADb4dr.js"),[],import.meta.url),"../../assets/icons/vue.svg":()=>P(()=>import("./vue.BjDXW0c2.js"),[],import.meta.url),"../../assets/icons/wechat.svg":()=>P(()=>import("./wechat.ca-m1rk6.js"),[],import.meta.url),"../../assets/icons/xml.svg":()=>P(()=>import("./xml.DDPJ6DAu.js"),[],import.meta.url)});for(const e in s){const s=e.replace(/.*\/(.*)\.svg$/,"$1");M.value.push(s)}B.value=M.value}(),X.value&&(W.value.includes(X.value.replace("el-icon-",""))?G.value="element":G.value="svg")}),(s,e)=>{const t=L,o=l("CircleClose"),_=l("ArrowDown"),a=y,i=T,r=w,P=b,q=h,M=f;return p(),m("div",{ref_key:"iconSelectRef",ref:z,style:n({width:k.width})},[u(M,{visible:c(Q),width:k.width,placement:"bottom-end"},{reference:E(()=>[v("div",{onClick:e[1]||(e[1]=s=>Q.value=!c(Q))},[I(s.$slots,"default",{},()=>[u(a,{modelValue:X.value,"onUpdate:modelValue":e[0]||(e[0]=s=>X.value=s),readonly:"",placeholder:"点击选择图标",class:"reference"},{prepend:E(()=>[c(H)?(p(),R(t,{key:0},{default:E(()=>[(p(),R(D(X.value.replace("el-icon-",""))))]),_:1})):(p(),m("div",{key:1,class:V(`i-svg:${X.value}`)},null,2))]),suffix:E(()=>[X.value?(p(),R(t,{key:0,style:{"margin-right":"8px"},onClick:A(ss,["stop"])},{default:E(()=>[u(o)]),_:1})):O("",!0),u(t,{style:n({transform:c(Q)?"rotate(180deg)":"rotate(0)",transition:"transform .5s"})},{default:E(()=>[u(_,{onClick:A(Y,["stop"])})]),_:1},8,["style"])]),_:1},8,["modelValue"])],!0)])]),default:E(()=>[v("div",{ref_key:"popoverContentRef",ref:U},[u(a,{modelValue:c(Z),"onUpdate:modelValue":e[2]||(e[2]=s=>g(Z)?Z.value=s:null),placeholder:"搜索图标",clearable:"",onInput:K},null,8,["modelValue"]),u(q,{modelValue:c(G),"onUpdate:modelValue":e[3]||(e[3]=s=>g(G)?G.value=s:null),onTabClick:J},{default:E(()=>[u(P,{label:"SVG 图标",name:"svg"},{default:E(()=>[u(r,{height:"300px"},{default:E(()=>[v("ul",x,[(p(!0),m(d,null,j(c(B),s=>(p(),m("li",{key:"svg-"+s,class:"icon-grid-item",onClick:e=>N(s)},[u(i,{content:s,placement:"bottom",effect:"light"},{default:E(()=>[v("div",{class:V(`i-svg:${s}`)},null,2)]),_:2},1032,["content"])],8,C))),128))])]),_:1})]),_:1}),u(P,{label:"Element 图标",name:"element"},{default:E(()=>[u(r,{height:"300px"},{default:E(()=>[v("ul",S,[(p(!0),m(d,null,j(c(F),s=>(p(),m("li",{key:s,class:"icon-grid-item",onClick:e=>N(s)},[u(t,null,{default:E(()=>[(p(),R(D(s)))]),_:2},1024)],8,$))),128))])]),_:1})]),_:1})]),_:1},8,["modelValue"])],512)]),_:3},8,["visible","width"])],4)}}}),[["__scopeId","data-v-3a2d6a32"]]);export{q as _};
