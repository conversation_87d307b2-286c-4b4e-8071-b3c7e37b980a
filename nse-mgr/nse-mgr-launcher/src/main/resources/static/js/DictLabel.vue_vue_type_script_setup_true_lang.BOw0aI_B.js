import{d as e,r as a,bv as t,I as s,e as i,g as l,i as o,w as n,a1 as d,F as r,f as u}from"./index.Ckm1SagX.js";/* empty css            */import{E as c}from"./index.BPj3iklg.js";const m={key:1},p=e({__name:"DictLabel",props:{code:String,modelValue:[String,Number],size:{type:String,default:"default"}},setup(e){const p=e,y=a(""),g=a(),f=a(p.size),v=t(),b=async()=>{if(!p.code||void 0===p.modelValue)return;const{label:e,tagType:a}=await(async(e,a)=>{await v.loadDictItems(e);const t=v.getDictItems(e).find(e=>e.value==a);return{label:(null==t?void 0:t.label)||"",tagType:null==t?void 0:t.tagType}})(p.code,p.modelValue);y.value=e,g.value=a};return s([()=>p.code,()=>p.modelValue],async()=>{p.code&&await b()},{immediate:!0}),(e,a)=>{const t=c;return o(g)?(u(),i(t,{key:0,type:o(g),size:o(f)},{default:n(()=>[d(r(o(y)),1)]),_:1},8,["type","size"])):(u(),l("span",m,r(o(y)),1))}}});export{p as _};
