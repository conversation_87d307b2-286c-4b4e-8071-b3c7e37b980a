import{aw as e}from"./index.Ckm1SagX.js";const t="/api/user",a={getPage:a=>e({url:`${t}/page`,method:"get",params:a}),getTree:a=>e({url:`${t}/tree`,method:"get",params:{containExpiredAccount:a}}),getAll:()=>e({url:`${t}`,method:"get"}),getById:a=>e({url:`${t}/${a}`,method:"get"}),getClassList:a=>e({url:`${t}/classList`,method:"get",params:{className:a}}),getByAccount:(a,o)=>e({url:`${t}/account/${a}`,method:"get",params:{userType:o}}),save:a=>e({url:`${t}`,method:"post",data:a}),resetPassword:a=>e({url:`${t}/resetPassword`,method:"post",data:a}),batchSetValidDate:a=>e({url:`${t}/batchSetValidDate`,method:"post",data:a}),downloadTemplate:a=>e({url:`${t}/downloadTemplate`,method:"get",params:{userType:a},responseType:"blob"}),importData(a,o){const d=new FormData;return d.append("file",a),d.append("userType",o),e({url:`${t}/importFile`,method:"post",data:d,timeout:0,headers:{"Content-Type":"multipart/form-data"}})},batchAdd:a=>e({url:`${t}/batchAdd`,method:"post",data:a}),deleteById:a=>e({url:`${t}/${a}`,method:"delete"}),deleteBatch:a=>e({url:`${t}/batch`,method:"delete",data:a}),getRoles:a=>e({url:`${t}/role/${a}`,method:"get"}),assignRoles:(a,o)=>e({url:`${t}/role/${a}`,method:"post",data:o}),getMenus:a=>e({url:`${t}/menu/${a}`,method:"get"})};export{a as A};
