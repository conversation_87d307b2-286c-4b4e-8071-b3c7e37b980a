import{b7 as e,d as t,t as o,b as n,m as s,a9 as a,l as u,z as d,o as l,S as i,a8 as r,J as c,aC as m,c as v,H as f,a4 as p,I as y,bV as b,a3 as h,bh as E,a5 as M}from"./index.Ckm1SagX.js";import{P as w}from"./vnode.BkZiIFpS.js";import{t as x}from"./aria.C1IWO_Rd.js";import{g}from"./scroll.XdyICIdv.js";const k=t=>{if(!t)return{onClick:e,onMousedown:e,onMouseup:e};let o=!1,n=!1;return{onClick:e=>{o&&n&&t(e),o=n=!1},onMousedown:e=>{o=e.target===e.currentTarget},onMouseup:e=>{n=e.target===e.currentTarget}}},Y=o({mask:{type:Boolean,default:!0},customMaskEvent:Boolean,overlayClass:{type:d([String,Array,Object])},zIndex:{type:d([String,Number])}});const C=t({name:"ElOverlay",props:Y,emits:{click:e=>e instanceof MouseEvent},setup(e,{slots:t,emit:o}){const d=n("overlay"),{onClick:l,onMousedown:i,onMouseup:r}=k(e.customMaskEvent?void 0:e=>{o("click",e)});return()=>e.mask?s("div",{class:[d.b(),e.overlayClass],style:{zIndex:e.zIndex},onClick:l,onMousedown:i,onMouseup:r},[u(t,"default")],w.STYLE|w.CLASS|w.PROPS,["onClick","onMouseup","onMousedown"]):a("div",{class:e.overlayClass,style:{zIndex:e.zIndex,position:"fixed",top:"0px",right:"0px",bottom:"0px",left:"0px"}},[u(t,"default")])}}),L=(e,t,o,n)=>{const s={offsetX:0,offsetY:0},a=(t,o)=>{if(e.value){const{offsetX:a,offsetY:u}=s,d=e.value.getBoundingClientRect(),l=d.left,i=d.top,r=d.width,m=d.height,v=document.documentElement.clientWidth,f=document.documentElement.clientHeight,p=-l+a,y=-i+u,b=v-l-r+a,h=f-i-(m<f?m:0)+u;(null==n?void 0:n.value)||(t=Math.min(Math.max(t,p),b),o=Math.min(Math.max(o,y),h)),s.offsetX=t,s.offsetY=o,e.value.style.transform=`translate(${c(t)}, ${c(o)})`}},u=e=>{const t=e.clientX,o=e.clientY,{offsetX:n,offsetY:u}=s,d=e=>{const s=n+e.clientX-t,d=u+e.clientY-o;a(s,d)},l=()=>{document.removeEventListener("mousemove",d),document.removeEventListener("mouseup",l)};document.addEventListener("mousemove",d),document.addEventListener("mouseup",l)},d=()=>{t.value&&e.value&&(t.value.removeEventListener("mousedown",u),window.removeEventListener("resize",m))},m=()=>{const{offsetX:e,offsetY:t}=s;a(e,t)};return l(()=>{i(()=>{o.value?t.value&&e.value&&(t.value.addEventListener("mousedown",u),window.addEventListener("resize",m)):d()})}),r(()=>{d()}),{resetPosition:()=>{s.offsetX=0,s.offsetY=0,e.value&&(e.value.style.transform="")},updatePosition:m}},z=(e,t={})=>{m(e)||x("[useLockscreen]","You need to pass a ref param to this function");const o=t.ns||n("popup"),s=v(()=>o.bm("parent","hidden"));if(!f||p(document.body,s.value))return;let a=0,u=!1,d="0";const l=()=>{setTimeout(()=>{"undefined"!=typeof document&&u&&document&&(document.body.style.width=d,M(document.body,s.value))},200)};y(e,e=>{if(!e)return void l();u=!p(document.body,s.value),u&&(d=document.body.style.width,h(document.body,s.value)),a=g(o.namespace.value);const t=document.documentElement.clientHeight<document.body.scrollHeight,n=E(document.body,"overflowY");a>0&&(t||"scroll"===n)&&u&&(document.body.style.width=`calc(100% - ${a}px)`)}),b(()=>l())};export{C as E,L as a,k as b,z as u};
