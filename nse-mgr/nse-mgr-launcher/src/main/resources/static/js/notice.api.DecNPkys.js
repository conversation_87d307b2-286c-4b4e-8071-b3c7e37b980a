import{aw as e}from"./index.Ckm1SagX.js";const t="/api/v1/notices",a={getPage:a=>e({url:`${t}/page`,method:"get",params:a}),getFormData:a=>e({url:`${t}/${a}/form`,method:"get"}),create:a=>e({url:`${t}`,method:"post",data:a}),update:(a,r)=>e({url:`${t}/${a}`,method:"put",data:r}),deleteByIds:a=>e({url:`${t}/${a}`,method:"delete"}),publish:a=>e({url:`${t}/${a}/publish`,method:"put"}),revoke:a=>e({url:`${t}/${a}/revoke`,method:"put"}),getDetail:a=>e({url:`${t}/${a}/detail`,method:"get"}),readAll:()=>e({url:`${t}/read-all`,method:"put"}),getMyNoticePage:a=>e({url:`${t}/my-page`,method:"get",params:a})};export{a as N};
