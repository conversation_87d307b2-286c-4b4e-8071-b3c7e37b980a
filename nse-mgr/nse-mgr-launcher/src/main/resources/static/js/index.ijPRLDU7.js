import{aw as e,d as t,r as a,V as i,o as n,g as s,f as r,C as o,m as l,e as c,w as p,a1 as m,h as d,i as u,d9 as f,d5 as y,be as h,da as g,E as v,X as j,F as _,aP as w}from"./index.Ckm1SagX.js";/* empty css                */import{E as b}from"./card.BfhlXze7.js";import{E as x,a as C}from"./table-column.DQa6-hu-.js";import"./checkbox.CyAsOZKA.js";/* empty css                */import"./popper.DpZVcW1M.js";import"./scrollbar.6rbryiG1.js";/* empty css            *//* empty css             */import{_ as k}from"./index.r55iLQne.js";import{E as T}from"./index.BPj3iklg.js";import{E as L}from"./index.CMOQuMWt.js";import{v as E}from"./directive.C7vihscI.js";import{E as $}from"./index.KtapGdwl.js";import{_ as B}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./aria.C1IWO_Rd.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./index.BLy3nyPI.js";import"./_baseIteratee.PZHdcgYb.js";import"./isEqual.CZKKciWh.js";import"./castArray.Chmjnshw.js";import"./debounce.YgIwzEIs.js";import"./index.B0geSHq7.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./event.BwRzfsZt.js";import"./index.BRUQ9gWw.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";import"./pagination.TL6aFrlm.js";import"./select.DHkh6uhw.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./index.Byj-i824.js";import"./scroll.XdyICIdv.js";import"./vnode.BkZiIFpS.js";import"./index.4JfkAhur.js";import"./index.DJHzyRe5.js";/* empty css              */import"./index.CbYeWxT8.js";import"./index.BjYFza3j.js";const U="/api/mgr/license",z={class:"app-container"},M={class:"search-container"},A={class:"license-info"},F={class:"license-content"},I={class:"license-status"},P={class:"license-link"},S={class:"license-tips"},D=B(t({__name:"index",setup(t){const B=a(),D=a(!1),W=a(0),q=a("未授权"),H=i({pageNum:1,pageSize:10}),N=a(),O=async()=>{D.value=!0;try{const t=await(async t=>e({url:`${U}/page`,method:"post",params:t}))(H);N.value=t.rows,W.value=t.total}catch(t){w.error("查询失败")}finally{D.value=!1}};n(()=>{O(),R()});const R=async()=>{const t=await(async()=>e({url:`${U}/status`,method:"get"}))();q.value=t},V=async()=>{try{D.value=!0;const t=await(async()=>e({url:`${U}/hwinfo`,method:"get"}))();$.confirm(`<div style="font-family: monospace; word-break: break-all;">${t}</div>`,"机器码",{dangerouslyUseHTMLString:!0,confirmButtonText:"关闭",cancelButtonText:"复制",showCancelButton:!0,distinguishCancelAndClose:!0,type:"info"}).then(()=>{}).catch(e=>{"cancel"===e&&(navigator.clipboard.writeText(t),w.success("机器码已复制到剪贴板"))})}catch(t){w.error("获取机器码失败")}finally{D.value=!1}},X=async()=>{const t=document.createElement("input");t.type="file",t.accept=".lic",t.style.display="none",document.body.appendChild(t),t.onchange=async a=>{const i=a.target.files;if(i&&i.length>0){const a=i[0];try{if(!a.name.toLowerCase().endsWith(".lic"))throw new Error("请选择有效的.lic格式License文件");if(a.size>1048576)throw new Error("License文件过大，请确认文件正确性");D.value=!0;const t=await(async t=>{const a=new FormData;return a.append("file",t),e({url:`${U}/activate`,method:"post",data:a,headers:{"Content-Type":"multipart/form-data"}})})(a);$.confirm(`账号并发登录数量为：普通用户${t.permitUserCnt} + 管理员${t.permitMgrCnt}`,"授权激活成功",{confirmButtonText:"确定",type:"success"}),O()}catch(n){n.message?w.error(n.message):w.error("License激活失败")}finally{D.value=!1,document.body.removeChild(t)}}else document.body.removeChild(t)},t.click()},Y=async()=>{const t=document.createElement("input");t.type="file",t.accept=".lic",t.style.display="none",document.body.appendChild(t),t.onchange=async a=>{const i=a.target.files;if(i&&i.length>0){const a=i[0];try{if(!a.name.toLowerCase().endsWith(".lic"))throw new Error("请选择有效的.lic格式License文件");if(a.size>1048576)throw new Error("License文件过大，请确认文件正确性");D.value=!0;const t=await(async t=>{const a=new FormData;return a.append("file",t),e({url:`${U}/update`,method:"put",data:a,headers:{"Content-Type":"multipart/form-data"}})})(a);$.confirm(`账号并发登录数量为：普通用户${t.permitUserCnt} + 管理员${t.permitMgrCnt}`,"授权激活成功",{confirmButtonText:"确定",type:"success"}),O()}catch(n){n.message?w.error(n.message):w.error("License更新失败")}finally{D.value=!1,document.body.removeChild(t)}}else document.body.removeChild(t)},t.click()},G=async()=>{try{await $.confirm("此操作将注销该系统授权，所有功能将均无法使用，请确认是否继续?","警告",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),D.value=!0;const t=await(async()=>e({url:`${U}/cancel`,method:"get"}))();$.confirm(`注销码为：${t}`,"注销授权成功",{confirmButtonText:"确定",type:"success"}),R(),O()}catch(t){"cancel"!==t&&(t.message?w.error(t.message):w.error("License注销失败"))}finally{D.value=!1}};return(e,t)=>{const a=T,i=L,n=v,w=C,$=x,U=b,R=E;return r(),s("div",z,[o("div",M,[o("div",A,[t[10]||(t[10]=o("div",{class:"license-header"},[o("h3",null,"基本信息")],-1)),o("div",F,[o("div",I,[t[4]||(t[4]=o("span",{class:"status-label"},"授权状态：",-1)),"已授权"===q.value?(r(),c(a,{key:0,type:"success",effect:"light"},{default:p(()=>t[0]||(t[0]=[m("已授权")])),_:1,__:[0]})):"已过期"===q.value?(r(),c(a,{key:1,type:"warning",effect:"light"},{default:p(()=>t[1]||(t[1]=[m("已过期")])),_:1,__:[1]})):"已注销"===q.value?(r(),c(a,{key:2,type:"danger",effect:"light"},{default:p(()=>t[2]||(t[2]=[m("已注销")])),_:1,__:[2]})):(r(),c(a,{key:3,type:"info",effect:"light"},{default:p(()=>t[3]||(t[3]=[m("未授权")])),_:1,__:[3]}))]),o("div",P,[l(i,{icon:u(f),type:"primary",onClick:V},{default:p(()=>t[5]||(t[5]=[m(" 获取机器码")])),_:1,__:[5]},8,["icon"]),"未授权"===q.value?(r(),c(i,{key:0,icon:u(y),type:"primary",onClick:X},{default:p(()=>t[6]||(t[6]=[m(" 激活License")])),_:1,__:[6]},8,["icon"])):d("",!0),"未授权"!==q.value?(r(),c(i,{key:1,icon:u(y),type:"primary",onClick:Y},{default:p(()=>t[7]||(t[7]=[m(" 更新License")])),_:1,__:[7]},8,["icon"])):d("",!0),"未授权"!==q.value?(r(),c(i,{key:2,icon:u(h),type:"danger",onClick:G},{default:p(()=>t[8]||(t[8]=[m(" 注销授权")])),_:1,__:[8]},8,["icon"])):d("",!0)]),o("div",S,[l(n,null,{default:p(()=>[l(u(g))]),_:1}),t[9]||(t[9]=o("span",null,"授权到期提醒，请及时续费授权以免影响License文件。",-1))])])])]),l(U,{shadow:"never",class:"table-container"},{default:p(()=>[t[11]||(t[11]=o("div",{class:"table-header"},[o("h3",null,"授权信息")],-1)),j((r(),c($,{ref_key:"dataTableRef",ref:B,data:N.value,border:"",stripe:""},{default:p(()=>[l(w,{label:"序号",type:"index",width:"60",align:"center"}),l(w,{label:"授权产品信息",prop:"productInfo","min-width":"150",align:"center"}),l(w,{label:"授权码信息",prop:"activationCode","min-width":"220",align:"center","show-overflow-tooltip":""}),l(w,{label:"账号并发登录数量",prop:"contact","min-width":"180",align:"center"},{default:p(e=>[o("div",null,"普通用户"+_(e.row.permitUserCnt)+" + 管理员 "+_(e.row.permitMgrCnt),1)]),_:1}),l(w,{label:"有效期",prop:"validType",width:"100",align:"center"}),l(w,{label:"状态",prop:"status",width:"100",align:"center"}),l(w,{label:"注销码",prop:"cancelCode",width:"200",align:"center"}),l(w,{label:"激活时间",prop:"activationTime",width:"180",align:"center"}),l(w,{label:"注销时间",prop:"cancelTime",width:"180",align:"center"})]),_:1},8,["data"])),[[R,D.value]]),W.value>0?(r(),c(k,{key:0,total:W.value,page:H.pageNum,limit:H.pageSize,onPagination:O},null,8,["total","page","limit"])):d("",!0)]),_:1,__:[11]})])}}}),[["__scopeId","data-v-e4f706c4"]]);export{D as default};
