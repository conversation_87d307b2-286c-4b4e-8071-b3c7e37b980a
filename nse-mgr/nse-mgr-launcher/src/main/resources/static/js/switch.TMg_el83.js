import{ba as e,L as a,aV as i,t,z as n,v as c,_ as l,d as s,b as o,c as v,r as d,J as r,I as u,o as f,g as p,f as m,C as y,h as b,a0 as h,i as k,n as I,e as x,w as g,D as V,E as T,F as _,l as S,m as w,a_ as C,k as j,j as B,a2 as A,bc as E,q as N}from"./index.Ckm1SagX.js";import{i as P}from"./index.KtapGdwl.js";import{u as L}from"./index.BLy3nyPI.js";import{I as q,C as z,U as F}from"./event.BwRzfsZt.js";import{u as K,a as $,c as D,b as J}from"./use-form-common-props.BSYTvb6G.js";import{t as U,d as Y}from"./aria.C1IWO_Rd.js";const G=t({modelValue:{type:[Boolean,String,Number],default:!1},disabled:Boolean,loading:Boolean,size:{type:String,validator:P},width:{type:[String,Number],default:""},inlinePrompt:Boolean,inactiveActionIcon:{type:c},activeActionIcon:{type:c},activeIcon:{type:c},inactiveIcon:{type:c},activeText:{type:String,default:""},inactiveText:{type:String,default:""},activeValue:{type:[Boolean,String,Number],default:!0},inactiveValue:{type:[Boolean,String,Number],default:!1},name:{type:String,default:""},validateEvent:{type:Boolean,default:!0},beforeChange:{type:n(Function)},id:String,tabindex:{type:[String,Number]},...L(["ariaLabel"])}),H={[F]:t=>e(t)||a(t)||i(t),[z]:t=>e(t)||a(t)||i(t),[q]:t=>e(t)||a(t)||i(t)},M="ElSwitch",O=s({name:M});const Q=N(l(s({...O,props:G,emits:H,setup(a,{expose:i,emit:t}){const n=a,{formItem:c}=K(),l=$(),s=o("switch"),{inputId:N}=D(n,{formItemContext:c}),P=J(v(()=>n.loading)),L=d(!1!==n.modelValue),G=d(),H=d(),O=v(()=>[s.b(),s.m(l.value),s.is("disabled",P.value),s.is("checked",Z.value)]),Q=v(()=>[s.e("label"),s.em("label","left"),s.is("active",!Z.value)]),R=v(()=>[s.e("label"),s.em("label","right"),s.is("active",Z.value)]),W=v(()=>({width:r(n.width)}));u(()=>n.modelValue,()=>{L.value=!0});const X=v(()=>!!L.value&&n.modelValue),Z=v(()=>X.value===n.activeValue);[n.activeValue,n.inactiveValue].includes(X.value)||(t(F,n.inactiveValue),t(z,n.inactiveValue),t(q,n.inactiveValue)),u(Z,e=>{var a;G.value.checked=e,n.validateEvent&&(null==(a=null==c?void 0:c.validate)||a.call(c,"change").catch(e=>Y()))});const ee=()=>{const e=Z.value?n.inactiveValue:n.activeValue;t(F,e),t(z,e),t(q,e),A(()=>{G.value.checked=Z.value})},ae=()=>{if(P.value)return;const{beforeChange:a}=n;if(!a)return void ee();const i=a();[E(i),e(i)].includes(!0)||U(M,"beforeChange must return type `Promise<boolean>` or `boolean`"),E(i)?i.then(e=>{e&&ee()}).catch(e=>{}):i&&ee()};return f(()=>{G.value.checked=Z.value}),i({focus:()=>{var e,a;null==(a=null==(e=G.value)?void 0:e.focus)||a.call(e)},checked:Z}),(e,a)=>(m(),p("div",{class:I(k(O)),onClick:B(ae,["prevent"])},[y("input",{id:k(N),ref_key:"input",ref:G,class:I(k(s).e("input")),type:"checkbox",role:"switch","aria-checked":k(Z),"aria-disabled":k(P),"aria-label":e.ariaLabel,name:e.name,"true-value":e.activeValue,"false-value":e.inactiveValue,disabled:k(P),tabindex:e.tabindex,onChange:ee,onKeydown:h(ae,["enter"])},null,42,["id","aria-checked","aria-disabled","aria-label","name","true-value","false-value","disabled","tabindex","onKeydown"]),e.inlinePrompt||!e.inactiveIcon&&!e.inactiveText?b("v-if",!0):(m(),p("span",{key:0,class:I(k(Q))},[e.inactiveIcon?(m(),x(k(T),{key:0},{default:g(()=>[(m(),x(V(e.inactiveIcon)))]),_:1})):b("v-if",!0),!e.inactiveIcon&&e.inactiveText?(m(),p("span",{key:1,"aria-hidden":k(Z)},_(e.inactiveText),9,["aria-hidden"])):b("v-if",!0)],2)),y("span",{ref_key:"core",ref:H,class:I(k(s).e("core")),style:j(k(W))},[e.inlinePrompt?(m(),p("div",{key:0,class:I(k(s).e("inner"))},[e.activeIcon||e.inactiveIcon?(m(),x(k(T),{key:0,class:I(k(s).is("icon"))},{default:g(()=>[(m(),x(V(k(Z)?e.activeIcon:e.inactiveIcon)))]),_:1},8,["class"])):e.activeText||e.inactiveText?(m(),p("span",{key:1,class:I(k(s).is("text")),"aria-hidden":!k(Z)},_(k(Z)?e.activeText:e.inactiveText),11,["aria-hidden"])):b("v-if",!0)],2)):b("v-if",!0),y("div",{class:I(k(s).e("action"))},[e.loading?(m(),x(k(T),{key:0,class:I(k(s).is("loading"))},{default:g(()=>[w(k(C))]),_:1},8,["class"])):k(Z)?S(e.$slots,"active-action",{key:1},()=>[e.activeActionIcon?(m(),x(k(T),{key:0},{default:g(()=>[(m(),x(V(e.activeActionIcon)))]),_:1})):b("v-if",!0)]):k(Z)?b("v-if",!0):S(e.$slots,"inactive-action",{key:2},()=>[e.inactiveActionIcon?(m(),x(k(T),{key:0},{default:g(()=>[(m(),x(V(e.inactiveActionIcon)))]),_:1})):b("v-if",!0)])],2)],6),e.inlinePrompt||!e.activeIcon&&!e.activeText?b("v-if",!0):(m(),p("span",{key:1,class:I(k(R))},[e.activeIcon?(m(),x(k(T),{key:0},{default:g(()=>[(m(),x(V(e.activeIcon)))]),_:1})):b("v-if",!0),!e.activeIcon&&e.activeText?(m(),p("span",{key:1,"aria-hidden":!k(Z)},_(e.activeText),9,["aria-hidden"])):b("v-if",!0)],2))],10,["onClick"]))}}),[["__file","switch.vue"]]));export{Q as E};
