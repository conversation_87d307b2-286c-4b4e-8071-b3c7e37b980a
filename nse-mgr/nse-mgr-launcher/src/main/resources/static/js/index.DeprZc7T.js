import{f as e}from"./vnode.BkZiIFpS.js";import{s as t,d as n,a9 as r,cN as o,o as s,bS as a}from"./index.Ckm1SagX.js";const d=(d,l)=>{const i=t({}),u=t([]),p=new WeakMap,f=()=>{u.value=((t,n,r)=>e(t.subTree).filter(e=>{var t;return a(e)&&(null==(t=e.type)?void 0:t.name)===n&&!!e.component}).map(e=>e.component.uid).map(e=>r[e]).filter(e=>!!e))(d,l,i.value)},m=e=>e.render(),c=n({setup:(e,{slots:t})=>()=>(f(),t.default?r(m,{render:t.default}):null)});return{children:u,addChild:e=>{i.value[e.uid]=e,o(i),s(()=>{const t=e.getVnode().el,n=t.parentNode;if(!p.has(n)){p.set(n,[]);const e=n.insertBefore.bind(n);n.insertBefore=(t,r)=>(p.get(n).some(e=>t===e||r===e)&&o(i),e(t,r))}p.get(n).push(t)})},removeChild:e=>{delete i.value[e.uid],o(i);const t=e.getVnode().el,n=t.parentNode,r=p.get(n),s=r.indexOf(t);r.splice(s,1)},ChildrenSorter:c}};export{d as u};
