import{cG as e,cH as t,bJ as s}from"./index.Ckm1SagX.js";const n=e("experiment",()=>{const e=t("experiment_sessions",{});function n(){return Object.values(e.value)}function r(t=72e5){const s=Date.now();let n=0;for(const[r,o]of Object.entries(e.value))s-o.lastAccessAt>t&&(delete e.value[r],n++);return n}let o=null;return{sessions:s(e),createSession:function(t){const s=Math.random().toString(36).substring(2,12)+Date.now().toString(36),n=Date.now(),r={id:s,signUrl:t.signUrl,title:t.title||"实验环境",hostId:t.hostId,serverIp:t.serverIp,serverPort:t.serverPort,userId:t.userId,createdAt:n,lastAccessAt:n};return e.value[s]=r,s},getSession:function(t){const s=e.value[t];return s&&(s.lastAccessAt=Date.now(),e.value[t]=s),s},removeSession:function(t){const s=t in e.value;return s&&delete e.value[t],s},getAllSessions:n,cleanupExpiredSessions:r,getSessionStats:function(){const e=n(),t=Date.now();return{total:e.length,active:e.filter(e=>t-e.lastAccessAt<18e5).length,oldest:e.reduce((e,t)=>!e||t.createdAt<e.createdAt?t:e,null)}},clearAllSessions:function(){Object.keys(e.value).length,e.value={}},startAutoCleanup:function(){o||(o=setInterval(()=>{r()},18e5))},stopAutoCleanup:function(){o&&(clearInterval(o),o=null)}}});export{n as u};
