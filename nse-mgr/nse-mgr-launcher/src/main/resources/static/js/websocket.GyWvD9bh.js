import{d as e,d2 as a,r as s,I as t,o as l,a8 as n,g as r,f as o,m as u,w as d,a1 as i,aC as p,i as m,e as c,C as _,Q as f,R as v,n as y,F as j}from"./index.Ckm1SagX.js";import{E as b,a as h}from"./form-item.CUMILu98.js";import{E as g}from"./card.BfhlXze7.js";import{a as x,E as k}from"./col.Bi-hDQ18.js";/* empty css            *//* empty css               *//* empty css              *//* empty css             */import{u as V}from"./useStomp.CNg0aTo8.js";import{E as C}from"./index.CMOQuMWt.js";import{E as I}from"./index.4JfkAhur.js";import{E as w}from"./index.CbYeWxT8.js";import{E}from"./index.BPj3iklg.js";import{_ as S}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./castArray.Chmjnshw.js";import"./aria.C1IWO_Rd.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./index.BRUQ9gWw.js";import"./index.BLy3nyPI.js";import"./event.BwRzfsZt.js";import"./index.DJHzyRe5.js";import"./index.Byj-i824.js";const U={class:"app-container"},q={class:"chat-messages-wrapper"},A={key:0,class:"chat-message__content"},F={class:"text-gray-600"},H={key:1},O=S(e({__name:"websocket",setup(e){const S=a(),O=s(""),W=s([]),Y=s("亲爱的朋友们，系统已恢复最新状态。"),G=s("Hi, "+S.userInfo.username+" 这里是点对点消息示例！"),J=s("root"),{isConnected:N,connect:P,subscribe:Q,disconnect:R}=V({debug:!0});function T(){P()}function X(){R()}function Z(){N.value&&(Q("/app/broadcast",()=>{}),W.value.push({sender:S.userInfo.username,content:Y.value}))}function $(){N.value&&(Q(`/app/sendToUser/${J.value}`,()=>{}),W.value.push({sender:S.userInfo.username,content:G.value}))}return t(()=>N.value,e=>{e?(Q("/topic/notice",e=>{W.value.push({sender:"Server",content:e.body})}),Q("/user/queue/greeting",e=>{const a=JSON.parse(e.body);W.value.push({sender:a.sender,content:a.content})}),W.value.push({sender:"Server",content:"Websocket 已连接",type:"tip"})):W.value.push({sender:"Server",content:"Websocket 已断开",type:"tip"})}),l(()=>{T()}),n(()=>{X()}),(e,a)=>{const s=C,t=I,l=w,n=x,V=E,P=k,Q=g,R=h,z=b;return o(),r("div",U,[u(s,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/websocket.vue",type:"primary",target:"_blank",class:"mb-[20px]"},{default:d(()=>a[4]||(a[4]=[i(" 示例源码 请点击>>>> ")])),_:1,__:[4]}),u(P,{gutter:10},{default:d(()=>[u(n,{span:12},{default:d(()=>[u(Q,null,{default:d(()=>[u(P,null,{default:d(()=>[u(n,{span:18},{default:d(()=>[u(t,{modelValue:m(O),"onUpdate:modelValue":a[0]||(a[0]=e=>p(O)?O.value=e:null),style:{width:"200px"}},null,8,["modelValue"]),u(l,{type:"primary",class:"ml-5",disabled:m(N),onClick:T},{default:d(()=>a[5]||(a[5]=[i(" 连接 ")])),_:1,__:[5]},8,["disabled"]),u(l,{type:"danger",disabled:!m(N),onClick:X},{default:d(()=>a[6]||(a[6]=[i(" 断开 ")])),_:1,__:[6]},8,["disabled"])]),_:1}),u(n,{span:6,class:"text-right"},{default:d(()=>[a[9]||(a[9]=i(" 连接状态： ")),m(N)?(o(),c(V,{key:0,type:"success"},{default:d(()=>a[7]||(a[7]=[i("已连接")])),_:1,__:[7]})):(o(),c(V,{key:1,type:"info"},{default:d(()=>a[8]||(a[8]=[i("已断开")])),_:1,__:[8]}))]),_:1,__:[9]})]),_:1})]),_:1}),u(Q,{class:"mt-5"},{default:d(()=>[u(z,{"label-width":"90px"},{default:d(()=>[u(R,{label:"消息内容"},{default:d(()=>[u(t,{modelValue:m(Y),"onUpdate:modelValue":a[1]||(a[1]=e=>p(Y)?Y.value=e:null),type:"textarea"},null,8,["modelValue"])]),_:1}),u(R,null,{default:d(()=>[u(l,{type:"primary",onClick:Z},{default:d(()=>a[10]||(a[10]=[i("发送广播")])),_:1,__:[10]})]),_:1})]),_:1})]),_:1}),u(Q,{class:"mt-5"},{default:d(()=>[u(z,{"label-width":"90px"},{default:d(()=>[u(R,{label:"消息内容"},{default:d(()=>[u(t,{modelValue:m(G),"onUpdate:modelValue":a[2]||(a[2]=e=>p(G)?G.value=e:null),type:"textarea"},null,8,["modelValue"])]),_:1}),u(R,{label:"消息接收人"},{default:d(()=>[u(t,{modelValue:m(J),"onUpdate:modelValue":a[3]||(a[3]=e=>p(J)?J.value=e:null)},null,8,["modelValue"])]),_:1}),u(R,null,{default:d(()=>[u(l,{type:"primary",onClick:$},{default:d(()=>a[11]||(a[11]=[i("发送点对点消息")])),_:1,__:[11]})]),_:1})]),_:1})]),_:1})]),_:1}),u(n,{span:12},{default:d(()=>[u(Q,null,{default:d(()=>[_("div",q,[(o(!0),r(f,null,v(m(W),(e,a)=>(o(),r("div",{key:a,class:y(["tip"===e.type?"system-notice":"chat-message",{"chat-message--sent":e.sender===m(S).userInfo.username,"chat-message--received":e.sender!==m(S).userInfo.username}])},["tip"!=e.type?(o(),r("div",A,[_("div",{class:y({"chat-message__sender":e.sender===m(S).userInfo.username,"chat-message__receiver":e.sender!==m(S).userInfo.username})},j(e.sender),3),_("div",F,j(e.content),1)])):(o(),r("div",H,j(e.content),1))],2))),128))])]),_:1})]),_:1})]),_:1})])}}}),[["__scopeId","data-v-1f7c28c6"]]);export{O as default};
