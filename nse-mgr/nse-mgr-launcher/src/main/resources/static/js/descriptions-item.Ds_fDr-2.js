import{g as e,f as l}from"./vnode.BkZiIFpS.js";import{d as t,J as s,b as a,X as r,a9 as n,M as i,A as o,t as c,z as p,_ as d,g as u,i as b,f as y,C as h,Q as m,R as v,e as f,m as g,O as w,b8 as S,c as k,h as N,n as W,l as $,a1 as x,F as D,y as E,q as A,G as _}from"./index.Ckm1SagX.js";import{a as j}from"./use-form-common-props.BSYTvb6G.js";const z=Symbol("elDescriptions");var C=t({name:"ElDescriptionsCell",props:{cell:{type:Object},tag:{type:String,default:"td"},type:{type:String}},setup:()=>({descriptions:o(z,{})}),render(){var l;const t=e(this.cell),o=((null==(l=this.cell)?void 0:l.dirs)||[]).map(e=>{const{dir:l,arg:t,modifiers:s,value:a}=e;return[l,a,t,s]}),{border:c,direction:p}=this.descriptions,d="vertical"===p,u=()=>{var e,l,s;return(null==(s=null==(l=null==(e=this.cell)?void 0:e.children)?void 0:l.label)?void 0:s.call(l))||t.label},b=()=>{var e,l,t;return null==(t=null==(l=null==(e=this.cell)?void 0:e.children)?void 0:l.default)?void 0:t.call(l)},y=t.span,h=t.rowspan,m=t.align?`is-${t.align}`:"",v=t.labelAlign?`is-${t.labelAlign}`:m,f=t.className,g=t.labelClassName,w="label"===this.type&&(t.labelWidth||this.descriptions.labelWidth)||t.width,S={width:s(w),minWidth:s(t.minWidth)},k=a("descriptions");switch(this.type){case"label":return r(n(this.tag,{style:S,class:[k.e("cell"),k.e("label"),k.is("bordered-label",c),k.is("vertical-label",d),v,g],colSpan:d?y:1,rowspan:d?1:h},u()),o);case"content":return r(n(this.tag,{style:S,class:[k.e("cell"),k.e("content"),k.is("bordered-content",c),k.is("vertical-content",d),m,f],colSpan:d?y:2*y-1,rowspan:d?2*h-1:h},b()),o);default:{const e=u(),l={},a=s(t.labelWidth||this.descriptions.labelWidth);return a&&(l.width=a,l.display="inline-block"),r(n("td",{style:S,class:[k.e("cell"),m],colSpan:y,rowspan:h},[i(e)?void 0:n("span",{style:l,class:[k.e("label"),g]},e),n("span",{class:[k.e("content"),f]},b())]),o)}}}});const I=c({row:{type:p(Array),default:()=>[]}}),O=t({name:"ElDescriptionsRow"});var R=d(t({...O,props:I,setup(e){const l=o(z,{});return(e,t)=>"vertical"===b(l).direction?(y(),u(m,{key:0},[h("tr",null,[(y(!0),u(m,null,v(e.row,(e,l)=>(y(),f(b(C),{key:`tr1-${l}`,cell:e,tag:"th",type:"label"},null,8,["cell"]))),128))]),h("tr",null,[(y(!0),u(m,null,v(e.row,(e,l)=>(y(),f(b(C),{key:`tr2-${l}`,cell:e,tag:"td",type:"content"},null,8,["cell"]))),128))])],64)):(y(),u("tr",{key:1},[(y(!0),u(m,null,v(e.row,(e,t)=>(y(),u(m,{key:`tr3-${t}`},[b(l).border?(y(),u(m,{key:0},[g(b(C),{cell:e,tag:"td",type:"label"},null,8,["cell"]),g(b(C),{cell:e,tag:"td",type:"content"},null,8,["cell"])],64)):(y(),f(b(C),{key:1,cell:e,tag:"td",type:"both"},null,8,["cell"]))],64))),128))]))}}),[["__file","descriptions-row.vue"]]);const q=c({border:Boolean,column:{type:Number,default:3},direction:{type:String,values:["horizontal","vertical"],default:"horizontal"},size:w,title:{type:String,default:""},extra:{type:String,default:""},labelWidth:{type:[String,Number],default:""}}),B="ElDescriptionsItem",F=t({name:"ElDescriptions"});var G=d(t({...F,props:q,setup(e){const t=e,s=a("descriptions"),r=j(),n=S();E(z,t);const i=k(()=>[s.b(),s.m(r.value)]),o=(e,l,t,s=!1)=>(e.props||(e.props={}),l>t&&(e.props.span=t),s&&(e.props.span=l),e),c=()=>{if(!n.default)return[];const e=l(n.default()).filter(e=>{var l;return(null==(l=null==e?void 0:e.type)?void 0:l.name)===B}),s=[];let a=[],r=t.column,i=0;const c=[];return e.forEach((l,n)=>{var p,d,u;const b=(null==(p=l.props)?void 0:p.span)||1,y=(null==(d=l.props)?void 0:d.rowspan)||1,h=s.length;if(c[h]||(c[h]=0),y>1)for(let e=1;e<y;e++)c[u=h+e]||(c[u]=0),c[h+e]++,i++;if(c[h]>0&&(r-=c[h],c[h]=0),n<e.length-1&&(i+=b>r?r:b),n===e.length-1){const e=t.column-i%t.column;return a.push(o(l,e,r,!0)),void s.push(a)}b<r?(r-=b,a.push(l)):(a.push(o(l,b,r)),s.push(a),r=t.column,a=[])}),s};return(e,l)=>(y(),u("div",{class:W(b(i))},[e.title||e.extra||e.$slots.title||e.$slots.extra?(y(),u("div",{key:0,class:W(b(s).e("header"))},[h("div",{class:W(b(s).e("title"))},[$(e.$slots,"title",{},()=>[x(D(e.title),1)])],2),h("div",{class:W(b(s).e("extra"))},[$(e.$slots,"extra",{},()=>[x(D(e.extra),1)])],2)],2)):N("v-if",!0),h("div",{class:W(b(s).e("body"))},[h("table",{class:W([b(s).e("table"),b(s).is("bordered",e.border)])},[h("tbody",null,[(y(!0),u(m,null,v(c(),(e,l)=>(y(),f(R,{key:l,row:e},null,8,["row"]))),128))])],2)],2)],2))}}),[["__file","description.vue"]]);const J=["left","center","right"],M=c({label:{type:String,default:""},span:{type:Number,default:1},rowspan:{type:Number,default:1},width:{type:[String,Number],default:""},minWidth:{type:[String,Number],default:""},labelWidth:{type:[String,Number],default:""},align:{type:String,values:J,default:"left"},labelAlign:{type:String,values:J},className:{type:String,default:""},labelClassName:{type:String,default:""}}),Q=t({name:B,props:M}),X=A(G,{DescriptionsItem:Q}),H=_(Q);export{X as E,H as a};
