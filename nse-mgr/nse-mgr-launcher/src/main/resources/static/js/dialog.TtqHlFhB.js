import{_ as e,d as s,x as o,A as a,c as l,g as t,f as r,C as n,h as i,l as d,n as c,i as f,F as u,m as p,w as b,e as v,D as g,bO as m,E as y,k as C,b8 as h,b as k,r as R,T as w,X as x,W as A,bd as $,Z as _,y as F,q as E}from"./index.Ckm1SagX.js";import{a as I,E as P,b as j}from"./index.BjYFza3j.js";import{F as L,E as M}from"./focus-trap.Bd_uzvDY.js";import{E as T}from"./index.C0OsJ5su.js";import{b as q,c as z,d as B,a as D,u as S}from"./overlay.CXfNA60T.js";import{c as O}from"./refs.biN0GvkM.js";import{u as K}from"./index.BRUQ9gWw.js";const U=Symbol("dialogInjectionKey"),W=s({name:"ElDialogContent"});var X=e(s({...W,props:z,emits:q,setup(e,{expose:s}){const h=e,{t:k}=o(),{Close:R}=m,{dialogRef:w,headerRef:x,bodyId:A,ns:$,style:_}=a(U),{focusTrapRef:F}=a(L),E=l(()=>[$.b(),$.is("fullscreen",h.fullscreen),$.is("draggable",h.draggable),$.is("align-center",h.alignCenter),{[$.m("center")]:h.center}]),P=O(F,w),j=l(()=>h.draggable),M=l(()=>h.overflow),{resetPosition:T,updatePosition:q}=I(w,x,j,M);return s({resetPosition:T,updatePosition:q}),(e,s)=>(r(),t("div",{ref:f(P),class:c(f(E)),style:C(f(_)),tabindex:"-1"},[n("header",{ref_key:"headerRef",ref:x,class:c([f($).e("header"),e.headerClass,{"show-close":e.showClose}])},[d(e.$slots,"header",{},()=>[n("span",{role:"heading","aria-level":e.ariaLevel,class:c(f($).e("title"))},u(e.title),11,["aria-level"])]),e.showClose?(r(),t("button",{key:0,"aria-label":f(k)("el.dialog.close"),class:c(f($).e("headerbtn")),type:"button",onClick:s=>e.$emit("close")},[p(f(y),{class:c(f($).e("close"))},{default:b(()=>[(r(),v(g(e.closeIcon||f(R))))]),_:1},8,["class"])],10,["aria-label","onClick"])):i("v-if",!0)],2),n("div",{id:f(A),class:c([f($).e("body"),e.bodyClass])},[d(e.$slots,"default")],10,["id"]),e.$slots.footer?(r(),t("footer",{key:0,class:c([f($).e("footer"),e.footerClass])},[d(e.$slots,"footer")],2)):i("v-if",!0)],6))}}),[["__file","dialog-content.vue"]]);const Z=s({name:"ElDialog",inheritAttrs:!1});const G=E(e(s({...Z,props:D,emits:B,setup(e,{expose:s}){const o=e,a=h();K({scope:"el-dialog",from:"the title slot",replacement:"the header slot",version:"3.0.0",ref:"https://element-plus.org/en-US/component/dialog.html#slots"},l(()=>!!a.title));const t=k("dialog"),u=R(),g=R(),m=R(),{visible:y,titleId:E,bodyId:I,style:L,overlayDialogStyle:q,rendered:z,zIndex:B,afterEnter:D,afterLeave:O,beforeLeave:W,handleClose:Z,onModalClick:G,onOpenAutoFocus:H,onCloseAutoFocus:J,onCloseRequested:N,onFocusoutPrevented:Q}=S(o,u);F(U,{dialogRef:u,headerRef:g,bodyId:I,ns:t,rendered:z,style:L});const V=j(G),Y=l(()=>o.draggable&&!o.fullscreen);return s({visible:y,dialogContentRef:m,resetPosition:()=>{var e;null==(e=m.value)||e.resetPosition()},handleClose:Z}),(e,s)=>(r(),v(f(T),{to:e.appendTo,disabled:"body"===e.appendTo&&!e.appendToBody},{default:b(()=>[p(w,{name:"dialog-fade",onAfterEnter:f(D),onAfterLeave:f(O),onBeforeLeave:f(W),persisted:""},{default:b(()=>[x(p(f(P),{"custom-mask-event":"",mask:e.modal,"overlay-class":e.modalClass,"z-index":f(B)},{default:b(()=>[n("div",{role:"dialog","aria-modal":"true","aria-label":e.title||void 0,"aria-labelledby":e.title?void 0:f(E),"aria-describedby":f(I),class:c(`${f(t).namespace.value}-overlay-dialog`),style:C(f(q)),onClick:f(V).onClick,onMousedown:f(V).onMousedown,onMouseup:f(V).onMouseup},[p(f(M),{loop:"",trapped:f(y),"focus-start-el":"container",onFocusAfterTrapped:f(H),onFocusAfterReleased:f(J),onFocusoutPrevented:f(Q),onReleaseRequested:f(N)},{default:b(()=>[f(z)?(r(),v(X,A({key:0,ref_key:"dialogContentRef",ref:m},e.$attrs,{center:e.center,"align-center":e.alignCenter,"close-icon":e.closeIcon,draggable:f(Y),overflow:e.overflow,fullscreen:e.fullscreen,"header-class":e.headerClass,"body-class":e.bodyClass,"footer-class":e.footerClass,"show-close":e.showClose,title:e.title,"aria-level":e.headerAriaLevel,onClose:f(Z)}),$({header:b(()=>[e.$slots.title?d(e.$slots,"title",{key:1}):d(e.$slots,"header",{key:0,close:f(Z),titleId:f(E),titleClass:f(t).e("title")})]),default:b(()=>[d(e.$slots,"default")]),_:2},[e.$slots.footer?{name:"footer",fn:b(()=>[d(e.$slots,"footer")])}:void 0]),1040,["center","align-center","close-icon","draggable","overflow","fullscreen","header-class","body-class","footer-class","show-close","title","aria-level","onClose"])):i("v-if",!0)]),_:3},8,["trapped","onFocusAfterTrapped","onFocusAfterReleased","onFocusoutPrevented","onReleaseRequested"])],46,["aria-label","aria-labelledby","aria-describedby","onClick","onMousedown","onMouseup"])]),_:3},8,["mask","overlay-class","z-index"]),[[_,f(y)]])]),_:3},8,["onAfterEnter","onAfterLeave","onBeforeLeave"])]),_:3},8,["to","disabled"]))}}),[["__file","dialog.vue"]]));export{G as E};
