import{d as e,o as t,V as a,r as o,d1 as l,ap as r,g as i,f as n,m as s,w as m,a1 as u,Q as d,R as c,e as p,F as f,C as x,W as h,bp as g}from"./index.Ckm1SagX.js";/* empty css               *//* empty css            */import{E as b}from"./index.CbYeWxT8.js";import{E as v}from"./index.BPj3iklg.js";import"./index.BRUQ9gWw.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";const w={class:"app-container"},k={style:{padding:"20px"}},y=e({__name:"index",setup(e){const y=[{label:"管理员",value:"admin"},{label:"用户",value:"user"},{label:"访客",value:"guest"}];t(()=>{setTimeout(()=>{j.formConfig.items.forEach(e=>{"roles"===e.field&&(e.itemRender.props.options=y)})},500)});const C=o(),j=a({autoResize:!0,showFooter:!0,footerMethod:({columns:e,data:t})=>[e.map((e,a)=>0===a||void 0===e.field?"":"status"===e.field?`启用：${t.reduce((e,t)=>e+(t.status?1:0),0)}条`:"-")],columns:[{type:"checkbox",width:60},{type:"expand",width:60,slots:{content:"column-expand"}},{type:"seq",width:60},{field:"id",title:"ID",visible:!1},{field:"username",title:"用户名"},{field:"roles",title:"角色",slots:{default:"column-roles"}},{field:"phone",title:"手机号"},{field:"email",title:"邮箱"},{field:"status",title:"状态",sortable:!0,filters:[{label:"启用",value:!0},{label:"禁用",value:!1}],filterMultiple:!1,formatter:({cellValue:e})=>!0===e?"启用":"禁用"},{field:"createTime",title:"创建时间",sortable:!0},{title:"操作",width:"150px",fixed:"right",showOverflow:!1,slots:{default:"column-operate"}}],columnConfig:{resizable:!0},customConfig:{checkMethod:({column:e})=>!["username"].includes(e.field)},checkboxConfig:{},expandConfig:{accordion:!0},rowConfig:{keyField:"id",isCurrent:!0},formConfig:{items:[{span:4,field:"username",title:"用户名",titlePrefix:{useHTML:!0,content:'点击链接：<a class="link" href="https://vxetable.cn" target="_blank">vxe-table官网</a>',icon:"vxe-icon-question-circle-fill"},itemRender:{name:"VxeInput",props:{type:"text",clearable:!0,placeholder:"请输入用户名"}}},{span:4,field:"roles",title:"角色",folding:!0,itemRender:{name:"VxeSelect",props:{multiple:!0,multiCharOverflow:-1,filterable:!0,clearable:!0,options:[],placeholder:"请选择角色"}}},{collapseNode:!0,itemRender:{name:"VxeButtonGroup",options:[{type:"submit",status:"primary",icon:"vxe-icon-search",content:"搜索"},{type:"reset",icon:"vxe-icon-refresh",content:"重置"}]}}]},toolbarConfig:{import:!0,export:!0,print:!0,refresh:!0,zoom:!0,custom:!0,slots:{buttons:"toolbar-btns"}},importConfig:{},exportConfig:{columns:[{field:"phone"},{field:"email"},{field:"status"},{field:"createTime"}]},printConfig:{},filterConfig:{remote:!1},sortConfig:{remote:!1,multiple:!1,chronological:!0},pagerConfig:{enabled:!0,pageSize:10},proxyConfig:{autoLoad:!0,seq:!0,form:!0,filter:!0,sort:!0,response:{result:"result",total:"total"},ajax:{query:({page:{currentPage:e,pageSize:t},form:a,filters:o,sort:l,sorts:r})=>new Promise(a=>{setTimeout(()=>{const o=[{username:"Richard Clark",roles:"editor",phone:"18185826431",email:"<EMAIL>",status:!0,createTime:"2010-04-17 12:39:20",id:0xb3db2e429149880},{username:"Robert Garcia",roles:"admin",phone:"18125716043",email:"<EMAIL>",status:!1,createTime:"2020-01-02 11:51:58",id:0x1cdda7eaf398530},{username:"Thomas Moore",roles:"admin",phone:"18106622048",email:"<EMAIL>",status:!0,createTime:"1983-10-12 10:06:41",id:0x5d423f47b7c9440},{username:"Dorothy Lewis",roles:"admin",phone:"13321357284",email:"<EMAIL>",status:!0,createTime:"1970-03-03 00:26:45",id:0x214e86388b81e60},{username:"George Rodriguez",roles:"admin",phone:"18158641167",email:"<EMAIL>",status:!0,createTime:"1988-03-16 14:46:26",id:0x87727f308afc980},{username:"Angela Jackson",roles:"admin",phone:"19810721230",email:"<EMAIL>",status:!0,createTime:"2006-09-26 12:53:37",id:0x4db735337fc37c0},{username:"James Walker",roles:"admin",phone:"18123903251",email:"<EMAIL>",status:!0,createTime:"1981-01-19 12:51:34",id:0x1cdda7e147eeb00},{username:"Paul Garcia",roles:"admin",phone:"18617930381",email:"<EMAIL>",status:!1,createTime:"2009-12-04 20:40:57",id:0x713e27aa56c8980},{username:"Jeffrey Miller",roles:"admin",phone:"18145245413",email:"<EMAIL>",status:!1,createTime:"1991-04-01 05:16:52",id:0x494656ea59b37c0},{username:"Donna Lewis",roles:"editor",phone:"19839835537",email:"<EMAIL>",status:!0,createTime:"1987-11-29 21:47:37",id:0x8e1bcc9cead4180},{username:"Jennifer Smith",roles:"editor",phone:"18145245413",email:"<EMAIL>",status:!0,createTime:"1991-04-01 05:16:52",id:64000019700523e4}];a({result:o.slice((e-1)*t,e*t),total:o.length})},500)})}}}),_={formReset(){}},T=o(),q=a({title:"",maskClosable:!0,escClosable:!0,beforeHideMethod:()=>{var e;return null==(e=R.value)||e.clearValidate(),Promise.resolve()}}),R=o(),M=a({span:24,titleWidth:100,data:{username:"",password:""},items:[{field:"username",title:"用户名",itemRender:{name:"VxeInput",props:{placeholder:"请输入"}}},{field:"password",title:"密码",itemRender:{name:"VxeInput",props:{placeholder:"请输入"}}},{align:"right",itemRender:{name:"$buttons",children:[{props:{content:"取消"},events:{click:()=>{var e;return null==(e=T.value)?void 0:e.close()}}},{props:{type:"submit",content:"确定",status:"primary"},events:{click:()=>S.onSubmitForm()}}]}}],rules:{username:[{required:!0,validator:({itemValue:e})=>{switch(!0){case!e:return new Error("请输入");case!e.trim():return new Error("空格无效")}}}],password:[{required:!0,validator:({itemValue:e})=>{switch(!0){case!e:return new Error("请输入");case!e.trim():return new Error("空格无效")}}}]}}),S={commitQuery:()=>{var e;return null==(e=C.value)?void 0:e.commitProxy("query")},onShowModal:e=>{var t;q.title=e?"修改用户":"新增用户",null==(t=T.value)||t.open()},onSubmitForm:()=>{},onDelete:e=>{var t;let a=[];if(void 0===e){const e=null==(t=C.value)?void 0:t.getCheckboxRecords();if(!e||0===e.length)return void l.modal.message({content:"请至少选择一条数据",status:"warning"});a=e.map(e=>e.id)}else a=[e.id];l.modal.confirm("确定要删除吗？").then(e=>{})}};return(e,t)=>{const a=r("vxe-button"),o=v,l=b,y=r("vxe-grid"),V=r("vxe-form"),z=r("vxe-modal");return n(),i("div",w,[s(y,h({ref_key:"xGrid",ref:C},j,g(_)),{"toolbar-btns":m(()=>[s(a,{status:"primary",icon:"vxe-icon-add",onClick:t[0]||(t[0]=e=>S.onShowModal())},{default:m(()=>t[2]||(t[2]=[u(" 新增用户 ")])),_:1,__:[2]}),s(a,{status:"danger",icon:"vxe-icon-delete",onClick:t[1]||(t[1]=e=>S.onDelete())},{default:m(()=>t[3]||(t[3]=[u(" 批量删除 ")])),_:1,__:[3]})]),"column-expand":m(({row:e})=>[x("div",k,[x("ul",null,[x("li",null,[t[4]||(t[4]=x("span",null,"ID：",-1)),x("span",null,f(e.id),1)]),x("li",null,[t[5]||(t[5]=x("span",null,"UserName：",-1)),x("span",null,f(e.username),1)]),x("li",null,[t[6]||(t[6]=x("span",null,"CreateTime：",-1)),x("span",null,f(e.createTime),1)])])])]),"column-roles":m(({row:e,column:t})=>[(n(!0),i(d,null,c(e[t.field].split(","),(e,t)=>(n(),p(o,{key:t,type:"admin"===e?"primary":"warning",effect:"plain"},{default:m(()=>[u(f(e),1)]),_:2},1032,["type"]))),128))]),"column-operate":m(({row:e})=>[s(l,{link:"",type:"primary",onClick:t=>S.onShowModal(e)},{default:m(()=>t[7]||(t[7]=[u("修改")])),_:2,__:[7]},1032,["onClick"]),s(l,{link:"",type:"danger",onClick:t=>S.onDelete(e)},{default:m(()=>t[8]||(t[8]=[u("删除")])),_:2,__:[8]},1032,["onClick"])]),_:1},16),s(z,h({ref_key:"xModal",ref:T},q),{default:m(()=>[s(V,h({ref_key:"xForm",ref:R},M),null,16)]),_:1},16)])}}});export{y as default};
