import{aw as e}from"./index.Ckm1SagX.js";const t="/api/course",o={getCoursePage:o=>e({url:`${t}/page`,method:"get",params:o}),createCourse:o=>e({url:t,method:"post",data:o}),getCourseDetail:o=>e({url:`${t}/${o}`,method:"get"}),deleteCourse:o=>e({url:`${t}/${o}`,method:"delete"}),deleteCourseBatch:o=>e({url:`${t}/batch`,method:"delete",data:o}),getCourseStudentPage:o=>e({url:`${t}/student/page`,method:"get",params:o}),getCourseStudent:o=>e({url:`${t}/student/${o}`,method:"get"}),addCourseStudent:o=>e({url:`${t}/student`,method:"post",data:o}),deleteCourseStudent:o=>e({url:`${t}/student`,method:"delete",data:o}),startCourseCheck:o=>e({url:`${t}/startCheck/${o}`,method:"get"}),startCourse:o=>e({url:`${t}/start/${o}`,method:"post"}),endCourse:o=>e({url:`${t}/end/${o}`,method:"post"}),assignHomework:(o,r)=>e({url:`${t}/assignHomework`,method:"post",data:{courseId:o,repoId:r}})};export{o as T};
