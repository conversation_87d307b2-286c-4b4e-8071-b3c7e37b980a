import{d as e,r as t,V as l,aT as a,c as o,ap as r,g as n,f as i,k as s,i as p,m as u,W as d,w as m,C as c,X as f,e as v,h as g,Q as b,R as _,a0 as y,a1 as j,n as h,l as k,F as x,E as w,aP as V,aA as C,bv as U}from"./index.Ckm1SagX.js";/* empty css                */import"./popper.DpZVcW1M.js";import{E}from"./popover.OzOh_E3J.js";import{_ as S}from"./index.r55iLQne.js";import{E as A,a as N}from"./table-column.DQa6-hu-.js";import"./checkbox.CyAsOZKA.js";/* empty css                */import"./scrollbar.6rbryiG1.js";/* empty css            */import{E as R,a as I}from"./form-item.CUMILu98.js";/* empty css               */import{E as z}from"./date-picker.CB50TImD.js";/* empty css              */import{E as $,a as D}from"./select.DHkh6uhw.js";import"./tree.BMRqnaD4.js";/* empty css             */import{E as Y}from"./tree-select.CTL8lHB1.js";import{E as K}from"./index.4JfkAhur.js";import{E as M}from"./index.CbYeWxT8.js";import{v as P}from"./directive.C7vihscI.js";import{_ as T}from"./_plugin-vue_export-helper.BCo6x5W8.js";import{_ as F}from"./DictLabel.vue_vue_type_script_setup_true_lang.BOw0aI_B.js";/* empty css             */import{E as q}from"./index.CMOQuMWt.js";import{E as L}from"./index.BPj3iklg.js";import"./index.BLy3nyPI.js";import"./_arrayPush.Dbwejsrt.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./aria.C1IWO_Rd.js";import"./index.Dh_vcBr5.js";import"./focus-trap.Bd_uzvDY.js";import"./use-form-common-props.BSYTvb6G.js";import"./dropdown.Dp4e0zMH.js";import"./pagination.TL6aFrlm.js";import"./isEqual.CZKKciWh.js";import"./_Uint8Array.BCiDNJWl.js";import"./event.BwRzfsZt.js";import"./index.BRUQ9gWw.js";import"./_initCloneObject.BsGr3vVr.js";import"./isPlainObject.Ct3iyI-U.js";import"./_baseIteratee.PZHdcgYb.js";import"./castArray.Chmjnshw.js";import"./debounce.YgIwzEIs.js";import"./index.B0geSHq7.js";import"./_baseClone.ByRc02qR.js";import"./index.COAWgEf6.js";import"./index.DJHzyRe5.js";import"./index.Byj-i824.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./scroll.XdyICIdv.js";import"./vnode.BkZiIFpS.js";import"./index.Cg5eTZHL.js";import"./index.CqmGTqol.js";const O={class:"feedback"},W=T(e({__name:"index",props:{selectConfig:{},text:{default:""}},emits:["confirmClick"],setup(e,{emit:C}){const U=e,T=C,F=U.selectConfig.pk??"id",q=!0===U.selectConfig.multiple,L=U.selectConfig.width??"100%",W=U.selectConfig.placeholder??"请选择",X=t(!1),B=t(!1),G=t(0),H=t([]),J=l({pageNumber:1,pageSize:10}),Q=t(),Z=t(L);a(Q,e=>{Z.value=`${e[0].contentRect.width}px`});const ee=t();for(const t of U.selectConfig.formItems)J[t.prop]=t.initialValue??"";function te(){var e;null==(e=ee.value)||e.resetFields(),ae(!0)}function le(){ae(!0)}function ae(e=!1){B.value=!0,e&&(J.pageNumber=1,J.pageSize=10),U.selectConfig.indexAction(J).then(e=>{G.value=e.total,H.value=e.rows}).finally(()=>{B.value=!1})}const oe=t();for(const t of U.selectConfig.tableColumns)if("selection"===t.type){t.reserveSelection=!0;break}const re=t([]),ne=o(()=>re.value.length>0?`已选(${re.value.length})`:"确 定");function ie(e){var t,l,a;q||0===e.length?re.value=e:(re.value=[e[e.length-1]],null==(t=oe.value)||t.clearSelection(),null==(l=oe.value)||l.toggleRowSelection(re.value[0],!0),null==(a=oe.value)||a.setCurrentRow(re.value[0]))}function se(e){q&&(re.value=e)}function pe(){ae()}const ue=t(!1);function de(){!1===ue.value&&(ue.value=!0,ae())}function me(){0!==re.value.length?(X.value=!1,T("confirmClick",re.value)):V.error("请选择数据")}function ce(){var e;null==(e=oe.value)||e.clearSelection(),re.value=[]}function fe(){X.value=!1}const ve=t();return(e,t)=>{const l=r("ArrowDown"),a=w,o=K,V=D,C=$,U=Y,T=z,ae=I,re=M,ue=R,ge=N,be=A,_e=S,ye=E,je=P;return i(),n("div",{ref_key:"tableSelectRef",ref:Q,style:s("width:"+p(L))},[u(ye,d({visible:X.value,width:Z.value,placement:"bottom-end"},e.selectConfig.popover,{onShow:de}),{reference:m(()=>[c("div",{onClick:t[0]||(t[0]=e=>X.value=!X.value)},[k(e.$slots,"default",{},()=>[u(o,{class:"reference","model-value":e.text,readonly:!0,placeholder:p(W)},{suffix:m(()=>[u(a,{style:s({transform:X.value?"rotate(180deg)":"rotate(0)",transition:"transform .5s"})},{default:m(()=>[u(l)]),_:1},8,["style"])]),_:1},8,["model-value","placeholder"])],!0)])]),default:m(()=>[c("div",{ref_key:"popoverContentRef",ref:ve},[u(ue,{ref_key:"formRef",ref:ee,model:J,inline:!0},{default:m(()=>[(i(!0),n(b,null,_(e.selectConfig.formItems,e=>(i(),v(ae,{key:e.prop,label:e.label,prop:e.prop},{default:m(()=>{var t,l;return["input"===e.type?(i(),n(b,{key:0},["number"===(null==(t=e.attrs)?void 0:t.type)?(i(),v(o,d({key:0,modelValue:J[e.prop],"onUpdate:modelValue":t=>J[e.prop]=t,modelModifiers:{number:!0}},{ref_for:!0},e.attrs,{onKeyup:y(le,["enter"])}),null,16,["modelValue","onUpdate:modelValue"])):(i(),v(o,d({key:1,modelValue:J[e.prop],"onUpdate:modelValue":t=>J[e.prop]=t},{ref_for:!0},e.attrs,{onKeyup:y(le,["enter"])}),null,16,["modelValue","onUpdate:modelValue"]))],64)):"select"===e.type?(i(),v(C,d({key:1,modelValue:J[e.prop],"onUpdate:modelValue":t=>J[e.prop]=t},{ref_for:!0},e.attrs),{default:m(()=>[(i(!0),n(b,null,_(e.options,e=>(i(),v(V,{key:e.value,label:e.label,value:e.value},null,8,["label","value"]))),128))]),_:2},1040,["modelValue","onUpdate:modelValue"])):"tree-select"===e.type?(i(),v(U,d({key:2,modelValue:J[e.prop],"onUpdate:modelValue":t=>J[e.prop]=t},{ref_for:!0},e.attrs),null,16,["modelValue","onUpdate:modelValue"])):"date-picker"===e.type?(i(),v(T,d({key:3,modelValue:J[e.prop],"onUpdate:modelValue":t=>J[e.prop]=t},{ref_for:!0},e.attrs),null,16,["modelValue","onUpdate:modelValue"])):(i(),n(b,{key:4},["number"===(null==(l=e.attrs)?void 0:l.type)?(i(),v(o,d({key:0,modelValue:J[e.prop],"onUpdate:modelValue":t=>J[e.prop]=t,modelModifiers:{number:!0}},{ref_for:!0},e.attrs,{onKeyup:y(le,["enter"])}),null,16,["modelValue","onUpdate:modelValue"])):(i(),v(o,d({key:1,modelValue:J[e.prop],"onUpdate:modelValue":t=>J[e.prop]=t},{ref_for:!0},e.attrs,{onKeyup:y(le,["enter"])}),null,16,["modelValue","onUpdate:modelValue"]))],64))]}),_:2},1032,["label","prop"]))),128)),u(ae,null,{default:m(()=>[u(re,{type:"primary",icon:"search",onClick:le},{default:m(()=>t[4]||(t[4]=[j("搜索")])),_:1,__:[4]}),u(re,{icon:"refresh",onClick:te},{default:m(()=>t[5]||(t[5]=[j("重置")])),_:1,__:[5]})]),_:1})]),_:1},8,["model"]),f((i(),v(be,{ref_key:"tableRef",ref:oe,data:H.value,border:!0,"max-height":250,"row-key":p(F),"highlight-current-row":!0,class:h({radio:!q}),onSelect:ie,onSelectAll:se},{default:m(()=>[(i(!0),n(b,null,_(e.selectConfig.tableColumns,t=>(i(),n(b,{key:t.prop},["custom"===t.templet?(i(),v(ge,d({key:0,ref_for:!0},t),{default:m(l=>[k(e.$slots,t.slotName??t.prop,d({prop:t.prop},{ref_for:!0},l),void 0,!0)]),_:2},1040)):(i(),v(ge,d({key:1,ref_for:!0},t),null,16))],64))),128))]),_:3},8,["data","row-key","class"])),[[je,B.value]]),G.value>0?(i(),v(_e,{key:0,total:G.value,"onUpdate:total":t[1]||(t[1]=e=>G.value=e),page:J.pageNumber,"onUpdate:page":t[2]||(t[2]=e=>J.pageNumber=e),limit:J.pageSize,"onUpdate:limit":t[3]||(t[3]=e=>J.pageSize=e),onPagination:pe},null,8,["total","page","limit"])):g("",!0),c("div",O,[u(re,{type:"primary",size:"small",onClick:me},{default:m(()=>[j(x(ne.value),1)]),_:1}),u(re,{size:"small",onClick:ce},{default:m(()=>t[6]||(t[6]=[j("清 空")])),_:1,__:[6]}),u(re,{size:"small",onClick:fe},{default:m(()=>t[7]||(t[7]=[j("关 闭")])),_:1,__:[7]})])],512)]),_:3},16,["visible","width"])],4)}}}),[["__scopeId","data-v-82810dae"]]),X={pk:"id",width:"70%",placeholder:"请选择用户",formItems:[{type:"input",label:"关键字",prop:"keywords",attrs:{placeholder:"用户名/昵称/手机号",clearable:!0,style:{width:"200px"}}},{type:"tree-select",label:"部门",prop:"deptId",attrs:{placeholder:"请选择",data:[{value:1,label:"有来技术",children:[{value:2,label:"研发部门"},{value:3,label:"测试部门"}]}],filterable:!0,"check-strictly":!0,"render-after-expand":!1,clearable:!0,style:{width:"150px"}}},{type:"select",label:"状态",prop:"status",attrs:{placeholder:"全部",clearable:!0,style:{width:"100px"}},options:[{label:"启用",value:1},{label:"禁用",value:0}]},{type:"date-picker",label:"创建时间",prop:"createAt",attrs:{type:"daterange","range-separator":"~","start-placeholder":"开始时间","end-placeholder":"截止时间","value-format":"YYYY-MM-DD",style:{width:"240px"}}}],indexAction(e){if("createAt"in e){const t=e.createAt;(null==t?void 0:t.length)>1&&(e.startTime=t[0],e.endTime=t[1]),delete e.createAt}return C.getPage(e)},tableColumns:[{type:"selection",width:50,align:"center"},{label:"编号",align:"center",prop:"id",width:100},{label:"用户名",align:"center",prop:"username"},{label:"用户昵称",align:"center",prop:"nickname",width:120},{label:"性别",align:"center",prop:"gender",width:100,templet:"custom",slotName:"gender"},{label:"部门",align:"center",prop:"deptName",width:120},{label:"手机号码",align:"center",prop:"mobile",width:120},{label:"状态",align:"center",prop:"status",templet:"custom",slotName:"status"},{label:"创建时间",align:"center",prop:"createTime",width:180}]},B={class:"app-container"},G=e({__name:"index",setup(e){const l=U(),a=t();function r(e){a.value=e[0]}const s=o(()=>{var e;const t=null==(e=l.getDictItems("gender").find(e=>{var t;return e.value==(null==(t=a.value)?void 0:t.gender)}))?void 0:e.label;return a.value?`${a.value.username} - ${t} - ${a.value.deptName}`:""});return(e,t)=>{const l=q,a=L,o=F,d=W;return i(),n("div",B,[u(l,{href:"https://gitee.com/youlaiorg/vue3-element-admin/blob/master/src/views/demo/table-select/index.vue",type:"primary",target:"_blank",class:"mb-10"},{default:m(()=>t[0]||(t[0]=[j(" 示例源码 请点击>>>> ")])),_:1,__:[0]}),u(d,{text:p(s),"select-config":p(X),onConfirmClick:r},{status:m(e=>[u(a,{type:1==e.row[e.prop]?"success":"info"},{default:m(()=>[j(x(1==e.row[e.prop]?"启用":"禁用"),1)]),_:2},1032,["type"])]),gender:m(e=>[u(o,{modelValue:e.row.gender,"onUpdate:modelValue":t=>e.row.gender=t,code:"gender"},null,8,["modelValue","onUpdate:modelValue"])]),_:1},8,["text","select-config"])])}}});export{G as default};
