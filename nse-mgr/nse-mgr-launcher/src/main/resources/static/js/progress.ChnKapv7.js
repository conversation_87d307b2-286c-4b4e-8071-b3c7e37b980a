import{t as e,z as t,_ as a,d as s,b as r,c as n,cc as o,cd as i,be as l,aO as c,$ as u,b6 as d,L as p,g as f,f as h,h as g,n as y,i as k,C as v,k as b,l as x,F as $,e as m,w,D as F,E as N,q as B}from"./index.Ckm1SagX.js";const D=e({type:{type:String,default:"line",values:["line","circle","dashboard"]},percentage:{type:Number,default:0,validator:e=>e>=0&&e<=100},status:{type:String,default:"",values:["","success","exception","warning"]},indeterminate:Boolean,duration:{type:Number,default:3},strokeWidth:{type:Number,default:6},strokeLinecap:{type:t(String),default:"round"},textInside:Boolean,width:{type:Number,default:126},showText:{type:Boolean,default:!0},color:{type:t([String,Array,Function]),default:""},striped:Boolean,stripedFlow:Boolean,format:{type:t(Function),default:e=>`${e}%`}}),I=s({name:"ElProgress"});const S=B(a(s({...I,props:D,setup(e){const t=e,a={success:"#13ce66",exception:"#ff4949",warning:"#e6a23c",default:"#20a0ff"},s=r("progress"),B=n(()=>{const e={width:`${t.percentage}%`,animationDuration:`${t.duration}s`},a=j(t.percentage);return a.includes("gradient")?e.background=a:e.backgroundColor=a,e}),D=n(()=>(t.strokeWidth/t.width*100).toFixed(1)),I=n(()=>["circle","dashboard"].includes(t.type)?Number.parseInt(""+(50-Number.parseFloat(D.value)/2),10):0),S=n(()=>{const e=I.value,a="dashboard"===t.type;return`\n          M 50 50\n          m 0 ${a?"":"-"}${e}\n          a ${e} ${e} 0 1 1 0 ${a?"-":""}${2*e}\n          a ${e} ${e} 0 1 1 0 ${a?"":"-"}${2*e}\n          `}),T=n(()=>2*Math.PI*I.value),L=n(()=>"dashboard"===t.type?.75:1),W=n(()=>`${-1*T.value*(1-L.value)/2}px`),_=n(()=>({strokeDasharray:`${T.value*L.value}px, ${T.value}px`,strokeDashoffset:W.value})),E=n(()=>({strokeDasharray:`${T.value*L.value*(t.percentage/100)}px, ${T.value}px`,strokeDashoffset:W.value,transition:"stroke-dasharray 0.6s ease 0s, stroke 0.6s ease, opacity ease 0.6s"})),z=n(()=>{let e;return e=t.color?j(t.percentage):a[t.status]||a.default,e}),C=n(()=>"warning"===t.status?o:"line"===t.type?"success"===t.status?i:l:"success"===t.status?c:u),M=n(()=>"line"===t.type?12+.4*t.strokeWidth:.111111*t.width+2),P=n(()=>t.format(t.percentage));const j=e=>{var a;const{color:s}=t;if(d(s))return s(e);if(p(s))return s;{const t=function(e){const t=100/e.length;return e.map((e,a)=>p(e)?{color:e,percentage:(a+1)*t}:e).sort((e,t)=>e.percentage-t.percentage)}(s);for(const a of t)if(a.percentage>e)return a.color;return null==(a=t[t.length-1])?void 0:a.color}};return(e,t)=>(h(),f("div",{class:y([k(s).b(),k(s).m(e.type),k(s).is(e.status),{[k(s).m("without-text")]:!e.showText,[k(s).m("text-inside")]:e.textInside}]),role:"progressbar","aria-valuenow":e.percentage,"aria-valuemin":"0","aria-valuemax":"100"},["line"===e.type?(h(),f("div",{key:0,class:y(k(s).b("bar"))},[v("div",{class:y(k(s).be("bar","outer")),style:b({height:`${e.strokeWidth}px`})},[v("div",{class:y([k(s).be("bar","inner"),{[k(s).bem("bar","inner","indeterminate")]:e.indeterminate},{[k(s).bem("bar","inner","striped")]:e.striped},{[k(s).bem("bar","inner","striped-flow")]:e.stripedFlow}]),style:b(k(B))},[(e.showText||e.$slots.default)&&e.textInside?(h(),f("div",{key:0,class:y(k(s).be("bar","innerText"))},[x(e.$slots,"default",{percentage:e.percentage},()=>[v("span",null,$(k(P)),1)])],2)):g("v-if",!0)],6)],6)],2)):(h(),f("div",{key:1,class:y(k(s).b("circle")),style:b({height:`${e.width}px`,width:`${e.width}px`})},[(h(),f("svg",{viewBox:"0 0 100 100"},[v("path",{class:y(k(s).be("circle","track")),d:k(S),stroke:`var(${k(s).cssVarName("fill-color-light")}, #e5e9f2)`,"stroke-linecap":e.strokeLinecap,"stroke-width":k(D),fill:"none",style:b(k(_))},null,14,["d","stroke","stroke-linecap","stroke-width"]),v("path",{class:y(k(s).be("circle","path")),d:k(S),stroke:k(z),fill:"none",opacity:e.percentage?1:0,"stroke-linecap":e.strokeLinecap,"stroke-width":k(D),style:b(k(E))},null,14,["d","stroke","opacity","stroke-linecap","stroke-width"])]))],6)),!e.showText&&!e.$slots.default||e.textInside?g("v-if",!0):(h(),f("div",{key:2,class:y(k(s).e("text")),style:b({fontSize:`${k(M)}px`})},[x(e.$slots,"default",{percentage:e.percentage},()=>[e.status?(h(),m(k(N),{key:1},{default:w(()=>[(h(),m(F(k(C))))]),_:1})):(h(),f("span",{key:0},$(k(P)),1))])],6))],10,["aria-valuenow"]))}}),[["__file","progress.vue"]]));export{S as E};
