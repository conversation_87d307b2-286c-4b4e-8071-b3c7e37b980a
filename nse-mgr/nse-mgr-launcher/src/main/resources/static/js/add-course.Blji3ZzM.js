import{d as e,aF as a,r as s,am as l,aD as r,V as o,o as i,ay as t,ap as p,aQ as u,g as d,f as m,C as n,X as c,Z as v,m as x,w as g,Q as f,R as j,e as _,a1 as b,E as y,F as h,a2 as F}from"./index.Ckm1SagX.js";import{a as k,E as P}from"./form-item.CUMILu98.js";import{E as C,g as w}from"./upload.CnbKCtkP.js";import"./progress.ChnKapv7.js";/* empty css               *//* empty css              *//* empty css            */import{E as V,a as T}from"./select.DHkh6uhw.js";import"./scrollbar.6rbryiG1.js";import"./popper.DpZVcW1M.js";import{C as q}from"./course.api.CXNBFdV0.js";import{E}from"./index.4JfkAhur.js";import{E as M}from"./index.CbYeWxT8.js";import{_ as N}from"./_plugin-vue_export-helper.BCo6x5W8.js";import"./use-form-common-props.BSYTvb6G.js";import"./index.Dh_vcBr5.js";import"./castArray.Chmjnshw.js";import"./aria.C1IWO_Rd.js";import"./_baseClone.ByRc02qR.js";import"./_Uint8Array.BCiDNJWl.js";import"./_arrayPush.Dbwejsrt.js";import"./_initCloneObject.BsGr3vVr.js";import"./isEqual.CZKKciWh.js";import"./index.BPj3iklg.js";import"./token.DWNpOE8r.js";import"./strings.By8NVWWL.js";import"./index.Byj-i824.js";import"./event.BwRzfsZt.js";import"./scroll.XdyICIdv.js";import"./debounce.YgIwzEIs.js";import"./_baseIteratee.PZHdcgYb.js";import"./index.BLy3nyPI.js";import"./index.B0geSHq7.js";import"./vnode.BkZiIFpS.js";import"./index.C0OsJ5su.js";import"./index.Cn1QDWeG.js";import"./focus-trap.Bd_uzvDY.js";import"./index.DJHzyRe5.js";import"./index.BRUQ9gWw.js";const U={class:"add-course-container"},R={class:"upload-section"},I={class:"upload-section"},A={class:"form-actions"},S={class:"success-message"},D={class:"success-icon"},B={class:"success-details"},L={class:"detail-item"},O={class:"value"},Q={class:"detail-item"},X={class:"value"},Z={class:"detail-item"},G={class:"value"},H={class:"detail-item"},K={class:"detail-item"},Y={class:"value"},$=N(e({__name:"add-course",setup(e){const N=a(),$=s(!1),z=s({}),J=l(),W=r(),ee=s(),ae=s(),se=s(),le=o({coursePkg:"",expName:"",expType:"",expProjectFile:void 0,expManualFile:void 0}),re=s([]),oe=s(!1),ie=e=>{oe.value=!0,q.getCoursePkgList(e).then(e=>{re.value=e}).finally(()=>oe.value=!1)},te=s(),pe=s(),ue={coursePkg:[{required:!0,message:"请输入课程包名称",trigger:"blur"},{min:2,max:100,message:"课程包名称长度在 2 到 100 个字符",trigger:"blur"}],expName:[{required:!0,message:"请输入实验名称",trigger:"blur"},{min:2,max:100,message:"实验名称长度在 2 到 100 个字符",trigger:"blur"}],expType:[{required:!0,message:"请输入实验名称",trigger:"blur"},{min:2,max:100,message:"实验名称长度在 2 到 100 个字符",trigger:"blur"}],expManualFile:[{required:!0,message:"请上传文件",trigger:"change"}],expProjectFile:[{required:!0,message:"请上传文件",trigger:"change"}]};i(async()=>{const e=J.query.id;e&&q.getCourseDetail(e).then(e=>{Object.assign(le,e)});const{roles:a}=t().userInfo;a.includes("super_admin")||(le.expType="学校课程库")});const de=e=>{te.value=e.raw,le.expManualFile=e.raw,ee.value.validateField("expManualFile")},me=e=>{pe.value=e.raw,le.expProjectFile=e.raw,ee.value.validateField("expProjectFile")},ne=()=>{le.expManualFile=void 0},ce=()=>{le.expProjectFile=void 0},ve=e=>{ae.value.clearFiles();const a=e[0];a.uid=w(),ae.value.handleStart(a)},xe=e=>{se.value.clearFiles();const a=e[0];a.uid=w(),se.value.handleStart(a)},ge=async()=>{const e=ee.value;e&&e.validate(e=>{e&&q.saveCourse(le).then(e=>{$.value=!0,z.value=e})})},fe=e=>{if(N.closeCurrentView(),e){const e={name:"Course"};N.delCachedView(e)}F(()=>{W.push("/course/index")})};return(e,a)=>{const s=T,l=V,r=k,o=E,i=M,t=C,F=P,w=p("ArrowLeftBold"),q=y,N=p("SuccessFilled"),J=u("hasRole");return m(),d("div",U,[a[19]||(a[19]=n("div",{class:"add-course-title"},"新建课程实验",-1)),c(x(F,{ref_key:"courseFormRef",ref:ee,class:"add-form",model:le,rules:ue,"label-width":"120px"},{default:g(()=>[x(r,{label:"课程包名称",prop:"coursePkg"},{default:g(()=>[x(l,{modelValue:le.coursePkg,"onUpdate:modelValue":a[0]||(a[0]=e=>le.coursePkg=e),filterable:"","allow-create":"",remote:"","reserve-keyword":"",placeholder:"请输入课程包名称","remote-method":ie,loading:oe.value},{default:g(()=>[(m(!0),d(f,null,j(re.value,e=>(m(),_(s,{key:e.id,label:e.coursePkg+" ("+e.expType+")",value:e.coursePkg},null,8,["label","value"]))),128))]),_:1},8,["modelValue","loading"])]),_:1}),x(r,{label:"实验名称",prop:"expName"},{default:g(()=>[x(o,{modelValue:le.expName,"onUpdate:modelValue":a[1]||(a[1]=e=>le.expName=e),placeholder:"请输入实验名称",maxlength:"100"},null,8,["modelValue"])]),_:1}),c((m(),_(r,{label:"实验类型",prop:"expType"},{default:g(()=>[x(l,{modelValue:le.expType,"onUpdate:modelValue":a[2]||(a[2]=e=>le.expType=e),placeholder:"请选择",clearable:""},{default:g(()=>[x(s,{label:"锐捷课程库",value:"锐捷课程库"}),x(s,{label:"学校课程库",value:"学校课程库"})]),_:1},8,["modelValue"])]),_:1})),[[J,["super_admin"]]]),x(r,{label:"实验手册",prop:"expManualFile"},{default:g(()=>[n("div",R,[x(t,{ref_key:"manualUploadRef",ref:ae,"auto-upload":!1,limit:1,accept:".pdf","on-change":de,"on-exceed":ve,"on-remove":ne,class:"upload-demo"},{tip:g(()=>a[7]||(a[7]=[n("div",{class:"form-item-tip"},"请导入格式为.pdf格式的实验手册",-1)])),default:g(()=>[x(i,{type:"primary"},{default:g(()=>a[6]||(a[6]=[b("导入手册")])),_:1,__:[6]})]),_:1},512)])]),_:1}),x(r,{label:"实验工程文件",prop:"expProjectFile"},{default:g(()=>[n("div",I,[x(t,{ref_key:"projectUploadRef",ref:se,"auto-upload":!1,limit:1,accept:".nse","on-change":me,"on-exceed":xe,"on-remove":ce,class:"upload-demo"},{tip:g(()=>a[9]||(a[9]=[n("div",{class:"form-item-tip"},[b(" 请导入格式为.nse格式的实验工程文件 "),n("br"),b(" 可以通过我的实验模块创建实验后，导出实验工程文件 ")],-1)])),default:g(()=>[x(i,{type:"primary"},{default:g(()=>a[8]||(a[8]=[b("导入工程")])),_:1,__:[8]})]),_:1},512)])]),_:1}),n("div",A,[x(i,{style:{"margin-right":"30px"},onClick:a[3]||(a[3]=e=>fe(!1))},{default:g(()=>a[10]||(a[10]=[b("取消")])),_:1,__:[10]}),x(i,{type:"primary",onClick:ge},{default:g(()=>a[11]||(a[11]=[b("确定")])),_:1,__:[11]})])]),_:1},8,["model"]),[[v,!$.value]]),c(n("div",null,[x(i,{onClick:a[4]||(a[4]=e=>fe(!0))},{default:g(()=>[x(q,null,{default:g(()=>[x(w)]),_:1}),a[12]||(a[12]=b(" 返回 "))]),_:1,__:[12]}),n("div",S,[n("div",D,[x(q,null,{default:g(()=>[x(N)]),_:1})]),a[18]||(a[18]=n("div",{class:"success-text"},"新建课程实验成功",-1)),n("div",B,[n("div",L,[a[13]||(a[13]=n("span",{class:"label"},"课程包名称",-1)),n("span",O,h(z.value.coursePkg),1)]),n("div",Q,[a[14]||(a[14]=n("span",{class:"label"},"实验名称",-1)),n("span",X,h(z.value.expName),1)]),n("div",Z,[a[15]||(a[15]=n("span",{class:"label"},"实验类型",-1)),n("span",G,h(z.value.expType),1)]),n("div",H,[a[16]||(a[16]=n("span",{class:"label"},"实验手册",-1)),n("span",{class:"value exp-manual",onClick:a[5]||(a[5]=e=>{return a=z.value.expManualPath,a=encodeURIComponent(a),void window.open(`http://172.29.20.26:8090/api/course-repo/preview?filePath=${a}`);var a})},h(z.value.expManualName),1)]),n("div",K,[a[17]||(a[17]=n("span",{class:"label"},"实验工程文件",-1)),n("span",Y,h(z.value.expProjectName),1)])])])],512),[[v,$.value]])])}}}),[["__scopeId","data-v-bb977926"]]);export{$ as default};
