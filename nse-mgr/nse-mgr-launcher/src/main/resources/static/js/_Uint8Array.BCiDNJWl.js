import{ch as t,c0 as e,ci as r,ce as o,cg as n,cj as a,c7 as c,c4 as s,ck as i,cl as u,cm as p,cn as f}from"./index.Ckm1SagX.js";import{b,i as j,a as y}from"./_arrayPush.Dbwejsrt.js";var l=t(e,"WeakMap");function h(t){return null!=t&&b(t.length)&&!r(t)}var _=Object.prototype;function d(t){var e=t&&t.constructor;return t===("function"==typeof e&&e.prototype||_)}var v="object"==typeof exports&&exports&&!exports.nodeType&&exports,m=v&&"object"==typeof module&&module&&!module.nodeType&&module,g=m&&m.exports===v?e.Buffer:void 0,A=(g?g.isBuffer:void 0)||function(){return!1},w={};function O(t){return function(e){return t(e)}}w["[object Float32Array]"]=w["[object Float64Array]"]=w["[object Int8Array]"]=w["[object Int16Array]"]=w["[object Int32Array]"]=w["[object Uint8Array]"]=w["[object Uint8ClampedArray]"]=w["[object Uint16Array]"]=w["[object Uint32Array]"]=!0,w["[object Arguments]"]=w["[object Array]"]=w["[object ArrayBuffer]"]=w["[object Boolean]"]=w["[object DataView]"]=w["[object Date]"]=w["[object Error]"]=w["[object Function]"]=w["[object Map]"]=w["[object Number]"]=w["[object Object]"]=w["[object RegExp]"]=w["[object Set]"]=w["[object String]"]=w["[object WeakMap]"]=!1;var x="object"==typeof exports&&exports&&!exports.nodeType&&exports,z=x&&"object"==typeof module&&module&&!module.nodeType&&module,S=z&&z.exports===x&&a.process,k=function(){try{var t=z&&z.require&&z.require("util").types;return t||S&&S.binding&&S.binding("util")}catch(e){}}(),P=k&&k.isTypedArray,U=P?O(P):function(t){return o(t)&&b(t.length)&&!!w[n(t)]},B=Object.prototype.hasOwnProperty;function M(t,e){var r=s(t),o=!r&&j(t),n=!r&&!o&&A(t),a=!r&&!o&&!n&&U(t),i=r||o||n||a,u=i?function(t,e){for(var r=-1,o=Array(t);++r<t;)o[r]=e(r);return o}(t.length,String):[],p=u.length;for(var f in t)!e&&!B.call(t,f)||i&&("length"==f||n&&("offset"==f||"parent"==f)||a&&("buffer"==f||"byteLength"==f||"byteOffset"==f)||c(f,p))||u.push(f);return u}function T(t,e){return function(r){return t(e(r))}}var D=T(Object.keys,Object),I=Object.prototype.hasOwnProperty;function E(t){return h(t)?M(t):function(t){if(!d(t))return D(t);var e=[];for(var r in Object(t))I.call(t,r)&&"constructor"!=r&&e.push(r);return e}(t)}function F(t){var e=this.__data__=new i(t);this.size=e.size}function V(){return[]}F.prototype.clear=function(){this.__data__=new i,this.size=0},F.prototype.delete=function(t){var e=this.__data__,r=e.delete(t);return this.size=e.size,r},F.prototype.get=function(t){return this.__data__.get(t)},F.prototype.has=function(t){return this.__data__.has(t)},F.prototype.set=function(t,e){var r=this.__data__;if(r instanceof i){var o=r.__data__;if(!u||o.length<199)return o.push([t,e]),this.size=++r.size,this;r=this.__data__=new p(o)}return r.set(t,e),this.size=r.size,this};var W=Object.prototype.propertyIsEnumerable,q=Object.getOwnPropertySymbols,C=q?function(t){return null==t?[]:(t=Object(t),function(t,e){for(var r=-1,o=null==t?0:t.length,n=0,a=[];++r<o;){var c=t[r];e(c,r,t)&&(a[n++]=c)}return a}(q(t),function(e){return W.call(t,e)}))}:V;function G(t,e,r){var o=e(t);return s(t)?o:y(o,r(t))}function L(t){return G(t,E,C)}var N=t(e,"DataView"),R=t(e,"Promise"),H=t(e,"Set"),J="[object Map]",K="[object Promise]",Q="[object Set]",X="[object WeakMap]",Y="[object DataView]",Z=f(N),$=f(u),tt=f(R),et=f(H),rt=f(l),ot=n;(N&&ot(new N(new ArrayBuffer(1)))!=Y||u&&ot(new u)!=J||R&&ot(R.resolve())!=K||H&&ot(new H)!=Q||l&&ot(new l)!=X)&&(ot=function(t){var e=n(t),r="[object Object]"==e?t.constructor:void 0,o=r?f(r):"";if(o)switch(o){case Z:return Y;case $:return J;case tt:return K;case et:return Q;case rt:return X}return e});var nt=e.Uint8Array;export{H as S,nt as U,ot as a,G as b,O as c,F as d,L as e,d as f,C as g,M as h,A as i,h as j,E as k,U as l,k as n,T as o,V as s};
