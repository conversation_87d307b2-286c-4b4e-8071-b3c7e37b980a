spring:
  datasource:
    url: jdbc:postgresql://${DB_HOST}:${DB_PORT}/${DB_NAME}?currentSchema=mgr&stringtype=unspecified
    username: ${DB_USER}
    password: ${DB_PASSWORD}
    driver-class-name: org.postgresql.Driver


  # Flyway 数据库迁移配置
  flyway:
    enabled: true
    # 迁移脚本位置
    locations: classpath:/db/migration
    # 数据库schema
    default-schema: mgr
    # 验证迁移脚本
    validate-on-migrate: true
    # 清理模式（生产环境必须true）
    clean-disabled: true

nse:
  ehcache:
    storage-path: E:\GNS3_DATA\ehcache-storage-mgr
    heap: 50
    offheap: 100
    disk: 1024
    jwt-expire: 1440
    service-expire: 30
  jwt:
    expire: 7200
  py3:
    storage-path: F:\GNS3_DATA

  jobs:
    pid-validation:
      enabled: true
      cron: "0 */5 * * * ?"
      description: "定时校验ser_hosts表中的pid是否存在，清理无效记录"

mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
