# 1. 停止服务
sudo systemctl stop postgresql@17-main || true

# 2. 彻底清除 PostgreSQL 17 相关包
sudo apt purge -y postgresql-17 postgresql-client-17 postgresql-client postgresql-common postgresql-common-dev 2>/dev/null || true

# 3. 使用 dpkg 强制清除残留配置
sudo dpkg --purge postgresql-17 postgresql-client-17 2>/dev/null || true

# 4. 删除文件（即使包已删，手动保险）
sudo rm -f /usr/bin/psql

# 5. 删除数据和配置
sudo rm -rf /var/lib/postgresql/17/
sudo rm -rf /etc/postgresql/17/
sudo rm -rf /var/log/postgresql/17/

# 6. 清理
sudo apt autoremove -y
sudo apt autoclean

# 7. 验证
which psql || echo "psql 已移除"