# PostgreSQL 17 卸载脚本使用说明

## 概述

本目录包含用于完全卸载 PostgreSQL 17 的脚本，适用于通过离线安装包安装的 PostgreSQL 17。

## 文件说明

- `unist_postgresql.sh` - PostgreSQL 17 完整卸载脚本
- `README.md` - 本说明文件

## 使用方法

### 1. 直接执行卸载脚本

```bash
cd /path/to/nse-mgr/installer/Ubuntu_24_04/scripts/unist
sudo bash unist_postgresql.sh
```

### 2. 从任意位置执行

```bash
sudo bash /path/to/nse-mgr/installer/Ubuntu_24_04/scripts/unist/unist_postgresql.sh
```

## 卸载内容

脚本将完全删除以下内容：

### 1. 服务和进程
- 停止所有 PostgreSQL 相关服务
- 禁用服务自启动

### 2. 软件包
- postgresql-17
- postgresql-client-17
- postgresql-common
- postgresql-common-dev
- libpq5
- 其他相关依赖包

### 3. 用户和组
- postgres 用户
- postgres 组

### 4. 数据和配置
- `/var/lib/postgresql/` - 数据目录
- `/etc/postgresql/` - 配置目录
- `/var/log/postgresql/` - 日志目录
- `/usr/lib/postgresql/17/` - 程序目录
- `/usr/share/postgresql/17/` - 共享文件

### 5. 二进制文件
- psql
- pg_dump
- pg_restore
- createdb
- dropdb
- postgres
- postmaster

### 6. 系统配置
- 环境变量清理
- systemd 配置重载
- 包管理器缓存清理

### 7. 部署状态
- 清理 NSE 部署状态文件中的数据库相关标记

## 安全确认

脚本执行前会要求用户确认：
- 显示将要删除的内容
- 要求输入 'YES' 进行确认
- 只有确认后才会执行卸载操作

## 验证功能

卸载完成后，脚本会自动验证：
- 检查包是否完全卸载
- 检查服务是否停止
- 检查用户是否删除
- 检查目录是否清理
- 检查命令是否移除

## 日志记录

所有操作都会记录到日志文件：
```
/path/to/nse-mgr/installer/Ubuntu_24_04/logs/nse-deploy.log
```

## 注意事项

⚠️ **重要警告**：
1. 此操作不可逆，将永久删除所有 PostgreSQL 数据
2. 请在执行前备份重要数据
3. 确保没有其他应用程序依赖 PostgreSQL
4. 建议在测试环境中先验证脚本功能

## 故障排除

如果卸载不完整，可以：

1. 查看日志文件了解详细信息
2. 手动检查残留文件和进程
3. 重新运行卸载脚本
4. 联系系统管理员

## 重新安装

如需重新安装 PostgreSQL 17，请：

1. 确保卸载完成且验证通过
2. 重新运行安装脚本：
   ```bash
   bash /path/to/nse-mgr/installer/Ubuntu_24_04/scripts/13__postgresql_installer.sh
   ```
