#!/bin/bash

source "$(dirname "$0")/00__config.sh"

# ==================== 获取数据库凭证 ====================
get_db_credentials() {
    log "请输入数据库配置信息："

    read -p "数据库用户名 [$DEFAULT_DB_USER]: " DB_USER_INPUT
    DB_USER="${DB_USER_INPUT:-$DEFAULT_DB_USER}"

    DB_PASS=""
    while [[ -z "$DB_PASS" ]]; do
        read -s -p "数据库密码: " DB_PASS
        echo
        if [[ -z "$DB_PASS" ]]; then
            log "密码不能为空，请重新输入。"
        fi
    done

    local confirm
    read -s -p "确认密码: " confirm
    echo

    if [[ "$DB_PASS" != "$confirm" ]]; then
        error_exit "两次输入的密码不一致，请重新运行脚本。"
    fi

    log "数据库用户名: $DB_USER"
    log "密码: ******** (已隐藏)"

    upsert_env_file

}

create_env_file() {
    sudo mkdir -p "$NSE_PROJECT_DIR/config"
    local file="$NSE_PROJECT_DIR/config/nse-global.conf"
    cat >> "$file" << EOF
DB_HOST=localhost
DB_PORT=$DB_PORT
DB_NAME=$DB_NAME
DB_USER=$DB_USER
DB_PASSWORD=$DB_PASS
EOF
    chmod 600 "$file"
}

upsert_env_file() {
    local config_file="$NSE_PROJECT_DIR/config/nse-global.conf"
    local temp_file="$(mktemp)"

    # 确保目录存在
    sudo mkdir -p "$(dirname "$config_file")"
    sudo chown "$USER":"$USER" "$(dirname "$config_file")"

    # 定义要写入的变量（可以做成参数）
    local vars=(
        "DB_HOST=localhost"
        "DB_PORT=$DB_PORT"
        "DB_NAME=$DB_NAME"
        "DB_USER=$DB_USER"
        "DB_PASSWORD=$DB_PASS"
    )

    # 使用 awk 处理：匹配 KEY= 的行，先删除或更新，最后统一追加缺失的
    {
        # 1. 先读原文件，跳过匹配的变量
        awk -v vars="${vars[*]}" '
        BEGIN {
            split(vars, arr, " ")
            for (i in arr) {
                match(arr[i], /([^=]+)=(.*)/, cap)
                keys[cap[1]] = arr[i]   # keys["DB_HOST"] = "DB_HOST=localhost"
            }
        }
        /^[[:space:]]*[^[:space:]#]/ {
            match($0, /([^= \t#]+)[ \t=]/, name)
            if (name[1] in keys) {
                # 跳过这一行，后面统一写
                next
            }
        }
        { print }
        ' "$config_file" 2>/dev/null || true

        # 2. 输出所有变量（覆盖或追加）
        for v in "${vars[@]}"; do
            echo "$v"
        done
    } > "$temp_file"

    # 3. 移动临时文件
    mv "$temp_file" "$config_file"

    # 4. 设置权限
    chmod 600 "$config_file"
    sudo chown "$USER":"$USER" "$config_file"

    log "配置文件已更新（覆盖或追加）: $config_file"
}

# ==================== 检查 PostgreSQL 是否已安装（包或服务）====================
is_postgresql_installed() {
    local pg_version="17"
    local package_name="postgresql-$pg_version"

    # 方法1：检查主包是否安装
    if dpkg-query -W -f='${Status}' "$package_name" 2>/dev/null | grep -q "install ok installed"; then
        return 0
    fi

    # 方法2：检查服务是否存在
    if systemctl list-units --full --type=service 2>/dev/null | grep -q "postgresql@${pg_version}-main"; then
        return 0
    fi

    # 方法3：检查 postgres 用户和数据目录
    if id "postgres" &>/dev/null && [ -d "/var/lib/postgresql/$pg_version/main" ]; then
        return 0
    fi

    return 1
}

# ==================== 检查数据库是否已配置（用户、库、Schema）====================
is_database_configured() {
    local db_name="$DB_NAME"
    local db_user="$DB_USER"

    # 优先检查状态标记
    if is_state_saved "database_configured"; then
        log "✅ 检测到数据库已配置（状态标记存在）"
        return 0
    fi

    # 备选：尝试连接并查询
    if systemctl is-active "postgresql@17-main" --quiet; then
        if sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='$db_name'" &>/dev/null && \
           sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$db_user'" &>/dev/null; then
            log "✅ 检测到数据库 '$db_name' 和用户 '$db_user' 已存在"
            return 0
        fi
    fi

    return 1
}

# ==================== 安装 .deb 包（仅当未安装时）====================
install_postgresql_debs() {
    log "开始安装 PostgreSQL 17 离线 .deb 包..."

    local pkg_dir="$SOFTS_DIR/postgresql17-offline-complete-pkgs"
    if [ ! -d "$pkg_dir" ]; then
        error_exit "未找到 .deb 包目录: $pkg_dir"
    fi

    cd "$pkg_dir" || error_exit "无法进入目录: $pkg_dir"

    local ordered_debs=(
        "libc6_2.39-0ubuntu8.5_amd64.deb"
        "libgcc-s1_14.2.0-4ubuntu2~24.04_amd64.deb"
        "libcrypt1_1%3a4.4.36-4build1_amd64.deb"
        "libssl3t64_3.0.13-0ubuntu3.5_amd64.deb"
        "libreadline8t64_8.2-4build1_amd64.deb"
        "libtinfo6_6.4+20240113-1ubuntu2_amd64.deb"
        "liblzma5_5.6.1+really5.4.5-1ubuntu0.2_amd64.deb"
        "libzstd1_1.5.5+dfsg2-2build1.1_amd64.deb"
        "liblz4-1_1.9.4-1build1.1_amd64.deb"
        "libselinux1_3.5-2ubuntu2.1_amd64.deb"
        "libpcre2-8-0_10.42-4ubuntu2.1_amd64.deb"
        "libunistring5_1.1-2build1.1_amd64.deb"
        "libuuid1_2.39.3-9ubuntu6.3_amd64.deb"
        "libxml2_2.9.14+dfsg-1.3ubuntu3.4_amd64.deb"
        "libxslt1.1_1.1.39-0exp1ubuntu0.24.04.2_amd64.deb"
        "libicu74_74.2-1ubuntu3.1_amd64.deb"
        "libgdbm6t64_1.23-5.1build1_amd64.deb"
        "libgdbm-compat4t64_1.23-5.1build1_amd64.deb"
        "libdb5.3t64_5.3.28+dfsg2-7_amd64.deb"
        "libedit2_3.1-20230828-1build1_amd64.deb"
        "libperl5.38t64_5.38.2-3.2ubuntu0.2_amd64.deb"
        "perl-base_5.38.2-3.2ubuntu0.2_amd64.deb"
        "libllvm19_19.1.1-1ubuntu1~24.04.2_amd64.deb"
        "libio-pty-perl_1%3a1.20-1build2_amd64.deb"
        "libipc-run-perl_20231003.0-1_all.deb"
        "libjson-perl_4.10000-1_all.deb"
        "ssl-cert_1.1.2ubuntu1_all.deb"
        "libpq5_17.6-1.pgdg24.04+1_amd64.deb"
        "postgresql-client-common_281.pgdg24.04+1_all.deb"
        "postgresql-common-dev_281.pgdg24.04+1_all.deb"
        "postgresql-common_281.pgdg24.04+1_all.deb"
        "readline-common_8.2-4build1_all.deb"
        "netbase_6.4_all.deb"
        "sysvinit-utils_3.08-6ubuntu3_amd64.deb"
        "dpkg_1.22.6ubuntu6.1_amd64.deb"
        "tar_1.35+dfsg-3build1_amd64.deb"
        "adduser_3.137ubuntu1_all.deb"
        "passwd_1%3a4.13+dfsg1-4ubuntu3.2_amd64.deb"
        "libpam0g_1.5.3-5ubuntu5.4_amd64.deb"
        "libpam-modules_1.5.3-5ubuntu5.4_amd64.deb"
        "libpam-modules-bin_1.5.3-5ubuntu5.4_amd64.deb"
        "libaudit1_1%3a3.1.2-2.1build1.1_amd64.deb"
        "libaudit-common_1%3a3.1.2-2.1build1.1_all.deb"
        "libcap2_1%3a2.66-5ubuntu2.2_amd64.deb"
        "libcap-ng0_0.8.4-2build2_amd64.deb"
        "libcom-err2_1.47.0-2.4~exp1ubuntu4.1_amd64.deb"
        "libkeyutils1_1.6.3-3build1_amd64.deb"
        "libmd0_1.1.0-2build1.1_amd64.deb"
        "libsepol2_3.5-2build1_amd64.deb"
        "libsemanage2_3.5-1build5_amd64.deb"
        "libsemanage-common_3.5-1build5_all.deb"
        "libacl1_2.3.2-1build1.1_amd64.deb"
        "libbsd0_0.12.1-1build1.1_amd64.deb"
        "libsystemd0_255.4-1ubuntu8_amd64.deb"
        "init-system-helpers_1.66ubuntu1_all.deb"
        "m4_1.4.19-4build1_amd64.deb"
        "bison_3.8.2+dfsg-1build2_amd64.deb"
        "flex_2.6.4-6_amd64.deb"
        "pkg-config_0.29.1-0ubuntu2_amd64.deb"
        "debconf_1.5.86ubuntu1_all.deb"
        "ucf_3.0043+nmu1_all.deb"
        "lsb-base_11.6_all.deb"
        "sensible-utils_0.0.22_all.deb"
        "tzdata_2025b-0ubuntu0.24.04.1_all.deb"
        "locales_2.39-0ubuntu8.5_all.deb"
        "locales-all_2.39-0ubuntu8.5_amd64.deb"
        "postgresql-client-17_17.6-1.pgdg24.04+1_amd64.deb"
        "postgresql-17_17.6-1.pgdg24.04+1_amd64.deb"
    )

    local installed_any=false

    for deb in "${ordered_debs[@]}"; do
        if [ ! -f "$deb" ]; then
            log "⚠️  文件不存在: $deb，跳过"
            continue
        fi

        package_name=$(dpkg-deb -f "$deb" Package 2>/dev/null || echo "unknown")
        if dpkg-query -W -f='${Status}' "$package_name" 2>/dev/null | grep -q "install ok installed"; then
            log "✅ 包已安装: $package_name，跳过"
            continue
        fi

        log "📦 安装: $package_name"
        if sudo dpkg -i "$deb"; then
            log "✅ 安装成功: $package_name"
            installed_any=true
        else
            log "❌ 安装失败: $package_name"
            sudo apt --fix-broken install -y
            if ! sudo dpkg -i "$deb"; then
                error_exit "多次尝试后仍无法安装: $package_name"
            fi
            installed_any=true
        fi
    done

    if [ "$installed_any" = true ]; then
        log "✅ 有新包安装，修复依赖..."
        sudo apt --fix-broken install -y
    else
        log "✅ 所有必需的 .deb 包已安装，跳过安装阶段"
    fi
}

# ==================== 配置 PostgreSQL ====================
configure_postgresql() {
    log "配置 PostgreSQL 数据库..."

    local db_user="$DB_USER"
    local db_pass="$DB_PASS"
    local db_name="$DB_NAME"
    local schema_name="$SCHEMA_NAME"
    local data_dir="/var/lib/postgresql/17/main"
    local pg_version="17"
    local service_name="postgresql@${pg_version}-main"

    # 重载 systemd
    sudo systemctl daemon-reload

    # 启用并启动服务（如果未启用）
    if ! systemctl is-enabled "$service_name" &>/dev/null; then
        sudo systemctl enable "$service_name" --now
    fi

    if ! systemctl is-active --quiet "$service_name"; then
        log "⚠️  PostgreSQL 服务未运行，正在启动..."
        sudo systemctl start "$service_name"
        wait_for_service "$service_name"
    fi

    # 初始化数据目录（如果不存在）
    if [ ! -d "$data_dir" ]; then
        log "⚠️  数据目录不存在: $data_dir，正在初始化..."
        sudo mkdir -p "$data_dir"
        sudo chown -R postgres:postgres "$data_dir"
        sudo -u postgres /usr/lib/postgresql/${pg_version}/bin/initdb -D "$data_dir" || error_exit "initdb 失败"
        sudo systemctl restart "$service_name"
        wait_for_service "$service_name"
    fi

    # 创建用户、数据库、Schema
    log "创建/更新数据库用户、数据库和 Schema..."
    sudo -u postgres psql -v ON_ERROR_STOP=1 << EOF
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = '$db_user') THEN
        CREATE USER $db_user WITH PASSWORD '$db_pass';
    ELSE
        ALTER USER $db_user WITH PASSWORD '$db_pass';
    END IF;
END \$\$;

\c postgres
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_database WHERE datname = '$db_name') THEN
        CREATE DATABASE $db_name OWNER $db_user;
    END IF;
END \$\$;

\c $db_name
CREATE SCHEMA IF NOT EXISTS $schema_name AUTHORIZATION $db_user;
GRANT ALL ON SCHEMA $schema_name TO $db_user;
EOF

    # 配置 postgresql.conf
    sudo sed -i "s/^#*listen_addresses\s*=.*/listen_addresses = '*'/" "$data_dir/postgresql.conf"
    sudo sed -i "s/^#*port\s*=.*/port = $DB_PORT/" "$data_dir/postgresql.conf"

    # 配置 pg_hba.conf（避免重复）
    local hba_file="$data_dir/pg_hba.conf"
    local rules=(
        "host    $db_name    $db_user    127.0.0.1/32    md5"
        "host    all         all         ::1/128         md5"
        "host    $db_name    $db_user    0.0.0.0/0       md5"
    )

    for rule in "${rules[@]}"; do
        if ! grep -qF "$rule" "$hba_file"; then
            echo "$rule" | sudo tee -a "$hba_file" > /dev/null
        fi
    done

    # 重启服务
    sudo systemctl restart "$service_name"
    wait_for_service "$service_name"

    log "✅ PostgreSQL 配置完成（用户、数据库、模式、远程访问）"
    save_state "database_configured"
}

# ==================== 主流程 ====================

log "开始配置数据库..."

# 获取凭证（始终需要）
get_db_credentials

# 检查 PostgreSQL 是否已安装
if is_postgresql_installed; then
    log "✅ PostgreSQL 已安装，跳过 .deb 包安装。"
else
    log "📦 PostgreSQL 未安装，开始安装离线包..."
    install_postgresql_debs
fi

# 检查是否已配置
if is_database_configured; then
    log "💡 数据库已配置，跳过初始化配置。"
else
    log "🔧 正在配置 PostgreSQL（创建用户、数据库、Schema 等）..."
    configure_postgresql
fi

log "🎉 数据库安装与配置完成！"