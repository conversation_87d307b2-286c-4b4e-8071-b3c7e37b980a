#!/bin/bash

source "$(dirname "$0")/00__config.sh"
PG_CONF_PATH="/etc/postgresql/17/main/postgresql.conf"
PG_HBA_PATH="/etc/postgresql/17/main/pg_hba.conf"
PG_SERVICE_USER="postgres"

# ==================== 配置 PostgreSQL ====================
configure_postgresql() {
    log "开始配置 PostgreSQL 数据库..."

    # 1. 配置 postgresql.conf 允许远程访问
    log "1. 配置 postgresql.conf..."
    if [ -f "$PG_CONF_PATH" ]; then
        # 备份原始文件
        cp "$PG_CONF_PATH" "$PG_CONF_PATH.bak"
        # 使用 sed 修改 listen_addresses
        sed -i "s/^#listen_addresses = 'localhost'/listen_addresses = '*'/" "$PG_CONF_PATH"
        log "   'listen_addresses' 已修改为 '*'。"
    else
        log "警告：未找到 postgresql.conf 文件，跳过此步骤。"
    fi

    # 2. 配置 pg_hba.conf 允许远程认证
    log "2. 配置 pg_hba.conf..."
    if [ -f "$PG_HBA_PATH" ]; then
        # 备份原始文件
        cp "$PG_HBA_PATH" "$PG_HBA_PATH.bak"
        # 检查是否已存在远程认证规则，避免重复添加
        if ! grep -q "host    all             all             0.0.0.0/0               md5" "$PG_HBA_PATH"; then
            echo "host    all             all             0.0.0.0/0               md5" >> "$PG_HBA_PATH"
            log "   已添加远程认证规则到 pg_hba.conf。"
        else
            log "   远程认证规则已存在，无需重复添加。"
        fi
    else
        log "警告：未找到 pg_hba.conf 文件，跳过此步骤。"
    fi

    # 3. 重启 PostgreSQL 服务以应用配置更改
    log "3. 重启 PostgreSQL 服务..."
    systemctl restart postgresql || { log "错误：重启 PostgreSQL 服务失败，请手动检查。"; exit 1; }
    log "   PostgreSQL 服务已重启。"

    # 4. 创建数据库和用户
    log "4. 创建数据库和用户..."
    sudo -u "$PG_SERVICE_USER" psql -c "CREATE USER \"$DEFAULT_DB_USER\" WITH ENCRYPTED PASSWORD '$DEFAULT_DB_PASS';"
    sudo -u "$PG_SERVICE_USER" psql -c "CREATE DATABASE \"$DB_NAME\" OWNER \"$DEFAULT_DB_USER\";"
    log "   用户 '$DEFAULT_DB_USER' 和数据库 '$DB_NAME' 已创建。"

    # 5. 授予权限
    log "5. 授予权限..."
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "GRANT ALL PRIVILEGES ON DATABASE \"$DB_NAME\" TO \"$DEFAULT_DB_USER\";"
    sudo -u "$PG_SERVICE_USER" psql -d "$DB_NAME" -c "GRANT ALL ON SCHEMA \"$SCHEMA_NAME\" TO \"$DEFAULT_DB_USER\";"
    log "   权限授予完成。"

    log "PostgreSQL 数据库配置完成。"
}

register_as_service() {
    log "将 PostgreSQL 注册为系统服务..."
    if command -v systemctl &>/dev/null; then
        log "   systemd 已检测到。"
        systemctl enable postgresql || { log "警告：启用服务失败，可能已经启用。"; }
        log "   PostgreSQL 已配置为开机自启。"
    else
        log "警告：未找到 systemd。如果需要开机自启，请手动配置。"
    fi
}

sudo tar -xzvf "$SOFTS_DIR/postgis-offline.tar.gz" -C ~/
sudo dpkg -i $SOFTS_DIR/postgis-offline/*.deb

configure_postgresql
register_as_service