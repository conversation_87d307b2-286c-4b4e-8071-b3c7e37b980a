#!/bin/bash

source "$(dirname "$0")/00__config.sh"

# ==================== 检查 PostgreSQL 是否已安装（包或服务）====================
is_postgresql_installed() {
    local pg_version="17"
    local package_name="postgresql-$pg_version"

    # 方法1：检查主包是否安装
    if dpkg-query -W -f='${Status}' "$package_name" 2>/dev/null | grep -q "install ok installed"; then
        return 0
    fi

    # 方法2：检查服务是否存在
    if systemctl list-units --full --type=service 2>/dev/null | grep -q "postgresql@${pg_version}-main"; then
        return 0
    fi

    # 方法3：检查 postgres 用户和数据目录
#    if id "postgres" &>/dev/null && [ -d "/var/lib/postgresql/$pg_version/main" ]; then
#        return 0
#    fi

    return 1
}

# ==================== 检查数据库是否已配置（用户、库、Schema）====================
is_database_configured() {
    local db_name="$DB_NAME"
    local db_user="$DEFAULT_DB_USER"

    # 优先检查状态标记
#    if is_state_saved "database_configured"; then
#        log "✅ 检测到数据库已配置（状态标记存在）"
#        return 0
#    fi

    # 备选：尝试连接并查询
    if systemctl is-active "postgresql@17-main" --quiet; then
        if sudo -u postgres psql -tAc "SELECT 1 FROM pg_database WHERE datname='$db_name'" &>/dev/null && \
           sudo -u postgres psql -tAc "SELECT 1 FROM pg_roles WHERE rolname='$db_user'" &>/dev/null; then
            log "✅ 检测到数据库 '$db_name' 和用户 '$db_user' 已存在"
            return 0
        fi
    fi

    return 1
}


# ==================== 配置 PostgreSQL ====================
configure_postgresql() {
    log "配置 PostgreSQL 数据库..."

    local db_user="$DEFAULT_DB_USER"
    local db_pass="$DEFAULT_DB_PASS"
    local db_name="$DB_NAME"
    local schema_name="$SCHEMA_NAME"
    local data_dir="/var/lib/postgresql/17/main"
    local pg_version="17"
    local service_name="postgresql@${pg_version}-main"

    # 重载 systemd
    sudo systemctl daemon-reload

    # 启用并启动服务（如果未启用）
    if ! systemctl is-enabled "$service_name" &>/dev/null; then
        sudo systemctl enable "$service_name" --now
    fi

    if ! systemctl is-active --quiet "$service_name"; then
        log "⚠️  PostgreSQL 服务未运行，正在启动..."
        sudo systemctl start "$service_name"
        wait_for_service "$service_name"
    fi

    # 初始化数据目录（如果不存在）
    if [ ! -d "$data_dir" ]; then
        log "⚠️  数据目录不存在: $data_dir，正在初始化..."
        sudo mkdir -p "$data_dir"
        sudo chown -R postgres:postgres "$data_dir"
        sudo -u postgres /usr/lib/postgresql/${pg_version}/bin/initdb -D "$data_dir" || error_exit "initdb 失败"
        sudo systemctl restart "$service_name"
        wait_for_service "$service_name"
    fi

    # 创建用户、数据库、Schema
    log "创建/更新数据库用户、数据库和 Schema..."
    sudo -u postgres psql -v ON_ERROR_STOP=1 << EOF
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = '$db_user') THEN
        CREATE USER $db_user WITH PASSWORD '$db_pass';
    ELSE
        ALTER USER $db_user WITH PASSWORD '$db_pass';
    END IF;
END \$\$;

\c postgres
DO \$\$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_database WHERE datname = '$db_name') THEN
        CREATE DATABASE $db_name OWNER $db_user;
    END IF;
END \$\$;

\c $db_name
CREATE SCHEMA IF NOT EXISTS $schema_name AUTHORIZATION $db_user;
GRANT ALL ON SCHEMA $schema_name TO $db_user;
EOF

    # 配置 postgresql.conf
    sudo sed -i "s/^#*listen_addresses\s*=.*/listen_addresses = '*'/" "$data_dir/postgresql.conf"
    sudo sed -i "s/^#*port\s*=.*/port = $DB_PORT/" "$data_dir/postgresql.conf"

    # 配置 pg_hba.conf（避免重复）
    local hba_file="$data_dir/pg_hba.conf"
    local rules=(
        "host    $db_name    $db_user    127.0.0.1/32    md5"
        "host    all         all         ::1/128         md5"
        "host    $db_name    $db_user    0.0.0.0/0       md5"
    )

    for rule in "${rules[@]}"; do
        if ! grep -qF "$rule" "$hba_file"; then
            echo "$rule" | sudo tee -a "$hba_file" > /dev/null
        fi
    done

    # 重启服务
    sudo systemctl restart "$service_name"
    wait_for_service "$service_name"

    log "✅ PostgreSQL 配置完成（用户、数据库、模式、远程访问）"
    save_state "database_configured"
}

# ==================== 主流程 ====================

log "开始配置数据库..."

# 检查是否已配置
if is_database_configured; then
    log "💡 数据库已配置，跳过初始化配置。"
else
    log "🔧 正在配置 PostgreSQL（创建用户、数据库、Schema 等）..."
    configure_postgresql
fi

log "🎉 数据库安装与配置完成！"