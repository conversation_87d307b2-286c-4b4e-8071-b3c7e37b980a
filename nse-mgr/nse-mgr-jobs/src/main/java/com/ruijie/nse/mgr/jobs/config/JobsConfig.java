package com.ruijie.nse.mgr.jobs.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 定时任务配置类
 * 
 * <AUTHOR> Team
 * @since 2025-01-06
 */
@Slf4j
@Data
@Configuration
@EnableScheduling
@ConfigurationProperties(prefix = "nse.jobs")
public class JobsConfig {

    /**
     * PID校验任务配置
     */
    private PidValidation pidValidation = new PidValidation();

    @Data
    public static class PidValidation {
        /**
         * 是否启用PID校验任务
         */
        private boolean enabled = true;

        /**
         * Cron表达式，默认每5分钟执行一次
         */
        private String cron = "0 */5 * * * ?";

        /**
         * 任务描述
         */
        private String description = "定时校验ser_hosts表中的pid是否存在，清理无效记录";
    }

    /**
     * 初始化配置信息
     */
    public void init() {
        log.info("定时任务配置初始化完成:");
        log.info("PID校验任务 - 启用状态: {}, Cron表达式: {}", 
                pidValidation.enabled, pidValidation.cron);
    }
}