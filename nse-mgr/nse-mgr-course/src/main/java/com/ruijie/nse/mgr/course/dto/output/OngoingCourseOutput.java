package com.ruijie.nse.mgr.course.dto.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * 获取进行中的课程列表
 */
@Data
public class OngoingCourseOutput {

    private String id;

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 任课老师
     */
    private String teacherName;

    /**
     * 课程名称
     */
    private String courseName;

    /**
     * 班级名称
     */
    private String className;

    /**
     * 班级ID
     */
    private String classId;

    /**
     * 状态
     */
    private String status;

    /**
     * 学生数量
     */
    private Integer studentCnt;

    /**
     * 已登录人数
     */
    private Integer loginCnt;

    /**
     * 实验数量
     */
    private Integer expCnt;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdDate;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间 即上课时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedDate;
}
