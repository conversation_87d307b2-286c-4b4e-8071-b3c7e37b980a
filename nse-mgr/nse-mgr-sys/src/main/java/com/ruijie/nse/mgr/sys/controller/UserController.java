package com.ruijie.nse.mgr.sys.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruijie.nse.common.annotation.Anonymous;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.common.handler.StudentLoginManager;
import com.ruijie.nse.mgr.common.constants.WebSocketConstants;
import com.ruijie.nse.mgr.common.handler.WebSocketHandler;
import com.ruijie.nse.mgr.common.dto.WebsocketMessage;
import com.ruijie.nse.mgr.repository.dto.input.UserQueryInput;
import com.ruijie.nse.mgr.repository.dto.output.UserOutput;
import com.ruijie.nse.mgr.repository.entity.Menu;
import com.ruijie.nse.mgr.repository.entity.User;
import com.ruijie.nse.mgr.sys.dto.input.PasswordResetInput;
import com.ruijie.nse.mgr.sys.dto.input.UserInput;
import com.ruijie.nse.mgr.sys.dto.output.*;
import com.ruijie.nse.mgr.sys.service.OnlineUserService;
import com.ruijie.nse.mgr.sys.service.UserService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.logout.SecurityContextLogoutHandler;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.Collections;
import java.util.List;

@RestController
@RequestMapping("/api/user")
public class UserController {

    @Autowired
    private UserService userService;

    @Autowired
    private EhcacheService ehcacheService;

    @Resource
    private StudentLoginManager studentLoginManager;

    @Resource
    private OnlineUserService onlineUserService;

    /**
     * 退出
     *
     * @return
     */
    @DeleteMapping("/logout")
    public R<LoginOutput> logout(HttpServletRequest request, HttpServletResponse response) {
        studentLoginManager.studentLogout(SecurityUtils.getUserId());
        // 从缓存中删除jwt令牌
        ehcacheService.evict(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, SecurityUtils.getUserId());
        // 执行注销操作
        new SecurityContextLogoutHandler().logout(request, response, SecurityContextHolder.getContext().getAuthentication());
        // 清除认证信息
        SecurityContextHolder.clearContext();
        // 刷新前端登录数量
        WebsocketMessage message = WebsocketMessage.create(WebSocketConstants.REFRESH_LOGIN_NUM_EVENT, onlineUserService.getOnlineUserCount());
        WebSocketHandler.sendMsg(JSONUtil.toJsonStr(message));
        return R.success();
    }

    /**
     * 获取登录状态
     *
     * @return
     */
    @GetMapping("status")
    public R<LoginStatusOutput> getLoginStatus() {
        return R.success(userService.getLoginStatus());
    }

    /**
     * 获取当前用户信息
     *
     * @return
     */
    @GetMapping("me")
    public R<UserInfoOutput> me() {
        return R.success(userService.me());
    }

    /**
     * 获取用户树结构数据
     *
     * @return
     */
    @GetMapping("tree")
    public R<List<OptionOutput<String>>> getStudentTree(
            @RequestParam(required = false, defaultValue = "true") boolean containExpiredAccount) {
        return R.success(userService.getStudentTree(containExpiredAccount));
    }

    /**
     * 获取用户分页数据
     *
     * @param userQueryInput
     * @return
     */
    @GetMapping("page")
    public R<PageOutput<UserOutput>> page(UserQueryInput userQueryInput) {
        return R.success(userService.findByPage(userQueryInput));
    }

    /**
     * 获取班级信息
     *
     * @param className
     * @return
     */
    @GetMapping("classList")
    public R<List<String>> getClassList(String className) {
        return R.success(userService.getClassList(className));
    }

    /**
     * 保存用户信息
     *
     * @param userInput 用户实体对象
     */
    @PostMapping
    public R<Void> save(@RequestBody UserInput userInput) {
        userService.saveOrUpdate(userInput);
        return R.success();
    }

    /**
     * 根据用户账号获取用户信息
     *
     * @param account  用户账号
     * @param userType 用户类型
     * @return 返回用户对象
     */
    @GetMapping("/account/{account}")
    public R<UserOutput> getByAccount(@PathVariable String account, String userType) {
        List<User> users = userService.list(Wrappers.lambdaQuery(User.class)
                .eq(User::getAccount, account)
                .eq(User::getUserType, userType));
        if (CollUtil.isNotEmpty(users)) {
            return R.success(BeanUtil.toBean(users.get(0), UserOutput.class));
        }
        return R.success();
    }

    /**
     * 重置密码
     *
     * @param userInput 用户输入对象
     */
    @PostMapping("/resetPassword")
    public R<Void> resetPassword(@RequestBody UserInput userInput) {
        userService.resetPassword(userInput);
        return R.success();
    }

    /**
     * 修改密码
     *
     * @param id       主键id
     * @param passwordResetInput 新密码参数对象
     * @return Void
     */
    @PutMapping("/{id}/password/reset")
    public R<Void> updatePassword(@PathVariable String id,
                                  @RequestBody PasswordResetInput passwordResetInput) {
        userService.updatePassword(id, passwordResetInput);
        return R.success();
    }

    /**
     * 校验当前密码是否正确
     *
     * @param id       主键id
     * @param password 密码
     * @return boolean
     */
    @GetMapping("/{id}/isPasswordCorrect")
    public R<Boolean> isPasswordCorrect(@PathVariable("id") String id, @RequestParam("password") String password) {
        return R.success(userService.isPasswordCorrect(id, password));
    }

    /**
     * 批量设置失效时间
     *
     * @param userInput 用户输入对象
     */
    @PostMapping("/batchSetValidDate")
    public R<Void> batchSetValidDate(@RequestBody UserInput userInput) {
        if (CollUtil.isEmpty(userInput.getIds())) {
            return R.error("请选择要设置的用户");
        }
        userService.update(Wrappers.lambdaUpdate(User.class)
                .set(User::getValidDate, userInput.getValidDate())
                .in(User::getId, userInput.getIds()));
        return R.success();
    }

    /**
     * 下载模板
     *
     * @param userType
     */
    @GetMapping("/downloadTemplate")
    @Anonymous
    public void downloadTemplate(String userType, HttpServletResponse response) {
        userService.downloadTemplate(userType, response);
    }

    /**
     * 解析导入文件
     *
     * @param userType
     * @param file
     * @return
     */
    @PostMapping("/importFile")
    @Anonymous
    public R<R<List<ImportUserOutput>>> importFile(String userType, MultipartFile file) {
        try {
            return R.success(com.ruijie.nse.common.dto.R.success("上传成功", userService.resolveFile(userType, file)));
        } catch (BusinessException e) {
            return R.success(com.ruijie.nse.common.dto.R.error(e.getMessage()));
        }
    }

    /**
     * 批量新增
     *
     * @param users
     * @return
     */
    @PostMapping("/batchAdd")
    public R<Void> batchAdd(@RequestBody List<UserInput> users) {
        userService.batchAdd(users);
        return R.success();
    }

    /**
     * 获取所有用户列表
     *
     * @return 返回用户列表数据
     */
    @GetMapping
    public R<List<User>> list() {
        return R.success(userService.list());
    }

    /**
     * 根据用户 ID 获取用户信息
     *
     * @param id 用户唯一标识
     * @return 返回用户对象
     */
    @GetMapping("/{id}")
    public R<User> get(@PathVariable String id) {
        return R.success(userService.getById(id));
    }

    /**
     * 删除指定 ID 的用户
     *
     * @param id 用户唯一标识
     */
    @DeleteMapping("/{id}")
    public R<Void> delete(@PathVariable String id) {
        userService.deleteByIds(Collections.singletonList(id));
        return R.success();
    }

    /**
     * 批量删除
     *
     * @param id 用户唯一标识
     */
    @DeleteMapping("/batch")
    public R<Void> delete(@RequestBody List<String> id) {
        userService.deleteByIds(id);
        return R.success();
    }

    /**
     * 获取指定用户的关联角色列表
     *
     * @param userId 用户唯一标识
     * @return 返回角色 ID 列表
     */
    @GetMapping("/role/{userId}")
    public R<List<String>> getUserRoles(@PathVariable String userId) {
        return R.success(userService.listRolesByUserId(userId));
    }

    /**
     * 给指定用户分配角色
     *
     * @param userId  用户唯一标识
     * @param roleIds 角色 ID 列表
     */
    @PostMapping("/role/{userId}")
    public R<Void> saveUserRoles(@PathVariable String userId, @RequestBody List<String> roleIds) {
        userService.assignRole(userId, roleIds);
        return R.success();
    }

    /**
     * 获取指定用户的菜单权限列表
     *
     * @param userId 用户唯一标识
     * @return 返回菜单对象列表
     */
    @GetMapping("/menu/{userId}")
    public R<List<Menu>> getUserMenus(@PathVariable String userId) {
        return R.success(userService.findMenusByUserId(userId));
    }

}
