package com.ruijie.nse.mgr.sys.dto.output;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 在线用户信息输出DTO
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OnlineUserOutput {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 用户名（登录账号）
     */
    private String username;

    /**
     * 用户姓名
     */
    private String name;

    /**
     * 用户类型
     */
    private UserType userType;

    /**
     * 用户类型文本
     */
    private String userTypeText;

    /**
     * 用户权限
     */
    private String authorities;

    /**
     * 登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date loginTime;

    /**
     * Token过期时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date expireTime;

    /**
     * 登录IP地址
     */
    private String ipAddress;

    /**
     * 在线状态
     */
    private String status;

    /**
     * 在线时长（分钟）
     */
    public Long getOnlineDuration() {
        if (loginTime == null) {
            return 0L;
        }
        return (System.currentTimeMillis() - loginTime.getTime()) / (1000 * 60);
    }

    /**
     * 剩余时长（分钟）
     */
    public Long getRemainingTime() {
        if (expireTime == null) {
            return 0L;
        }
        long remaining = expireTime.getTime() - System.currentTimeMillis();
        return remaining > 0 ? remaining / (1000 * 60) : 0L;
    }
}
