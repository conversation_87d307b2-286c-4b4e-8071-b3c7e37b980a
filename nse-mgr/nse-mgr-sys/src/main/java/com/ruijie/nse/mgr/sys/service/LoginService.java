package com.ruijie.nse.mgr.sys.service;


import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruijie.nse.common.config.security.UserPrincipal;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.enctry.JwtUtil;
import com.ruijie.nse.mgr.common.constants.WebSocketConstants;
import com.ruijie.nse.mgr.common.dto.QueuingStudent;
import com.ruijie.nse.mgr.common.dto.WebsocketMessage;
import com.ruijie.nse.mgr.common.handler.*;
import com.ruijie.nse.mgr.license.service.LicenseActivationInfoService;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import com.ruijie.nse.mgr.repository.entity.User;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.repository.mapper.UserDao;
import com.ruijie.nse.mgr.sys.dto.output.LoginOutput;
import com.ruijie.nse.mgr.sys.dto.output.OnlineUserOutput;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;


/**
 * 登录服务
 *
 * <AUTHOR>
 * @date 2025-07-16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LoginService {

    private final AuthenticationManager authenticationManager;
    private final EhcacheService ehcacheService;
    private final LicenseActivationInfoService licenseActivationInfoService;
    private final OnlineUserService onlineUserService;
    private final StudentLoginManager studentLoginManager;
    private final UserDao userDao;

    /**
     * 账号密码登录
     *
     * @param account
     * @param password
     * @return
     */
    public LoginOutput loginByAccount(String account, String password) {
        log.info("用户登录: {}", account);

        try {
            // 创建认证令牌
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    account, password
            );

            // 进行认证
            Authentication authentication = authenticationManager.authenticate(authToken);

            // 获取用户信息
            UserPrincipal userPrincipal = (UserPrincipal) authentication.getPrincipal();

            // 检查用户是否可以登录
            LoginOutput res = checkCanLogin(userPrincipal);
            if (res != null) {
                return res;
            }

            // 生成JWT令牌
            String accessToken = JwtUtil.generateAccessToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername(),
                    userPrincipal.getAuthoritiesString()
            );

            String refreshToken = JwtUtil.generateRefreshToken(
                    userPrincipal.getUserId(),
                    userPrincipal.getUsername()
            );

            // 构建登录结果
            LoginOutput.UserInfo userInfo = LoginOutput.UserInfo.builder()
                    .userId(userPrincipal.getUserId())
                    .username(userPrincipal.getUsername())
                    .build();

            LoginOutput result = LoginOutput.builder()
                    .loginResult(200)
                    .accessToken(accessToken)
                    .refreshToken(refreshToken)
                    .tokenType(CommonConstant.Jwt.TOKEN_TYPE)
                    .expiresIn(CommonConstant.Jwt.EXPIRATION / 1000)
                    .userInfo(userInfo)
                    .build();

            LambdaUpdateWrapper<User> updateWrapper = Wrappers.lambdaUpdate(User.class)
                    .set(User::getLoginDate, LocalDateTime.now())
                    .eq(User::getId, userPrincipal.getUserId());
            userDao.update(updateWrapper);

            // 将token存储到Ehcache
            ehcacheService.put(CacheConstants.JWT_TOKEN_CACHE, userPrincipal.getUserId(), accessToken);

            log.info("用户登录成功: {}", result.toString());
            WebsocketMessage message = WebsocketMessage.create(WebSocketConstants.REFRESH_LOGIN_NUM_EVENT, onlineUserService.getOnlineUserCount());
            WebSocketHandler.sendMsg(JSONUtil.toJsonStr(message));
            return result;

        } catch (BadCredentialsException e) {
            log.warn("用户登录失败，用户名或密码错误: {}", account);
            throw new BadCredentialsException("用户名或密码错误");
        } catch (Exception e) {
            log.error("用户登录失败: {}, 错误: {}", account, e.getMessage(), e);
            throw new RuntimeException("登录失败: " + e.getMessage());
        }
    }


    /**
     * 检查用户是否可以登录
     *
     * @param userPrincipal
     * @return
     */
    private LoginOutput checkCanLogin(UserPrincipal userPrincipal) {
        // 判断用户类型，控制并发登录数量
        String userType = userPrincipal.getUserType();
        if (UserType.TEACHER.getValue().equals(userType)) {
            LicenseActivationInfo currentLicense = licenseActivationInfoService.getCurrentLicense();
            Integer permitMgrCnt = currentLicense.getPermitMgrCnt();

            OnlineUserOutput onlineUserInfo = onlineUserService.getOnlineUserInfo(userPrincipal.getUserId());
            if (onlineUserInfo != null) {
                return null;
            }
            List<OnlineUserOutput> onlineTeachers = onlineUserService.getOnlineUsers().stream()
                    .filter(onlineUser -> onlineUser.getUserType() == UserType.TEACHER)
                    .filter(onlineUser -> !onlineUser.getUserId().equals(userPrincipal.getUserId()))
                    .toList();
            if (onlineTeachers.size() >= permitMgrCnt) {
                log.info("教师用户登录失败，当前教师用户数已达到许可数: {}", permitMgrCnt);
                List<LoginOutput.OnlineUser> onlineUsers = onlineTeachers.stream()
                        .map(onlineUser ->
                                LoginOutput.OnlineUser
                                        .builder()
                                        .name(onlineUser.getName())
                                        .mobilePhone(onlineUser.getMobilePhone())
                                        .build()
                        )
                        .toList();
                return LoginOutput.builder()
                        .loginResult(201)
                        .loginErrMsg("登录账号达到上限，请确认！")
                        .onlineUsers(onlineUsers)
                        .build();
            }
        } else if (UserType.STUDENT.getValue().equals(userType)) {
            try {
                studentLoginManager.studentLoginCheck(userPrincipal.getUserId());
            } catch (BusinessException e) {
                LoginOutput.UserInfo userInfo = LoginOutput.UserInfo.builder()
                        .userId(userPrincipal.getUserId())
                        .username(userPrincipal.getUsername())
                        .build();
                if ("正在排队".equals(e.getMessage())) {
                    return LoginOutput.builder()
                            .loginResult(202)
                            .userInfo(userInfo)
                            .build();
                }
                return LoginOutput.builder()
                        .loginResult(201)
                        .loginErrMsg(e.getMessage())
                        .build();
            }
        }
        return null;
    }

    /**
     * 获取排队信息
     *
     * @return
     */
    public QueuingStudent getQueueInfo(String userId) {
        return studentLoginManager.getQueueInformation(userId);
    }

    /**
     * 取消排队
     *
     * @return
     */
    public Boolean cancelQueuing(String userId) {
        studentLoginManager.cancelQueuing(userId);
        return true;
    }
}
