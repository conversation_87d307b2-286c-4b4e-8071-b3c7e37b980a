package com.ruijie.nse.mgr.sys.enums;

import lombok.Getter;

@Getter
public enum StudentExcelHeader implements BaseExcelHeader {

    ACCOUNT("学号*", 20, "account"),
    NAME("姓名*", 20, "name"),
    MOBILE_PHONE("班级*", 20, "mobilePhone"),
    VALID_DATE("账号失效日期", 20, "validDate");

    private final String alias;
    private final Integer width;
    private final String property;

    /**
     * @param alias    标题名称
     * @param width    列宽度
     * @param property 属性
     */
    StudentExcelHeader(String alias, Integer width, String property) {
        this.alias = alias;
        this.width = width;
        this.property = property;
    }

    @Override
    public BaseExcelHeader[] getValues() {
        return values();
    }

}
