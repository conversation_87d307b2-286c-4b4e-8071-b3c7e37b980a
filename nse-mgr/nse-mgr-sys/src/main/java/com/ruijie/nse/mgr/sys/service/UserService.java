package com.ruijie.nse.mgr.sys.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.constant.CommonConstant;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.entity.BaseEntity;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.utils.enctry.PasswordUtil;
import com.ruijie.nse.common.utils.security.SecurityUtils;
import com.ruijie.nse.mgr.course.service.ExperimentService;
import com.ruijie.nse.mgr.repository.dto.input.UserQueryInput;
import com.ruijie.nse.mgr.repository.dto.output.UserOutput;
import com.ruijie.nse.mgr.repository.entity.*;
import com.ruijie.nse.mgr.repository.entity.enums.OfficialType;
import com.ruijie.nse.mgr.repository.entity.enums.UserStatus;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.repository.mapper.*;
import com.ruijie.nse.mgr.sys.dto.input.PasswordResetInput;
import com.ruijie.nse.mgr.sys.dto.input.UserInput;
import com.ruijie.nse.mgr.sys.dto.output.ImportUserOutput;
import com.ruijie.nse.mgr.sys.dto.output.OptionOutput;
import com.ruijie.nse.mgr.sys.dto.output.UserInfoOutput;
import com.ruijie.nse.mgr.sys.enums.BaseExcelHeader;
import com.ruijie.nse.mgr.sys.enums.StudentExcelHeader;
import com.ruijie.nse.mgr.sys.enums.TeacherExcelHeader;
import jakarta.annotation.Resource;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.dromara.hutool.core.bean.BeanUtil;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.collection.iter.IterUtil;
import org.dromara.hutool.core.reflect.FieldUtil;
import org.dromara.hutool.core.regex.ReUtil;
import org.dromara.hutool.core.text.CharSequenceUtil;
import org.dromara.hutool.core.text.StrValidator;
import org.dromara.hutool.poi.excel.ExcelUtil;
import org.springframework.cache.annotation.CacheConfig;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


/**
 * <p>
 * $!{table.comment} 服务类
 * </p>
 *
 * <AUTHOR>
 * @date 2018-08-14
 */
@Slf4j
@Service
@CacheConfig(cacheNames = CacheConstants.SERVICE_CACHE)
public class UserService extends ServiceImpl<UserDao, User> {

    @Resource
    private MenuDao menuDao;
    @Resource
    private RoleMenuDao roleMenuDao;
    @Resource
    private RoleDao roleDao;
    @Resource
    private UserOfficialDao userOfficialDao;
    @Resource
    private OfficialService officialService;
    @Resource
    private UserRoleDao userRoleDao;
    @Resource
    private CourseDao courseDao;
    @Resource
    private LessonDao lessonDao;
    @Resource
    private CourseLessonDao courseLessonDao;
    @Resource
    private ExperimentService experimentService;

    /**
     * 获取当前登录用户信息
     *
     * @return
     */
    public UserInfoOutput me() {
        String userId = SecurityUtils.getUserId();
        User user = baseMapper.selectById(userId);
        return new UserInfoOutput()
                .setUserId(userId)
                .setUsername(user.getAccount())
                .setNickName(user.getName())
                .setAvatar(user.getAvatar())
                .setIsDefaultPwd(user.getIsDefaultPwd())
                .setPerms(listPermissionsByUserId(userId))
                .setRoles(listRolesByUserId(userId));
    }

    /**
     * 分页查询用户
     *
     * @param userQueryInput
     * @return
     */
    public PageOutput<UserOutput> findByPage(UserQueryInput userQueryInput) {
        Page<UserOutput> userPage = baseMapper.findPage(new Page<>(userQueryInput.getPageNumber(), userQueryInput.getPageSize()), userQueryInput);
        /*
        List<UserOutput> userOutputs = BeanUtil.copyToList(userPage.getRecords(), UserOutput.class);
        if (CollUtil.isNotEmpty(userOutputs)) {
            List<String> ids = userOutputs.stream().filter(userOutput -> userOutput.getUserType() == UserType.STUDENT).map(UserOutput::getId).toList();
            if (CollUtil.isNotEmpty(ids)) {
                List<UserOfficialOutput> userOfficialOutputs = userOfficialDao.selectUserOfficialByUserId(ids);
                // 映射为用户id与班级名称List的map
                Map<String, List<UserOfficialOutput>> userOfficialList = userOfficialOutputs.stream().collect(Collectors.groupingBy(UserOfficialOutput::getUserId));
                for (UserOutput userOutput : userOutputs) {
                    Optional.ofNullable(userOfficialList.get(userOutput.getId()))
                            .ifPresent(userOfficialOutputList -> userOutput.setClasses(CollUtil.join(userOfficialOutputList.stream().map(UserOfficialOutput::getOfficialName).toList(), StrPool.COMMA)));
                }
            }
        }
        */
        return new PageOutput<>(userPage.getTotal(), userPage.getRecords());
    }


    /**
     * 构建用户树
     *
     * @return
     */
    public List<OptionOutput<String>> getStudentTree(boolean containExpiredAccount) {
        // 获取所有班级
        List<Official> classes = officialService.list(Wrappers.lambdaQuery(Official.class)
                .eq(Official::getType, OfficialType.CLASS)
                .orderByAsc(Official::getName));
        List<UserOfficial> userOfficials = userOfficialDao.selectList(null);
        List<User> students = baseMapper.selectList(Wrappers.lambdaQuery(User.class).eq(User::getUserType, UserType.STUDENT));

        // 构建班级节点
        List<OptionOutput<String>> classTreeNodes = new ArrayList<>();
        for (Official clazz : classes) {
            OptionOutput<String> classNode = new OptionOutput<>(clazz.getId(), clazz.getName());
            Set<String> userIds = userOfficials.stream().filter(userOfficial -> userOfficial.getOfficialId().equals(clazz.getId())).map(UserOfficial::getUserId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(userIds)) {
                List<OptionOutput<String>> children = students.stream().filter(student -> userIds.contains(student.getId()))
                        .filter(student -> student.getValidDate() == null || new Date().before(student.getValidDate()))
                        .sorted(Comparator.comparing(User::getName))
                        .map(student -> new OptionOutput<>(student.getId(), String.format("%s (%s)", student.getName(), student.getAccount())))
                        .toList();
                classNode.setChildren(children);
            }
            classTreeNodes.add(classNode);
        }
        if (!containExpiredAccount) {
            return classTreeNodes;
        }
        OptionOutput<String> expiredAccount = new OptionOutput<>("invalidAccount", "失效账号列表");
        List<OptionOutput<String>> children = students.stream().filter(student -> student.getValidDate() != null && new Date().after(student.getValidDate()))
                .map(student -> new OptionOutput<>(student.getId(), String.format("%s (%s)", student.getName(), student.getAccount())))
                .toList();
        expiredAccount.setChildren(children);
        classTreeNodes.add(expiredAccount);
        return classTreeNodes;
    }


    /**
     * 保存或更新用户
     *
     * @param userInput
     * @return
     */
    @Transactional
    public void saveOrUpdate(UserInput userInput) {
        boolean isInsert = userInput.getId() == null;
        User user = BeanUtil.toBean(userInput, User.class);
        if (isInsert) {
            String salt = PasswordUtil.generateSalt();
            user.setSalt(salt);
            user.setPassword(PasswordUtil.encodePassword(CommonConstant.System.DEFAULT_PASSWORD, salt));
            user.setStatus(UserStatus.OK);
            baseMapper.insert(user);
            Role role = roleDao.selectOne(Wrappers.lambdaQuery(Role.class)
                    .eq(Role::getName, UserType.TEACHER == user.getUserType() ?
                            CommonConstant.System.ADMIN : CommonConstant.System.USER));
            Optional.ofNullable(role).ifPresent(r -> {
                UserRole userRole = new UserRole();
                userRole.setUserId(user.getId());
                userRole.setRoleId(role.getId());
                userRoleDao.insert(userRole);
            });
        } else {
            baseMapper.updateById(user);
        }

        if (UserType.STUDENT == user.getUserType()) {
            if (!isInsert) {
                userOfficialDao.delete(Wrappers.lambdaQuery(UserOfficial.class).eq(UserOfficial::getUserId, user.getId()));
            }
            if (StrValidator.isBlank(userInput.getClasses())) {
                return;
            }
            // 只会有一个班级，不做分割
            // String[] classes = userInput.getClasses().split(StrPool.COMMA);
            String[] classes = { userInput.getClasses() };
            LambdaQueryWrapper<Official> wrapper = Wrappers.lambdaQuery(Official.class)
                    .in(Official::getName, Arrays.asList(classes));
            List<Official> officials = officialService.list(wrapper);
            List<String> toAddClasses = Arrays.asList(classes);
            if (CollUtil.isNotEmpty(officials)) {
                List<String> existNames = officials.stream().map(Official::getName).toList();
                toAddClasses = Arrays.stream(classes).filter(name -> !existNames.contains(name)).toList();
            }
            // 新增不存在的班级
            if (CollUtil.isNotEmpty(toAddClasses)) {
                List<Official> officialList = toAddClasses.stream().map(className -> new Official().setName(className).setType(OfficialType.CLASS)).toList();
                officialService.saveBatch(officialList);
                officials.addAll(officialList);
            }
            // 新增关联关系
            Set<String> officialIds = officials.stream().map(BaseEntity::getId).collect(Collectors.toSet());
            if (CollUtil.isNotEmpty(officialIds)) {
                officialIds.stream()
                        .map(id -> new UserOfficial().setUserId(user.getId())
                                .setOfficialId(id))
                        .forEach(userOfficial -> userOfficialDao.insert(userOfficial));
            }
        }
    }

    /**
     * 重置密码
     *
     * @param userInput
     */
    public void resetPassword(UserInput userInput) {
        User user = baseMapper.selectById(userInput.getId());
        if (user == null) {
            throw BusinessException.errorByMessage("用户不存在");
        }
        user.setPassword(PasswordUtil.encodePassword(CommonConstant.System.DEFAULT_PASSWORD, user.getSalt()));
        user.setIsDefaultPwd(true);
        baseMapper.updateById(user);
    }

    /**
     * 根据用户id获取权限。
     * 这里整合了超级管理员账号和其它账号
     *
     * @param userId
     * @return
     */
    public List<String> listPermissionsByUserId(String userId) {
        List<String> permissionList;
        List<String> roles = listRolesByUserId(userId);
        // 若是超级管理员，则直接有所有权限，否则从数据库中取权限
        if (roles.contains(CommonConstant.System.SUPER_ADMIN)) {
            permissionList = menuDao.selectList(Wrappers.lambdaQuery(Menu.class).isNotNull(Menu::getPerm).orderByAsc(Menu::getSort))
                    .stream()
                    .map(Menu::getPerm)
                    .toList();
        } else {
            permissionList = baseMapper.listPermissionsByUserId(userId);
        }
        return permissionList;
    }

    /**
     * 根据用户id获取角色ID。
     *
     * @param userId
     * @return
     */
    public List<String> listRolesByUserId(String userId) {
        return baseMapper.listRolesByUserId(userId);
    }

    /**
     * 分配角色
     *
     * @param userId
     * @param roleList
     */
    public void assignRole(String userId, List<String> roleList) {
        // 清除现有的
        baseMapper.deleteRolesOfUser(userId);
        // 添加新的
        baseMapper.insertRolesToUser(roleList, userId);
    }

    /**
     * 根据用户id获取菜单。
     *
     * @param userId
     * @return
     */
    public List<Menu> findMenusByUserId(String userId) {
        // 若是超级管理员，则直接有所有权限，否则从数据库中取权限
        List<String> roles = listRolesByUserId(userId);
        if (roles.contains(CommonConstant.System.SUPER_ADMIN)) {
            return menuDao.selectList(Wrappers.lambdaQuery(Menu.class).orderByAsc(Menu::getSort));
        }
        return menuDao.selectMenusByUserId(userId);
    }

    /**
     * 下载模板
     *
     * @param userType
     * @param response
     */
    public void downloadTemplate(String userType, HttpServletResponse response) {
        String sheetName = "管理员账号";
        String fileName = "管理员账号导入模版.xlsx";
        BaseExcelHeader[] headers = TeacherExcelHeader.values();
        if (UserType.STUDENT.getValue().equals(userType)) {
            sheetName = "普通用户账号";
            fileName = "普通用户账号导入模版.xlsx";
            headers = StudentExcelHeader.values();
        }
        try (Workbook wb = new SXSSFWorkbook();
             ServletOutputStream outputStream = response.getOutputStream()) {
            Sheet sheet = wb.createSheet(sheetName);
            CellStyle headStyle = wb.createCellStyle();
            Font headFont = wb.createFont();
            headFont.setColor(IndexedColors.BLACK.getIndex());
            // headFont.setBold(true);
            headStyle.setFont(headFont);
            headStyle.setAlignment(HorizontalAlignment.CENTER);
            headStyle.setVerticalAlignment(VerticalAlignment.CENTER);
            Row headRow = sheet.createRow(0);
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headRow.createCell(i);
                cell.setCellValue(headers[i].getAlias());
                cell.setCellStyle(headStyle);
                sheet.setColumnWidth(i, headers[i].getWidth() * 256);
            }
            response.reset();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, StandardCharsets.UTF_8));
            wb.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.error("导出Excel失败", e);
            throw BusinessException.error(CharSequenceUtil.EMPTY, "导出模板失败：" + e.getMessage());
        }
    }

    /**
     * 解析导入文件
     *
     * @param userType
     * @param file
     * @return
     */
    public List<ImportUserOutput> resolveFile(String userType, MultipartFile file) {
        UserType type = Arrays.stream(UserType.values())
                .filter(t -> t.getValue().equals(userType))
                .findFirst().orElse(null);
        if (Objects.isNull(type)) {
            throw BusinessException.errorByMessage("上传失败，用户类型错误");
        }
        List<Map<String, Object>> mapList;
        try {
            mapList = xlsxFileRead(file.getInputStream(), -1, 0);
        } catch (IOException e) {
            throw BusinessException.errorByMessage("上传失败，数据格式有误，请按照模板要求填写");
        }
        mapList = mapList.stream()
                .filter(map -> map.values().stream()
                        .anyMatch(value -> StrValidator.isNotBlank(Objects.toString(value, CharSequenceUtil.EMPTY))))
                .toList();
        if (CollUtil.isEmpty(mapList)) {
            throw BusinessException.errorByMessage("上传失败，表格内没有有效的数据行!");
        }
        Set<String> set = mapList.get(0).keySet();
        BaseExcelHeader[] headers = type == UserType.TEACHER ? TeacherExcelHeader.values() : StudentExcelHeader.values();
        for (BaseExcelHeader header : headers) {
            if (!set.contains(header.getAlias())) {
                throw BusinessException.errorByMessage("上传失败，数据格式有误，请按照模板要求填写");
            }
        }
        List<ImportUserOutput> dataList = new ArrayList<>();
        for (Map<String, Object> map : mapList) {
            ImportUserOutput importUserOutput = new ImportUserOutput();
            for (BaseExcelHeader header : headers) {
                Object o = map.get(header.getAlias());
                String str = Objects.toString(o, null);
                str = CharSequenceUtil.trimToNull(str);
                FieldUtil.setFieldValue(importUserOutput, header.getProperty(), str);
            }
            importUserOutput.setUserType(userType);
            importUserOutput.setUserTypeText(type.getDescription());
            dataList.add(importUserOutput);
        }
        importCheck(dataList, type);
        return dataList;
    }

    private static final Pattern DATE_PATTERN = Pattern.compile("^\\d{4}-(0[1-9]|1[0-2])-(0[1-9]|[12]\\d|3[01])$");
    private static final Pattern MOBILE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    /**
     * 导入数据检查
     *
     * @param dataList
     * @param type
     */
    private void importCheck(List<ImportUserOutput> dataList, UserType type) {
        List<String> accounts = dataList.stream().map(ImportUserOutput::getAccount).distinct().toList();
        List<User> users = baseMapper.selectList(Wrappers.lambdaQuery(User.class).in(User::getAccount, accounts).eq(User::getUserType, type.getValue()));
        Set<String> existAccounts = users.stream().map(User::getAccount).collect(Collectors.toSet());
        Set<String> duplicateAccounts = dataList.stream()
                .filter(userOutput -> StrValidator.isNotBlank(userOutput.getAccount()))
                .collect(Collectors.groupingBy(ImportUserOutput::getAccount))
                .entrySet().stream()
                .filter(entry -> entry.getValue().size() > 1)
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        for (ImportUserOutput userOutput : dataList) {
            if (duplicateAccounts.contains(userOutput.getAccount())) {
                userOutput.setImportInstruction("账号在导入文件中重复");
                continue;
            }
            if (existAccounts.contains(userOutput.getAccount())) {
                userOutput.setImportInstruction("系统已存在相同账号");
                continue;
            }
            if (type == UserType.TEACHER) {
                if (!StrValidator.isAllNotBlank(userOutput.getName(), userOutput.getAccount())) {
                    userOutput.setImportInstruction("工号、姓名均不能为空");
                    continue;
                }
                if (StrValidator.isNotBlank(userOutput.getMobilePhone()) && !ReUtil.isMatch(MOBILE_PATTERN, userOutput.getMobilePhone())) {
                    userOutput.setImportInstruction("联系方式不是正确的手机号格式");
                    continue;
                }
            }
            if (type == UserType.STUDENT) {
                if (!StrValidator.isAllNotBlank(userOutput.getName(), userOutput.getAccount(), userOutput.getClasses())) {
                    userOutput.setImportInstruction("学号、姓名、班级均不能为空");
                    continue;
                }
                if (StrValidator.isNotBlank(userOutput.getValidDate()) && !ReUtil.isMatch(DATE_PATTERN, userOutput.getValidDate())) {
                    userOutput.setImportInstruction("失效日期格式错误");
                    continue;
                }
            }
            if (CharSequenceUtil.length(userOutput.getName()) > 50) {
                userOutput.setImportInstruction("姓名不允许超过50个字符");
                continue;
            }
            userOutput.setImportInstruction("正常");
        }
    }

    /**
     * excel文件读取
     *
     * @param file
     * @param sheet
     * @return
     */
    private static List<Map<String, Object>> xlsxFileRead(InputStream file, int sheet, int headRowIndex) {
        List<String> headList = new ArrayList<>();
        List<Map<String, Object>> mapList = new ArrayList<>();
        ExcelUtil.readBySax(file, sheet, (sheetIndex, rowIndex, rowCells) -> {
            if (rowIndex > headRowIndex) {
                mapList.add(IterUtil.toMap(headList, rowCells, true));
            } else if (rowIndex == headRowIndex && CollUtil.isEmpty(headList)) {
                headList.addAll(rowCells.stream().map(String::valueOf).toList());
            }
        });
        return mapList;
    }

    /**
     * 批量添加用户
     *
     * @param users
     */
    @Transactional
    public void batchAdd(List<UserInput> users) {
        if (CollUtil.isEmpty(users)) {
            return;
        }
        List<User> userList = BeanUtil.copyToList(users, User.class);
        for (User user : userList) {
            String salt = PasswordUtil.generateSalt();
            user.setSalt(salt);
            user.setPassword(PasswordUtil.encodePassword(CommonConstant.System.DEFAULT_PASSWORD, salt));
            user.setStatus(UserStatus.OK);
        }
        saveBatch(userList);

        // 处理班级信息
        List<String> classesName = users.stream().filter(user -> StrValidator.isNotBlank(user.getClasses()))
                // 只归属一个班级 暂时不做分割
                // .flatMap(user -> SplitUtil.split(user.getClasses(), StrPool.COMMA).stream())
                .map(UserInput::getClasses)
                .distinct()
                .toList();
        if (CollUtil.isEmpty(classesName)) {
            return;
        }
        LambdaQueryWrapper<Official> wrapper = Wrappers.lambdaQuery(Official.class)
                .in(Official::getName, classesName)
                .eq(Official::getType, OfficialType.CLASS);
        List<Official> officials = officialService.list(wrapper);
        List<String> toAddClasses = classesName;
        if (CollUtil.isNotEmpty(officials)) {
            List<String> existNames = officials.stream().map(Official::getName).toList();
            toAddClasses = classesName.stream().filter(name -> !existNames.contains(name)).toList();
        }
        if (CollUtil.isNotEmpty(toAddClasses)) {
            List<Official> officialList = toAddClasses.stream().map(className -> new Official().setName(className).setType(OfficialType.CLASS)).toList();
            officialService.saveBatch(officialList);
            officials.addAll(officialList);
        }
        Map<String, Official> officialMap = officials.stream().collect(Collectors.toMap(Official::getName, official -> official));
        Map<String, User> userMap = userList.stream().collect(Collectors.toMap(User::getAccount, user -> user));
        // 插入每个用户与班级的关联关系
        for (UserInput user : users) {
            if (StrValidator.isNotBlank(user.getClasses())) {
                String userId = userMap.get(user.getAccount()).getId();
                // String[] split = user.getClasses().split(StrPool.COMMA);
                String[] split = { user.getClasses() };
                Arrays.stream(split)
                        .map(className -> new UserOfficial().setUserId(userId).setOfficialId(officialMap.get(className).getId()))
                        .forEach(userOfficial -> userOfficialDao.insert(userOfficial));
            }
        }
    }

    /**
     * 修改用户密码
     *
     * @param id                 主键id
     * @param passwordResetInput 新密码参数对象
     */
    public void updatePassword(String id, PasswordResetInput passwordResetInput) {
        User user = getById(id);
        if (user == null) {
            throw BusinessException.errorByMessage("用户不存在");
        }
        user.setPassword(PasswordUtil.encodePassword(passwordResetInput.getPassword(), user.getSalt()));
        user.setIsDefaultPwd(false);
        baseMapper.updateById(user);
    }

    /**
     * 校验当前密码是否正确
     *
     * @param id       主键id
     * @param password 密码
     * @return boolean
     */
    public Boolean isPasswordCorrect(String id, String password) {
        User user = getById(id);
        if (user == null) {
            throw BusinessException.errorByMessage("用户不存在");
        }
        return PasswordUtil.matches(password, user.getSalt(), user.getPassword());
    }

    /**
     * 获取班级列表
     * @param className
     * @return
     */
    public List<String> getClassList(String className) {
        LambdaQueryWrapper<Official> wrapper = Wrappers.lambdaQuery(Official.class)
                .like(StrValidator.isNotBlank(className), Official::getName, className)
                .orderByAsc(Official::getName);
        List<Official> list = officialService.list(wrapper);
        if (CollUtil.isNotEmpty(list)) {
            return list.stream().map(Official::getName).toList();
        }
        return Collections.emptyList();
    }

    /**
     * 批量删除用户
     * @param ids
     */
    @Transactional
    public void deleteByIds(List<String> ids) {
        baseMapper.deleteUserByIds(ids);
        // 老师对应的课程信息暂时先逻辑删除，防止有问题找不到数据
        courseDao.delete(Wrappers.lambdaUpdate(Course.class).in(Course::getUserId, ids));
        List<Lesson> lessons = lessonDao.selectList(Wrappers.lambdaQuery(Lesson.class).in(Lesson::getStudentId, ids));
        if (CollUtil.isNotEmpty(lessons)) {
            List<String> lessonIds = lessons.stream().map(Lesson::getId).toList();
            lessonDao.deleteLessonByIds(lessonIds);
            courseLessonDao.deleteByLessonIds(lessonIds);
        }
        // 删除用户的实验数据
        ids.forEach(id ->  experimentService.deleteExperimentByUserId(id, true));
    }
}
