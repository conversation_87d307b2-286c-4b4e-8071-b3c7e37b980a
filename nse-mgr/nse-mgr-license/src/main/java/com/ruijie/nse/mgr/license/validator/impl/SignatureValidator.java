package com.ruijie.nse.mgr.license.validator.impl;

import com.ruijie.nse.mgr.license.exception.LicenseException;
import com.ruijie.nse.mgr.license.pojo.License;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Hex;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.HexFormat;

/**
 * License签名验证器
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class SignatureValidator {


    // HMAC-SHA256完整性校验
    private static final String HASH_ALGORITHM = "SHA-256";


    /**
     * 验证License签名
     * 
     * @param license License对象
     * @return 签名是否有效
     */
    public boolean valid(License license) {
        try {
            if (license.getCdata() == null || license.getCdata().getSignature() == null) {
                log.warn("License内容有误！！！: {}", license.getActivationCode());
                return false;
            }

            checkContentSignature(license);
            return true;

        } catch (Exception e) {
            log.error("License签名验证失败: {}", license.getActivationCode(), e);
            return false;
        }
    }

    /**
     * 生成内容签名
     *
     * @param license License载荷
     * @return 内容签名
     */
    private void checkContentSignature(License license) {
        try {
            String payloadJson = JSONUtil.toJsonStr(license.getCdata().getPayload());
            String headerJson = JSONUtil.toJsonStr(license.getCdata().getHeader());
            MessageDigest digest = MessageDigest.getInstance(HASH_ALGORITHM);

            //
            String contentToSign = headerJson + "|" + license.getCdata().getFinger() + "|" + payloadJson + "|" + license.getCdata().getFinger();
            log.debug("[license file 校验] - 待签名内容: {}", contentToSign);
            byte[] hash = digest.digest(contentToSign.getBytes(StandardCharsets.UTF_8));
            String formatHex = Hex.encodeHexString(hash);

            if(formatHex.equals(license.getCdata().getSignature())) {
                return ;
            }

            throw new LicenseException.LicenseSignatureInvalidException(license.getActivationCode());

        } catch (Exception e) {
            throw new RuntimeException("[license file 校验] - 生成内容签名失败", e);
        }
    }
}
