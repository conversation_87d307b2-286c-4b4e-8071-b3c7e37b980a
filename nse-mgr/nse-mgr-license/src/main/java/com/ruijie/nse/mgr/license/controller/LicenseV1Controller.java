package com.ruijie.nse.mgr.license.controller;

import com.ruijie.nse.common.dto.PageInput;
import com.ruijie.nse.common.dto.PageOutput;
import com.ruijie.nse.common.dto.R;
import com.ruijie.nse.common.utils.oshi.HardwareUtil;
import com.ruijie.nse.mgr.license.context.LicenseContext;
import com.ruijie.nse.mgr.license.pojo.output.LicenseActivationInfoOutput;
import com.ruijie.nse.mgr.license.pojo.output.LicenseImportOutput;
import com.ruijie.nse.mgr.license.service.LicenseActivationInfoService;
import com.ruijie.nse.mgr.license.service.LicenseV1Service;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * License管理控制器
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@RestController
@RequestMapping("/api/mgr/license")
@RequiredArgsConstructor
public class LicenseV1Controller {

    private final LicenseV1Service licenseService;
    private final LicenseActivationInfoService licenseActivationInfoService;

    /**
     * 获取License状态
     * @return License状态
     */
    @GetMapping("/status")
    public R<String> status() {
        return R.success(licenseActivationInfoService.getCurrentStatus());
    }


    @GetMapping("/hwinfo")
    public R<String> hwinfo() {
        return R.success(HardwareUtil.generateMachineCode());
    }


    @PostMapping(value = "activate")
    public R<LicenseImportOutput> activate(MultipartFile file) {
        LicenseImportOutput output = licenseService.importLicense(file, "初次授权");
        LicenseContext.setValid(true);
        return R.success(output);
    }


    @PostMapping("page")
    public R<PageOutput<LicenseActivationInfoOutput>> page(PageInput pageInput) {
        return R.success(licenseActivationInfoService.findPage(pageInput));
    }

    /**
     * 更新license
     * @param file license文件
     * @return 更新结果
     */
    @PutMapping(value = "update")
    public R<LicenseImportOutput> update(MultipartFile file) {
        log.info("更新License文件: {}", file.getOriginalFilename());
        LicenseImportOutput output = licenseService.importLicense(file, "更新授权");
        LicenseContext.setValid(true);
        return R.success(output);
    }

    /**
     * 注销license
     * @return 注销结果
     */
    @GetMapping("/cancel")
    public R<String> cancel() {
        log.info("注销License");
        return R.success(licenseService.cancelLicense());
    }
}
