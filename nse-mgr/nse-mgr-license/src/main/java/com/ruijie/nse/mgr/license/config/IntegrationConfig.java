package com.ruijie.nse.mgr.license.config;

import com.ruijie.nse.mgr.common.config.EventIntegrationProperties;
import com.ruijie.nse.mgr.common.dto.EventMessage;
import com.ruijie.nse.mgr.license.service.EventVerifyRecordsService;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.util.EnumUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.integration.annotation.Router;
import org.springframework.integration.annotation.ServiceActivator;
import org.springframework.integration.channel.DirectChannel;
import org.springframework.integration.channel.PublishSubscribeChannel;
import org.springframework.integration.channel.QueueChannel;
import org.springframework.integration.config.EnableIntegration;
import org.springframework.integration.router.AbstractMessageRouter;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.MessageHandler;
import org.springframework.messaging.MessagingException;

import java.util.Collection;
import java.util.Collections;

@Slf4j
@Configuration
@EnableIntegration
@RequiredArgsConstructor
public class IntegrationConfig {

    private final EventVerifyRecordsService eventVerifyRecordsService;
    private final EventIntegrationProperties properties;

    @Bean
    public MessageChannel eventInputChannel() {
        return new DirectChannel();
    }

    /**
     * 设置处理队列
     * @return
     */
    @Bean
    public MessageChannel eventProcessingChannel() {
        int capacity = properties.getQueue().getCapacity();
        return new QueueChannel(capacity);
    }

    @Bean
    public MessageChannel eventPersistenceChannel() {
        return new DirectChannel();
    }

    @Bean
    public MessageChannel highPriorityChannel() {
        return new DirectChannel(); // 高优先级事件直接处理通道
    }

    // 优化后的消息路由：根据事件类型实现真正的路由逻辑
    @Bean
    @Router(inputChannel = "eventInputChannel")
    public AbstractMessageRouter eventRouter() {
        return new AbstractMessageRouter() {
            @Override
            protected Collection<MessageChannel> determineTargetChannels(Message<?> message) {
                String eventType = (String) message.getHeaders().get("eventType");

                // 根据事件类型决定处理策略
                if (isHighPriorityEvent(eventType)) {
                    // 高优先级事件（威胁检测、系统故障）直接处理，绕过队列
                    return Collections.singletonList(highPriorityChannel());
                } else {
                    // 普通事件进入队列缓冲处理
                    return Collections.singletonList(eventProcessingChannel());
                }
            }
            
            /**
             * 判断是否为高优先级事件
             */
            private boolean isHighPriorityEvent(String eventType) {
                EventTypeEnum eventTypeEnum = EnumUtil.fromString(EventTypeEnum.class, eventType);
                return eventTypeEnum.getLevel().equalsIgnoreCase("ERROR");
            }
        };
    }

    // 队列处理器：处理队列中的普通事件
    @Bean
    @ServiceActivator(inputChannel = "eventProcessingChannel", 
                     outputChannel = "eventPersistenceChannel")
    public MessageHandler eventProcessor() {
        return message -> {
            EventMessage event = (EventMessage) message.getPayload();
            log.debug("Processing queued event: {}", event.getEventType());
            // 这里可以添加额外的处理逻辑，如事件过滤、转换等
            eventVerifyRecordsService.saveEventMessage(event);
        };
    }

    // 高优先级事件处理器：直接处理高优先级事件
    @Bean
    @ServiceActivator(inputChannel = "highPriorityChannel", 
                     outputChannel = "eventPersistenceChannel")
    public MessageHandler highPriorityEventProcessor() {
        return message -> {
            EventMessage event = (EventMessage) message.getPayload();
            log.debug("Processing high priority event immediately: {}", event.getEventType());
            // 高优先级事件的特殊处理逻辑
            eventVerifyRecordsService.saveEventMessage(event);
        };
    }

    // 持久化处理器
    @Bean
    @ServiceActivator(inputChannel = "eventPersistenceChannel")
    public MessageHandler eventPersistenceHandler() {
        return message -> {
            try {
                EventMessage event = (EventMessage) message.getPayload();
                eventVerifyRecordsService.saveEventMessage(event);
                log.info("Event persisted: {}", event.getEventType());
            } catch (Exception e) {
                log.error("Error persisting event: {}", e.getMessage());
                throw new MessagingException("Failed to persist event", e);
            }
        };
    }

    // 错误处理通道
    @Bean
    public MessageChannel errorChannel() {
        return new PublishSubscribeChannel();
    }

    @Bean
    @ServiceActivator(inputChannel = "errorChannel")
    public MessageHandler errorHandler() {
        return message -> {
            String logLevel = properties.getErrorHandling().getLogLevel();

            if (message.getPayload() instanceof MessagingException exception) {

                // 如果启用了错误通知，可以在这里添加通知逻辑
                if (properties.getErrorHandling().isSendNotification()) {
                    // TODO: 实现错误通知逻辑（邮件、短信等）
                    log.error("Error notification would be sent here");
                }
            }
        };
    }

}
