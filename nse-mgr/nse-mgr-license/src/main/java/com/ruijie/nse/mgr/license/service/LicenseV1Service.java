package com.ruijie.nse.mgr.license.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruijie.nse.common.utils.oshi.HardwareUtil;
import com.ruijie.nse.mgr.common.service.EventPublishService;
import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.license.context.LicenseContext;
import com.ruijie.nse.mgr.license.exception.LicenseException;
import com.ruijie.nse.mgr.license.parser.LicenseParserFactory;
import com.ruijie.nse.mgr.license.parser.handler.LicenseParser;
import com.ruijie.nse.mgr.license.pojo.License;
import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
import com.ruijie.nse.mgr.license.pojo.output.LicenseImportOutput;
import com.ruijie.nse.mgr.license.validator.LicenseValidator;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import com.ruijie.nse.mgr.repository.mapper.LicenseActivationInfoDao;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.exception.ExceptionUtil;
import org.dromara.hutool.core.io.file.PathUtil;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.StandardCopyOption;
import java.nio.file.attribute.PosixFilePermission;
import java.nio.file.attribute.PosixFilePermissions;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.Objects;
import java.util.Set;

import static com.ruijie.nse.mgr.license.constants.LicenseConstants.Format.EXTENSION_1_0;

/**
 * License服务实现类
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LicenseV1Service {

    private final LicenseValidator licenseValidator;
    private final LicenseParserFactory licenseParserFactory;
    private final LicenseActivationInfoDao licenseActivationInfoDao;
    private final EventPublishService eventPublishService;


    /**
     * 获取当前本机lic文件校验
     * @return
     */
    public LicenseValidationResult validateCurrentLicense() {
        Path licfPath = LicenseConstants.LicenseExt.LICF_PATH;
        if(!PathUtil.exists(licfPath, false)) {
            // 发送事件
            eventPublishService.publishEvent(EventTypeEnum.EVT_LIC_FILE_ERROR, "LIC文件不存在", null);

            return LicenseValidationResult.builder()
                    .valid(false)
                    .errorMessage("未找到有效的License")
                    .validationTime(new Date())
                    .build();

        }
        return this.validateCurrentLicense(
                licenseParserFactory.getParserByFormat(LicenseConstants.Format.VERSION_1_0).parse(licfPath.toFile())
        );
    }

    /**
     * 获取内存中license内容校验
     * @param license
     * @return
     */
    public LicenseValidationResult validateCurrentLicense(License license) {
        try {
            if(license == null || license.getCdata() == null) {
                // 发送事件
                eventPublishService.publishEvent(EventTypeEnum.EVT_LIC_CONTEXT_ERROR, "LIC上下文内容不存在", null);

                return LicenseValidationResult.builder()
                        .valid(false)
                        .errorMessage("未找到有效的License")
                        .validationTime(new Date())
                        .build();

            }
            return licenseValidator.validate(license);
        } catch (Exception e) {
            log.error("验证当前License失败", e);
            eventPublishService.publishLicenseVerifyErrorEvent("验证License失败", ExceptionUtil.getRootCauseMessage(e));
            return LicenseValidationResult.builder()
                    .valid(false)
                    .errorMessage("License验证异常: " + e.getMessage())
                    .validationTime(new Date())
                    .build();
        }

    }

    /**
     * 导入授权
     * @param licenseFile license文件
     * @return 操作结果
     */
    public LicenseImportOutput importLicense(MultipartFile licenseFile, String source) {

        try {
            // 是否已经激活过了
            long count = licenseActivationInfoDao.selectCount(Wrappers.lambdaQuery(LicenseActivationInfo.class)
                    .eq(LicenseActivationInfo::getMachineCode, HardwareUtil.getMachineCode())
                    .eq(LicenseActivationInfo::getStatus, LicenseConstants.Status.ACTIVE)
            );
            if(count > 0) {
                throw new LicenseException.LicenseValidationException("License已经激活过了，请勿重复导入");
            }

            Path licfPath = LicenseConstants.LicenseExt.LICF_PATH;
            // 处理导入或更新license的情况
            if(licenseFile.isEmpty() || !Objects.requireNonNull(licenseFile.getOriginalFilename()).endsWith(EXTENSION_1_0)) {
                throw new LicenseException.LicenseFormatException("不支持的文件格式");
            }

            // 解析License内容
            LicenseParser parser = licenseParserFactory.getParserByFormat(LicenseConstants.Format.VERSION_1_0);

            // 转存到/etc/.nse/.licf下
            if(!PathUtil.exists(licfPath, false)) {
                PathUtil.mkParentDirs(licfPath);
            }
            try (InputStream inputStream = licenseFile.getInputStream()) {
                Files.copy(inputStream, licfPath, StandardCopyOption.REPLACE_EXISTING);
                // 设置权限
                Set<PosixFilePermission> permissions = PosixFilePermissions.fromString("rw-r-----");
                Files.setPosixFilePermissions(licfPath, permissions);
            } catch (UnsupportedOperationException e) {
                log.warn("无法设置文件权限（非POSIX文件系统）");
            }

            License license = parser.parse(licfPath.toFile());
            // 校验该licese是否使用过了
            long usedCount = licenseActivationInfoDao.selectCount(Wrappers.lambdaQuery(LicenseActivationInfo.class)
                    .eq(LicenseActivationInfo::getSignature, license.getCdata().getSignature()));
            if(usedCount > 0) {
                throw new LicenseException.LicenseValidationException("该License已经使用过了，请勿重复导入");
            }

            // 保存License到数据库
            LicenseActivationInfo activationInfo = LicenseActivationInfo.builder()
                    .activationCode(license.getActivationCode())
                    .productInfo(license.getCdata().getPayload().getProduct().getName())
                    .permitMgrCnt(license.getCdata().getPayload().getRestrictions().getMgr())
                    .permitUserCnt(license.getCdata().getPayload().getRestrictions().getUser())
                    .status(LicenseConstants.Status.ACTIVE)
                    .validFrom(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(license.getCdata().getPayload().getValidity().getStart())), ZoneId.systemDefault() ))
                    .validTo(LocalDateTime.ofInstant(Instant.ofEpochMilli(Long.parseLong(license.getCdata().getPayload().getValidity().getEnd())), ZoneId.systemDefault() ))
                    .validType(license.getCdata().getPayload().getValidity().getValidType())
                    .activationTime(LocalDateTime.now())
                    .source(source)
                    .cancelCode(license.getCancelCode())
                    .machineCode(license.getCdata().getPayload().getRestrictions().getHardwareHash())
                    .signature(license.getCdata().getSignature()).build();

            licenseActivationInfoDao.insert(activationInfo);
            LicenseContext.setLicense(license);
            log.info("License导入成功: {}", license.getActivationCode());
            return LicenseImportOutput.of(license.getCdata().getPayload().getRestrictions().getMgr(), license.getCdata().getPayload().getRestrictions().getUser());

        } catch (Exception e) {
            log.error("导入License失败", e);
            throw new LicenseException.LicenseParseException("导入License失败: " + e.getMessage());
        }
    }


    /***
     * 注销授权
     * @return 操作结果
     */
    @Transactional
    public String cancelLicense() {
        try {
            // 获取当前可用的授权
            LicenseActivationInfo latestActivation = licenseActivationInfoDao.selectOne(Wrappers.lambdaQuery(LicenseActivationInfo.class)
                            .eq(LicenseActivationInfo::getMachineCode, HardwareUtil.getMachineCode())
                            .eq(LicenseActivationInfo::getStatus, LicenseConstants.Status.ACTIVE), false);
            if(Objects.isNull(latestActivation)) {
                throw new LicenseException.LicenseNotFoundException("未找到当前机器的有效授权");
            }
            // 更新数据库中的License状态为已注销
            // 需要需要将所有已授权的，都改为已注销
            licenseActivationInfoDao.updateStatusToCanceled();

            // 推送事件
            eventPublishService.publishEvent(EventTypeEnum.EVT_LIC_CANCEL, "lic授权已注销", EventTypeEnum.EVT_LIC_CANCEL.getLevel(), "lic授权已注销", null);

            // 获取出来注销码
            String cancelCode = latestActivation.getCancelCode();
            // 如果cancelCode为空，则从license文件获取
            if(StrUtil.isBlankIfStr(cancelCode)) {
                Path licfPath = LicenseConstants.LicenseExt.LICF_PATH;
                // 解析License内容
                LicenseParser parser = licenseParserFactory.getParserByFormat(LicenseConstants.Format.VERSION_1_0);
                cancelCode = parser.parse(licfPath.toFile()).getCancelCode();
            }

            LicenseContext.setValid(false);

            log.info("License注销成功");
            return cancelCode;
        } catch (Exception e) {
            log.error("注销License失败", e);
            throw new LicenseException.LicenseParseException("注销License失败: " + e.getMessage());
        }
    }
}
