package com.ruijie.nse.mgr.license.validator.impl;

import com.ruijie.nse.mgr.common.utils.SM2Utils;
import com.ruijie.nse.mgr.license.constants.LicenseConstants;
import com.ruijie.nse.mgr.license.exception.LicenseException;
import com.ruijie.nse.mgr.license.pojo.License;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.io.file.FileUtil;
import org.dromara.hutool.core.io.file.PathUtil;
import org.dromara.hutool.http.meta.HttpStatus;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.nio.file.Path;
import java.util.function.Supplier;

/**
 * License时间有效性验证器
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
public class TimeValidityValidator {

    /**
     * 验证License时间有效性
     * 
     * @param license License对象
     * @return 时间是否有效
     */
    public boolean valid(License license) {
        try {
            if (license.getCdata() == null ||
                license.getCdata().getPayload() == null ||
                license.getCdata().getPayload().getValidity() == null) {
                log.warn("License内容有误！！！: {}", license.getActivationCode());
                return false;
            }

            License.Cdata.Payload.Validity validity = license.getCdata().getPayload().getValidity();
            
            // 检查是否为永久License
            if ("永久".equals(validity.getValidType())) {
                log.debug("该License为永久有效: {}", license.getActivationCode());
                return true;
            }

            // 获取当前时间
            long currentDate = getLicenseCurrentDate();

            // 检查开始时间
            if (validity.getStart() != null && Long.parseLong(validity.getStart()) > currentDate) {
                log.debug("License尚未生效: {} - 开始时间: {}", license.getActivationCode(), validity.getStart());
                return false;
            }

            // 检查结束时间
            if (validity.getEnd() != null && Long.parseLong(validity.getEnd()) < currentDate) {
                log.debug("License已过期: {} - 结束时间: {}", license.getActivationCode(), validity.getEnd());
                throw new LicenseException.LicenseExpiredException(license.getActivationCode());
            }

            log.debug("License时间验证通过: {}", license.getActivationCode());
            return true;

        } catch (Exception e) {
            throw new RuntimeException("[license file 校验] - License时间验证失败", e);
        }
    }


    /**
     * 校验当前时间戳
     * @return
     */
    private long getLicenseCurrentDate() {
        try {
            // 读取.licd时间
            Path licdPath = LicenseConstants.LicenseExt.LICD_PATH;
            if(!PathUtil.isExistsAndNotDirectory(licdPath, false)) {
                throw new LicenseException.LicenseValidationException(LicenseConstants.Message.LICENSE_EXPIRED_OR_NOT_FOUNT);
            }
            String licd = FileUtil.readUtf8String(licdPath.toFile());
            if(licd == null || licd.trim().isEmpty()) {
                throw new LicenseException.LicenseValidationException(LicenseConstants.Message.LICENSE_EXPIRED_OR_NOT_FOUNT);
            }

            // rsa解密，正确的时间为一个long类型，为毫秒级时间戳。且时间与当前时间误差不能操作5分钟
            String decryptRsa = SM2Utils.decrypt(licd, SM2Utils.getPrivateKeyBase64(getKeyInputStream));
            return Long.parseLong(decryptRsa);

        } catch (Exception e) {
            throw new LicenseException(HttpStatus.HTTP_INTERNAL_ERROR, LicenseConstants.Message.LICENSE_EXPIRED_OR_NOT_FOUNT, e);
        }

    }



    /**
     * 获取resource下的public_key.pem的内容
     * 私钥加密
     * @return
     */
    public Supplier<InputStream> getKeyInputStream = () -> this.getClass().getResourceAsStream("/certs/public_key.pem");

}
