package com.ruijie.nse.mgr.license.validator;


import com.ruijie.nse.mgr.license.pojo.LicenseValidationResult;
import com.ruijie.nse.mgr.license.pojo.License;

/**
 * License验证器接口
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
public interface LicenseValidator {

    /**
     * 验证License
     *
     * @param license License对象
     * @return 验证结果
     */
    LicenseValidationResult validate(License license);

    /**
     * 验证License是否有效
     *
     * @param license License对象
     * @return 是否有效
     */
    boolean isValid(License license);

    /**
     * 验证License签名
     *
     * @param license License对象
     * @return 签名是否有效
     */
    boolean validateSignature(License license);

    /**
     * 验证License时间有效性
     *
     * @param license License对象
     * @return 时间是否有效
     */
    boolean validateTimeValidity(License license);

    /**
     * 验证硬件指纹
     *
     * @param license License对象
     * @return 硬件指纹是否匹配
     */
    boolean validateHardwareFingerprint(License license);


    /**
     * 验证IP地址限制
     *
     * @param license License对象
     * @param currentIp 当前IP地址
     * @return IP地址是否被允许
     */
    boolean validateIpRestriction(License license, String currentIp);

    /**
     * 获取验证器名称
     *
     * @return 验证器名称
     */
    String getValidatorName();

    /**
     * 获取验证器优先级
     *
     * @return 优先级（数字越小优先级越高）
     */
    int getPriority();
}
