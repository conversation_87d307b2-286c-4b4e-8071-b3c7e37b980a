package com.ruijie.nse.mgr.license.validator.impl;

import com.ruijie.nse.common.utils.oshi.HardwareUtil;
import com.ruijie.nse.mgr.license.exception.LicenseException;
import com.ruijie.nse.mgr.license.pojo.License;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.text.StrUtil;
import org.springframework.stereotype.Component;

/**
 * 硬件指纹验证器
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
@Slf4j
@Component
public class HardwareFingerprintValidator {

    /**
     * 验证硬件指纹
     * 
     * @param license License对象
     * @return 硬件指纹是否匹配
     */
    public boolean valid(License license) {
        try {
            if (license.getCdata() == null ||
                    license.getCdata().getPayload() == null ||
                    license.getCdata().getPayload().getRestrictions() == null ||
                    StrUtil.isBlankIfStr(license.getCdata().getPayload().getRestrictions().getHardwareHash())
            ) {
                log.warn("License内容有误！！！: {}", license.getActivationCode());
                return false;
            }

            String hardwareHash = license.getCdata().getPayload().getRestrictions().getHardwareHash();
            // 当前机器硬件指纹
            String currentFingerprint = HardwareUtil.generateMachineCode();

            if (currentFingerprint == null || currentFingerprint.trim().isEmpty()) {
                throw new LicenseException.HardwareFingerprintMismatchException();
            }

            boolean matches = hardwareHash.equalsIgnoreCase(currentFingerprint.trim());
            
            if (!matches) {
                throw new LicenseException.HardwareFingerprintMismatchException();
            }

            return true;

        } catch (Exception e) {
            throw new RuntimeException("[license file 校验] - 硬件指纹验证失败", e);
        }
    }

}
