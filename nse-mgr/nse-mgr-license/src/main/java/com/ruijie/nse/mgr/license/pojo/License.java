package com.ruijie.nse.mgr.license.pojo;

import lombok.*;
import org.dromara.hutool.core.text.StrUtil;

import java.util.List;
import java.util.Map;


/**
 * lic内容
 * <AUTHOR>
 * @date 2025-07-18
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public final class License {

    /**
     * 授权申请码
     */
    private String activationCode;
    /**
     * 注销码
     */
    private String cancelCode;

    /**
     * lic使用情况
     */
    private Useage useage;

    /**
     * lic具体内容
     */
    private Cdata cdata;



    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public final static class Useage {
        /**
         * lic状态. 0-未使用, 1-已使用, 2-已过期, 3-已撤销
         */
        private Integer status;
        /**
         * 使用时间
         */
        private String usetime;
        /**
         * 使用说明
         */
        private String usedetail;
    }


    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public final static class Cdata {

        /**
         * 动态指纹
         */
        private String finger;

        /**
         * cdata内容签名
         */
        private String signature;
        private Header header;
        private Payload payload;


        @Getter
        @Setter
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public final static class Header {
            private String format;
            private String issuer;
            private String issueDate;
        }

        @Getter
        @Setter
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        public final static class Payload {
            private String licenseId;
            private Product product;
            private Validity validity;
            private Restriction restrictions;

            private Map<String, Object> ext;


            @Getter
            @Setter
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public final static class Product {
                private String name;
                private String version;
                private List<String> modules;
            }

            @Getter
            @Setter
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public final static class Validity {
                private String start;
                private String end;
                private String validType;
            }

            @Getter
            @Setter
            @Builder
            @NoArgsConstructor
            @AllArgsConstructor
            public final static class Restriction {
                private Integer mgr;
                private Integer user;
                private String hardwareHash;
            }


            public boolean isValidPayload(){
                return getLicenseId() != null
                        && getProduct() != null
                        && getValidity() != null
                        && getRestrictions() != null;
            }

        }

    }


    public boolean isValidFormat() {
        return StrUtil.isNotBlank(getCdata().getFinger())
                && getCdata().getHeader() != null
                && getCdata().getPayload() != null && getCdata().getPayload().isValidPayload()
                && getCdata().getSignature() != null;
    }

}
