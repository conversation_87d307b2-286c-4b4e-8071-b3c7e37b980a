package com.ruijie.nse.mgr.common.utils;

import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.asn1.gm.GMNamedCurves;
import org.bouncycastle.asn1.x9.X9ECParameters;
import org.bouncycastle.crypto.engines.SM2Engine;
import org.bouncycastle.crypto.params.ECDomainParameters;
import org.bouncycastle.crypto.params.ECPrivateKeyParameters;
import org.bouncycastle.jcajce.provider.asymmetric.ec.BCECPrivateKey;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.dromara.hutool.core.io.IoUtil;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Security;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;
import java.util.function.Supplier;

/**
 * SM2工具类
 * 国密SM2非对称加密算法实现
 */
@Slf4j
public class SM2Utils {

    private static final String ALGORITHM = "EC";
    private static final String PROVIDER = "BC";
    private static final String CURVE_NAME = "sm2p256v1";
    
    static {
        // 添加BouncyCastle作为安全提供者
        if (Security.getProvider(PROVIDER) == null) {
            Security.addProvider(new BouncyCastleProvider());
        }
    }

    /**
     * 从PEM格式文件加载SM2私钥
     * @param supplier 输入流提供者
     * @return SM2私钥
     * @throws Exception 加载私钥异常
     */
    public static PrivateKey getPrivateKeyBase64(Supplier<InputStream> supplier) throws Exception {
        String pemContent = IoUtil.readUtf8(supplier.get())
                .replaceAll("-----.*-----", "").replaceAll("\\s", "");
        byte[] keyBytes = Base64.getDecoder().decode(pemContent);
        return KeyFactory.getInstance(ALGORITHM, PROVIDER)
                .generatePrivate(new PKCS8EncodedKeySpec(keyBytes));
    }

    /**
     * 使用SM2私钥解密数据
     * @param encryptedBase64 Base64编码的加密内容
     * @param privateKey SM2私钥
     * @return 解密后的明文
     */
    public static String decrypt(String encryptedBase64, PrivateKey privateKey) {
        try {
            if (privateKey == null) {
                throw new RuntimeException("未配置SM2私钥，无法解密内容");
            }

            X9ECParameters ecParameters = GMNamedCurves.getByName(CURVE_NAME);
            ECDomainParameters domainParameters = new ECDomainParameters(
                    ecParameters.getCurve(),
                    ecParameters.getG(),
                    ecParameters.getN(),
                    ecParameters.getH());

            // 转换为BC的私钥格式
            BCECPrivateKey bcecPrivateKey = (BCECPrivateKey) privateKey;
            ECPrivateKeyParameters privateKeyParameters = new ECPrivateKeyParameters(
                    bcecPrivateKey.getD(),
                    domainParameters);

            // 初始化SM2解密引擎
            SM2Engine engine = new SM2Engine(SM2Engine.Mode.C1C3C2);
            engine.init(false, privateKeyParameters);

            // 解码Base64密文
            byte[] encryptedBytes = Base64.getDecoder().decode(encryptedBase64);

            // 执行解密
            byte[] decryptedBytes = engine.processBlock(encryptedBytes, 0, encryptedBytes.length);
            return new String(decryptedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("SM2解密失败", e);
            throw new RuntimeException("License数据内容有误，请确认", e);
        }
    }
}