package com.ruijie.nse.mgr.common.job;

import com.ruijie.nse.mgr.common.handler.StudentLoginManager;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 学生登录管理定时任务
 */
@Slf4j
@Component
public class StudentLoginManagerJob {

    @Resource
    private StudentLoginManager studentLoginManager;

    /**
     * 每天凌晨0点执行，重置用户练习时长
     */
    @Scheduled(cron = "0 0 0 * * ?")
    public void resetExerciseTimeDaily() {
        studentLoginManager.resetExerciseTime();
    }

    /**
     * 每秒执行一次，处理队列超时数据
     */
    @Scheduled(fixedRate = 1000)
    public void processQueueTimeoutDataPerSecond() {
        studentLoginManager.processQueueTimeoutData();
    }
}
