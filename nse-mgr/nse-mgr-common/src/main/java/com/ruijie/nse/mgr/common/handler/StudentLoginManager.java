package com.ruijie.nse.mgr.common.handler;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ruijie.nse.common.constant.CacheConstants;
import com.ruijie.nse.common.exception.BusinessException;
import com.ruijie.nse.common.service.cache.EhcacheService;
import com.ruijie.nse.common.utils.enctry.JwtUtil;
import com.ruijie.nse.common.utils.oshi.HardwareUtil;
import com.ruijie.nse.mgr.common.constants.WebSocketConstants;
import com.ruijie.nse.mgr.common.dto.BaseQueueStudent;
import com.ruijie.nse.mgr.common.dto.QueuingStudent;
import com.ruijie.nse.mgr.common.dto.WebsocketMessage;
import com.ruijie.nse.mgr.repository.entity.LicenseActivationInfo;
import com.ruijie.nse.mgr.repository.entity.User;
import com.ruijie.nse.mgr.repository.entity.enums.UserType;
import com.ruijie.nse.mgr.repository.mapper.LicenseActivationInfoDao;
import com.ruijie.nse.mgr.repository.mapper.UserDao;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.dromara.hutool.core.collection.CollUtil;
import org.dromara.hutool.core.collection.ListUtil;
import org.dromara.hutool.core.collection.set.SetUtil;
import org.dromara.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.function.Consumer;
import java.util.stream.Stream;

/**
 * 学生登录管理
 */
@Slf4j
@Component
public class StudentLoginManager {

    /**
     * 每日限制登录时长
     */
    private static final Long EXERCISE_TIME_LIMIT = 2 * 60 * 60 * 1000L;

    /**
     * 强制退出延迟时长
     */
    private static final Long FORCED_EXIT_DELAY = 3 * 60 * 1000L;

    /**
     * 登录确认时长
     */
    private static final Long LOGIN_CONFIRM_DURATION = 5 * 60 * 1000L;

    /**
     * 上课的学生登录排队的队列
     */
    private static final CustomPriorityQueue<QueuingStudent> ongoingStudentQueuing = new CustomPriorityQueue<>(Comparator.comparingLong(BaseQueueStudent::getEnterQueueTime));

    /**
     * 排队成功待进入系统的用户队列
     */
    private static final CustomPriorityQueue<QueuingStudent> ongoingQueuingSuccessful = new CustomPriorityQueue<>(Comparator.comparingLong(BaseQueueStudent::getEnterQueueTime));

    /**
     * 即将强制退出的队列练习
     */
    private static final CustomPriorityQueue<BaseQueueStudent> exerciseForcedExit  = new CustomPriorityQueue<>(Comparator.comparingLong(BaseQueueStudent::getEnterQueueTime));

    /**
     * 练习的学生登录排队的队列
     */
    private static final CustomPriorityQueue<QueuingStudent> exerciseStudentQueuing = new CustomPriorityQueue<>(Comparator.comparingLong(BaseQueueStudent::getEnterQueueTime));

    /**
     * 排队成功待进入系统的用户队列
     */
    private static final CustomPriorityQueue<QueuingStudent> exerciseQueuingSuccessful = new CustomPriorityQueue<>(Comparator.comparingLong(BaseQueueStudent::getEnterQueueTime));

    /**
     * 练习队列
     */
    private static final CustomPriorityQueue<BaseQueueStudent> exerciseQueue = new CustomPriorityQueue<>(Comparator.comparingLong(BaseQueueStudent::getEnterQueueTime));

    /**
     * 用户今日使用时长
     */
    private static final Map<String, Long> todayUsageDuration = new ConcurrentHashMap<>();

    /**
     * 上课的用户id
     */
    private static final Set<String> ongoingUsers = new CopyOnWriteArraySet<>();

    // 用于执行延迟任务的调度器
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(10);

    @Resource
    private EhcacheService ehcacheService;

    @Resource
    private LicenseActivationInfoDao licenseActivationInfoDao;

    @Resource
    private UserDao userDao;

    /**
     * 并发限制数量
     */
    private int CONCURRENT_LIMIT;


    // private final Python3ServerLauncher python3ServerLauncher;

    @PostConstruct
    public void init() {
        // 获取最新一条激活信息
        String machineCode = HardwareUtil.getMachineCode();
        LambdaQueryWrapper<LicenseActivationInfo> wrapper = Wrappers.lambdaQuery(LicenseActivationInfo.class)
                .eq(LicenseActivationInfo::getMachineCode, machineCode)
                .gt(LicenseActivationInfo::getValidTo, LocalDateTime.now())
                .orderByDesc(LicenseActivationInfo::getActivationTime);
        List<LicenseActivationInfo> listed = licenseActivationInfoDao.selectList(wrapper);
        if(CollUtil.isNotEmpty(listed)) {
            CONCURRENT_LIMIT = listed.get(0).getPermitUserCnt();
        }
    }


    /**
     * 重置用户练习时长并更新练习队列中的入队时间
     */
    public void resetExerciseTime() {
        // 清空今日使用时长记录
        todayUsageDuration.clear();
        // 更新练习队列中所有用户的入队时间为当前时间
        exerciseQueue.forEach(student -> student.setEnterQueueTime(System.currentTimeMillis()));
        log.info("已重置所有用户的练习时长记录和队列入队时间");
    }

    /**
     * 处理队列超时数据
     */
    public void processQueueTimeoutData() {
        removeTimeoutElements(exerciseForcedExit, FORCED_EXIT_DELAY, students ->
                students.forEach(student -> {
                    saveStudentExerciseDuration(student.getId(), student.getEnterQueueTime(), false);
                    forceLogout(student.getId());
                })
        );

        removeTimeoutElements(exerciseQueue, EXERCISE_TIME_LIMIT, students ->
            students.forEach(student -> {
                saveStudentExerciseDuration(student.getId(), student.getEnterQueueTime(), true);
                forceLogout(student.getId());
            })
        );

        removeTimeoutElements(exerciseQueuingSuccessful, LOGIN_CONFIRM_DURATION, students -> {
            List<String> allUserIds = Stream.concat(
                    students.stream().map(BaseQueueStudent::getId),
                    exerciseStudentQueuing.stream().map(BaseQueueStudent::getId)
            ).toList();
            notifyStudentRefreshQueue(allUserIds, false);
        });

        removeTimeoutElements(ongoingQueuingSuccessful, LOGIN_CONFIRM_DURATION, students -> {
            List<String> allUserIds = Stream.concat(
                    students.stream().map(BaseQueueStudent::getId),
                    ongoingStudentQueuing.stream().map(BaseQueueStudent::getId)
            ).toList();
            notifyStudentRefreshQueue(allUserIds, true);
        });
    }


    /**
     * 获取学生登录的并发限制数量
     *
     * @return
     */
    public int getStudentLoginConcurrentLimitCount() {
       return CONCURRENT_LIMIT;
    }

    /**
     * 获取练习学生的登录数量
     *
     * @return 练习学生的数量
     */
    public int getExerciseStudentCount() {
        return exerciseQueue.size() + exerciseQueuingSuccessful.size();
    }

    /**
     * 获取学生剩余登录数量
     */
    public int studentRemainLoginCount() {
        return CONCURRENT_LIMIT - ongoingUsers.size() - getExerciseStudentCount();
    }

    /**
     * 获取正在练习的学生ID列表
     *
     * @return 正在练习的学生ID列表
     */
    public List<String> getExerciseStudentUserId() {
        List<String> userIds = new ArrayList<>();
        exerciseQueue.stream().map(BaseQueueStudent::getId).forEach(userIds::add);
        exerciseQueuingSuccessful.stream().map(BaseQueueStudent::getId).forEach(userIds::add);
        return userIds;
    }

    /**
     * 保存学生今日使用时长
     * @param userId
     * @param enterQueueTime
     * @param isOverride
     */
    public void saveStudentExerciseDuration(String userId, Long enterQueueTime, boolean isOverride)  {
        long duration = System.currentTimeMillis() - enterQueueTime;
        log.info("练习用户本次使用时长：{}", duration / 1000);
        if (!isOverride) {
            duration = duration + todayUsageDuration.getOrDefault(userId, 0L);
        }
        todayUsageDuration.put(userId, duration);
    }

    /**
     * 校验学生上课是否能够剩余并发
     * @param userIds 学生ID列表
     * @return 是否能够剩余并发
     */
    public List<String> addOngoingStudentCheck(List<String> userIds) {
        if (userIds.size() > CONCURRENT_LIMIT) {
            throw BusinessException.errorByMessage(String.format("所上课班级账号一共%s个将超出登录并发数量%s个，请确认", userIds.size(), CONCURRENT_LIMIT));
        }

        // 需要排除同一个用户在其他已上课的课程内的，则不需要暂用多余的并发数， 剩下的则为需要的并发数
        List<String> toBeOngoingUserIds = userIds.stream()
                .filter(userId -> !ongoingUsers.contains(userId))
                .toList();

        int availableOngoingNum = CONCURRENT_LIMIT - ongoingUsers.size();
        if (ongoingUsers.size() + toBeOngoingUserIds.size() > CONCURRENT_LIMIT) {
            throw BusinessException.errorByMessage(String.format("所上课班级账号一共%d个将超出剩余登录并发数量%d个，请确认。", userIds.size(), availableOngoingNum));
        }
        return toBeOngoingUserIds;
    }

    /**
     * 添加上课的学生
     * @param userIds
     */
    public void addOngoingStudent(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        List<String> toBeOngoingUserIds = addOngoingStudentCheck(userIds);
/*

        if (userIds.size() > CONCURRENT_LIMIT) {
            throw BusinessException.errorByMessage(String.format("所上课班级账号一共%s个将超出登录并发数量%s个，请确认", userIds.size(), CONCURRENT_LIMIT));
        }

        // 需要排除同一个用户在其他已上课的课程内的，则不需要暂用多余的并发数， 剩下的则为需要的并发数
        List<String> toBeOngoingUserIds = userIds.stream()
                .filter(userId -> !ongoingUsers.contains(userId))
                .toList();

        int availableOngoingNum = CONCURRENT_LIMIT - ongoingUsers.size();
        if (ongoingUsers.size() + toBeOngoingUserIds.size() > CONCURRENT_LIMIT) {
            throw BusinessException.errorByMessage(
                    String.format("所上课班级账号一共%d个将超出剩余登录并发数量%d个，请确认。", userIds.size(), availableOngoingNum)
            );
        }
*/

        List<String> refreshStatusUserIds = ListUtil.zero();
        // 本次上课的学生是否存在练习即将强退的学生队列
        List<BaseQueueStudent> exerciseForcedExitStudent = exerciseForcedExit.stream()
                .filter(user -> toBeOngoingUserIds.contains(user.getId()))
                .toList();
        if (CollUtil.isNotEmpty(exerciseForcedExitStudent)) {
            for (BaseQueueStudent baseQueueStudent : exerciseForcedExitStudent) {
                exerciseForcedExit.remove(baseQueueStudent);
                saveStudentExerciseDuration(baseQueueStudent.getId(), baseQueueStudent.getEnterQueueTime(), false);
                refreshStatusUserIds.add(baseQueueStudent.getId());
            }
        }

        // 本次上课的学生是否存在练习队列
        List<BaseQueueStudent> exerciseStudent = exerciseQueue.stream()
                .filter(user -> toBeOngoingUserIds.contains(user.getId()))
                .toList();
        if (CollUtil.isNotEmpty(exerciseStudent)) {
            for (BaseQueueStudent baseQueueStudent : exerciseStudent) {
                exerciseQueue.remove(baseQueueStudent);
                saveStudentExerciseDuration(baseQueueStudent.getId(), baseQueueStudent.getEnterQueueTime(), true);
                refreshStatusUserIds.add(baseQueueStudent.getId());
            }
        }
        // 通知前端隐藏练习时长 转为上课学生
        notifyStudent(refreshStatusUserIds, WebsocketMessage.create(WebSocketConstants.REFRESH_STATUS_EVENT));

        // 本次上课学生是否存在即将登录的练习队列中
        List<QueuingStudent> exerciseQueuingSuccessfulStudent = exerciseQueuingSuccessful.stream()
                .filter(user -> toBeOngoingUserIds.contains(user.getId()))
                .toList();
        if (CollUtil.isNotEmpty(exerciseQueuingSuccessfulStudent)) {
            exerciseQueuingSuccessfulStudent.forEach(exerciseQueuingSuccessful::remove);
            ongoingQueuingSuccessful.addAll(exerciseQueuingSuccessfulStudent);
        }

        // 计算当前可用的练习并发数 = 总并发 - 已上课的学生数 - 本次上课需要的学生并发数
        int availableExerciseNum = CONCURRENT_LIMIT - ongoingUsers.size() - toBeOngoingUserIds.size();
        if (availableExerciseNum <= 0) {
            // 正在练习的学生数全部转移到强制退出的练习队列中 计算已用的今日练习时长保存
            List<String> notifyUserIds = exerciseQueue.stream().map(BaseQueueStudent::getId).toList();
            for (BaseQueueStudent baseQueueStudent : exerciseQueue) {
                saveStudentExerciseDuration(baseQueueStudent.getId(), baseQueueStudent.getEnterQueueTime(), true);
                exerciseQueue.remove(baseQueueStudent);
                baseQueueStudent.setEnterQueueTime(System.currentTimeMillis());
                exerciseForcedExit.add(baseQueueStudent);
            }
            notifyStudent(notifyUserIds, WebsocketMessage.create(WebSocketConstants.COMING_LOGOUT_EVENT));

            // 即将登录的练习学生统一提示无法登录了，并清空队列
            notifyUserIds = exerciseQueuingSuccessful.stream().map(BaseQueueStudent::getId).toList();
            exerciseQueuingSuccessful.clear();

            // 在练习队列中的学生全部结束排队提示无法登录，并清空队列
            SetUtil.of(notifyUserIds).addAll(exerciseStudentQueuing.stream().map(BaseQueueStudent::getId).toList());
            exerciseStudentQueuing.clear();
            notifyStudent(notifyUserIds, WebsocketMessage.create(WebSocketConstants.CANCEL_QUEUE_EVENT, "老师上课中，暂无无法登录，请非上课时间再尝试！"));

        } else {
            // 计算当前练习的学生数 = 练习队列学生数 + 即将登录的练习队列数
            int exerciseNum = exerciseQueue.size() + exerciseQueuingSuccessful.size();
            if (exerciseNum > availableExerciseNum) {
                // 计算需要踢出的学生数量 = 当前练习的学生数 - 可以的练习并发数
                int needKickNum = exerciseNum - availableExerciseNum;

                // 计算从练习队列踢出数量 = 需要踢出的学生数 >= 练习队列数量 ？ 练习队列数 ：需要踢出的数
                int needKickExerciseNum = needKickNum >= exerciseQueue.size() ? exerciseQueue.size() : needKickNum;

                // 从练习队列踢出指定数量的练习学生到即将待强制退出的队列中
                for (int i = 0; i < needKickExerciseNum; i++) {
                    BaseQueueStudent baseQueueStudent = exerciseQueue.poll();
                    if (Objects.isNull(baseQueueStudent)) {
                        break;
                    }
                    saveStudentExerciseDuration(baseQueueStudent.getId(), baseQueueStudent.getEnterQueueTime(), true);
                    // 计算剩余时长为练习时长限制 - 已用的时长
                    Long availableDuration = EXERCISE_TIME_LIMIT - todayUsageDuration.get(baseQueueStudent.getId());
                    // 剩余的练习时长是否小于等于强退倒计时时长？
                    if (availableDuration <= FORCED_EXIT_DELAY) {
                        // 则加入强制退出的队列时间 =
                        // 当前时间 -（强制退出倒计时时长 - 剩余的练习时长）
                        baseQueueStudent.setEnterQueueTime(System.currentTimeMillis() - (FORCED_EXIT_DELAY - availableDuration));
                    } else {
                        baseQueueStudent.setEnterQueueTime(System.currentTimeMillis());
                        notifyStudent(baseQueueStudent.getId(), WebsocketMessage.create(WebSocketConstants.COMING_LOGOUT_EVENT));
                    }

                    exerciseForcedExit.add(baseQueueStudent);
                }

                // 再从即将登录的练习队列中尾部退回指定的数量学生到练习排队的队列
                if (needKickNum - needKickExerciseNum > 0) {
                    List<String> notifyUserIds = ListUtil.zero();
                    for (int i = 0; i < needKickNum - needKickExerciseNum; i++) {
                        QueuingStudent queuingStudent = exerciseQueuingSuccessful.pollLast();
                        if (queuingStudent == null) {
                            break;
                        }
                        queuingStudent.setQueueSuccessful(false);
                        exerciseStudentQueuing.add(queuingStudent);
                        notifyUserIds.add(queuingStudent.getId());
                    }
                    notifyStudentRefreshQueue(notifyUserIds, false);
                }
            }
        }
        // 加入到上课的用户信息集合中
        ongoingUsers.addAll(userIds);

        // 本次上课学生是否子在练习的排队队列中？
        List<QueuingStudent> exerciseQueuing = exerciseStudentQueuing.stream()
                .filter(user -> userIds.contains(user.getId()))
                .toList();
        // 加入到上课的排队队列中
        ongoingStudentQueuing.addAll(exerciseQueuing);
    }

    /**
     * 结束上课的学生
     * @param userIds
     */
    public void endOngoingStudent(List<String> userIds) {
        if (CollUtil.isEmpty(userIds)) {
            return;
        }
        List<String> onlineStudentIds = getOnlineStudentIds();
        for (String userId : userIds) {
            if (onlineStudentIds.contains(userId)) {
                // 获取一下今日该用户已使用的练习时长
                Long usageDuration = todayUsageDuration.getOrDefault(userId, 0L);
                // 练习时长是否大于等于每日限制时长
                if (usageDuration >= EXERCISE_TIME_LIMIT) {
                    forceLogout(userId);
                } else {
                    long count = exerciseQueue.stream().filter(user -> user.getId().equals(userId)).count();
                    if (count == 0) {
                        BaseQueueStudent baseQueueStudent = new BaseQueueStudent();
                        baseQueueStudent.setEnterQueueTime(System.currentTimeMillis() - usageDuration);
                        baseQueueStudent.setId(userId);
                        exerciseQueue.add(baseQueueStudent);
                        notifyStudent(userId, WebsocketMessage.create(WebSocketConstants.REFRESH_STATUS_EVENT));
                    }
                }
                continue;
            }

            // 是否存在即将登录倒计时队列中？
            List<QueuingStudent> baseQueueStudents = ongoingQueuingSuccessful.stream()
                    .filter(user -> user.getId().equals(userId))
                    .toList();
            if (CollUtil.isNotEmpty(baseQueueStudents)) {
                for (QueuingStudent baseQueueStudent : baseQueueStudents) {
                    ongoingQueuingSuccessful.remove(baseQueueStudent);
                    if (todayUsageDuration.getOrDefault(baseQueueStudent.getId(), 0L) >= CONCURRENT_LIMIT) {
                        notifyStudent(baseQueueStudent.getId(), WebsocketMessage.create(WebSocketConstants.CANCEL_QUEUE_EVENT, "当日使用时长已用完，请明天再尝试！"));
                        continue;
                    }
                    exerciseQueuingSuccessful.add(baseQueueStudent);
                }
                continue;
            }

            // 是否存在排队的队列中？
            baseQueueStudents = ongoingStudentQueuing.stream()
                    .filter(user -> user.getId().equals(userId))
                    .toList();
            if (CollUtil.isNotEmpty(baseQueueStudents)) {
                for (QueuingStudent baseQueueStudent : baseQueueStudents) {
                    ongoingStudentQueuing.remove(baseQueueStudent);
                    if (todayUsageDuration.getOrDefault(baseQueueStudent.getId(), 0L) >= CONCURRENT_LIMIT) {
                        notifyStudent(baseQueueStudent.getId(), WebsocketMessage.create(WebSocketConstants.CANCEL_QUEUE_EVENT, "当日使用时长已用完，请明天再尝试！"));
                        continue;
                    }
                    exerciseStudentQueuing.add(baseQueueStudent);
                }
            }
        }
        ongoingUsers.removeAll(userIds);

        List<String> notifyUserIds = exerciseStudentQueuing.stream().map(BaseQueueStudent::getId).toList();
        notifyStudentRefreshQueue(notifyUserIds, false);
    }

    /**
     * 学生登录检查
     * @param userId
     */
    public void studentLoginCheck(String userId) {
        List<String> onlineStudentIds = getOnlineStudentIds();
        if (onlineStudentIds.contains(userId)) {
            return;
        }
        User student = userDao.selectById(userId);
        if (ongoingUsers.contains(userId)) {
            List<QueuingStudent> queuingStudents = ongoingQueuingSuccessful.stream().filter(user -> user.getId().equals(userId)).toList();
            if (CollUtil.isNotEmpty(queuingStudents)) {
                ongoingQueuingSuccessful.removeAll(queuingStudents);
                return;
            }
            long count = ongoingStudentQueuing.stream().filter(user -> user.getId().equals(userId)).count();
            if (count > 0) {
                refreshOngoingStudentQueuing();
                throw BusinessException.errorByMessage("正在排队");
            }
            List<String> ids = onlineStudentIds.stream().filter(id -> ongoingUsers.contains(userId)).toList();
            if (ids.size() + ongoingQueuingSuccessful.size() + exerciseForcedExit.size() >= ongoingUsers.size()) {
                QueuingStudent queuingStudent = new QueuingStudent();
                queuingStudent.setId(userId);
                queuingStudent.setEnterQueueTime(System.currentTimeMillis());
                queuingStudent.setDoLoginTime(System.currentTimeMillis());
                queuingStudent.setName(student.getName());
                queuingStudent.setAccount(student.getAccount());
                ongoingStudentQueuing.add(queuingStudent);
                refreshOngoingStudentQueuing();
                throw BusinessException.errorByMessage("正在排队");
            }
        } else {
            if (ongoingUsers.size() >= CONCURRENT_LIMIT) {
                throw BusinessException.errorByMessage("老师上课中，暂无法登录，请非上课时间再尝试！");
            }
            if (todayUsageDuration.getOrDefault(userId, 0L) >= EXERCISE_TIME_LIMIT) {
                throw BusinessException.errorByMessage("当日使用时长已用完，请明天再尝试！");
            }
            List<QueuingStudent> queuingStudents = exerciseQueuingSuccessful.stream().filter(user -> user.getId().equals(userId)).toList();
            if (CollUtil.isNotEmpty(queuingStudents)) {
                exerciseQueuingSuccessful.removeAll(queuingStudents);
                return;
            }
            long count = exerciseStudentQueuing.stream().filter(user -> user.getId().equals(userId)).count();
            if (count > 0) {
                refreshExerciseStudentQueuing();
                throw BusinessException.errorByMessage("正在排队");
            }
            // 练习队列数量 + 即将登录的队列 大于等于 总并发数 - 上课集合学生数量？
            if (exerciseQueue.size() + exerciseQueuingSuccessful.size() >= CONCURRENT_LIMIT - ongoingUsers.size()) {
                QueuingStudent queuingStudent = new QueuingStudent();
                queuingStudent.setId(userId);
                queuingStudent.setEnterQueueTime(System.currentTimeMillis());
                queuingStudent.setDoLoginTime(System.currentTimeMillis());
                queuingStudent.setName(student.getName());
                queuingStudent.setAccount(student.getAccount());
                exerciseStudentQueuing.add(queuingStudent);
                refreshExerciseStudentQueuing();
                throw BusinessException.errorByMessage("正在排队");
            }
        }
    }

    /**
     * 学生进入系统时调用的方法
     * @param userId 用户ID
     * @return 如果是上课学生返回null，如果是练习学生返回练习开始时间
     */
    public Long studentEnterSystem(String userId) {
        // 如果是上课学生，直接返回null
        if (ongoingUsers.contains(userId)) {
            return null;
        }
        
        // 查找是否在练习队列中
        Optional<BaseQueueStudent> existingStudent = exerciseQueue.stream()
                .filter(student -> student.getId().equals(userId))
                .findFirst();
        if (existingStudent.isPresent()) {
            return existingStudent.get().getEnterQueueTime();
        }
        existingStudent = exerciseForcedExit.stream()
                .filter(student -> student.getId().equals(userId))
                .findFirst();
        if (existingStudent.isPresent()) {
            Long enterQueueTime = existingStudent.get().getEnterQueueTime();
            return enterQueueTime + FORCED_EXIT_DELAY - EXERCISE_TIME_LIMIT;
        }

        Long usedDuration = todayUsageDuration.getOrDefault(userId, 0L);
        long exerciseStartTime = System.currentTimeMillis() - usedDuration;
        BaseQueueStudent newStudent = new BaseQueueStudent();
        newStudent.setId(userId);
        newStudent.setEnterQueueTime(exerciseStartTime);
        exerciseQueue.add(newStudent);
        return exerciseStartTime;
    }

    /**
     * 学生登出
     * @param userId
     */
    public void studentLogout(String userId) {
        if (ongoingUsers.contains(userId)) {
            if (!ongoingStudentQueuing.isEmpty()) {
                List<String> userIds = ongoingStudentQueuing.stream().map(BaseQueueStudent::getId).toList();
                notifyStudentRefreshQueue(userIds, true);
            }
        } else {
            List<BaseQueueStudent> baseQueueStudents = exerciseQueue.stream().filter(user -> user.getId().equals(userId)).toList();
            for (BaseQueueStudent baseQueueStudent : baseQueueStudents) {
                saveStudentExerciseDuration(userId, baseQueueStudent.getEnterQueueTime(), true);
                exerciseQueue.remove(baseQueueStudent);
            }
            List<BaseQueueStudent> forcedExitStudents = exerciseForcedExit.stream().filter(user -> user.getId().equals(userId)).toList();
            for (BaseQueueStudent baseQueueStudent : forcedExitStudents) {
                saveStudentExerciseDuration(userId, baseQueueStudent.getEnterQueueTime(), false);
                exerciseForcedExit.remove(baseQueueStudent);
            }
            if (!exerciseStudentQueuing.isEmpty() && CollUtil.isNotEmpty(baseQueueStudents)) {
                List<String> userIds = exerciseStudentQueuing.stream().map(BaseQueueStudent::getId).toList();
                notifyStudentRefreshQueue(userIds, false);
            }
            if (!ongoingStudentQueuing.isEmpty() && CollUtil.isNotEmpty(forcedExitStudents)) {
                List<String> userIds = ongoingStudentQueuing.stream().map(BaseQueueStudent::getId).toList();
                notifyStudentRefreshQueue(userIds, true);
            }
        }
    }

    public void addExcitingStudent(String userId) {
        BaseQueueStudent baseQueueStudent = new BaseQueueStudent();
        baseQueueStudent.setEnterQueueTime(System.currentTimeMillis());
        baseQueueStudent.setId(userId);
        exerciseQueue.add(baseQueueStudent);
    }


    /**
     * 刷新上课学生队列
     */
    public void refreshOngoingStudentQueuing() {
        if (ongoingStudentQueuing.isEmpty()) {
            return;
        }
        // 做一次强制退出exerciseForcedExit内的用户，防止超时的用户还在里面
        removeTimeoutElements(exerciseForcedExit, FORCED_EXIT_DELAY, students ->
                students.forEach(student -> {
                    saveStudentExerciseDuration(student.getId(), student.getEnterQueueTime(), false);
                    forceLogout(student.getId());
                })
        );
        if (exerciseForcedExit.size() < ongoingStudentQueuing.size()) {
            // 计算获取可进入登录的学生数
            int count = ongoingStudentQueuing.size() - exerciseForcedExit.size();
            // 从上课学生排队的队列数头部循环n次移入到即将登录的学生队列内
            for (int i = 0; i < count; i++) {
                QueuingStudent queuingStudent = ongoingStudentQueuing.poll();
                if (queuingStudent == null) {
                    break;
                }
                queuingStudent.setEnterQueueTime(System.currentTimeMillis());
                queuingStudent.setQueueSuccessful(true);
                ongoingQueuingSuccessful.add(queuingStudent);
            }
        }
        if (ongoingStudentQueuing.isEmpty()) {
            return;
        }
        for (int i = 0; i < ongoingStudentQueuing.size(); i++) {
            QueuingStudent queuingStudent = ongoingStudentQueuing.get(i);
            queuingStudent.setWaitingCount(i+1);
            queuingStudent.setTotalWaitingCount(ongoingStudentQueuing.size());
            long expectedLoginTime = Optional.ofNullable(exerciseForcedExit.get(i))
                    .map(BaseQueueStudent::getEnterQueueTime)
                    .orElse(System.currentTimeMillis()) + FORCED_EXIT_DELAY;
            queuingStudent.setExpectedLoginTime(expectedLoginTime);
        }
    }

    /**
     * 刷新练习的学生排队队列
     */
    public void refreshExerciseStudentQueuing() {
        if (exerciseStudentQueuing.isEmpty()) {
            return;
        }

        // 计算练习可用的并发数量
        int availableNum = CONCURRENT_LIMIT - ongoingUsers.size();
        if (exerciseQueue.size() + exerciseQueuingSuccessful.size() < availableNum) {
            // 计算可进入即将登录的人数
            int count = availableNum - exerciseQueue.size() - exerciseQueuingSuccessful.size();
            for (int i = 0; i < count; i++) {
                QueuingStudent queuingStudent = exerciseStudentQueuing.poll();
                if (queuingStudent == null) {
                    break;
                }
                queuingStudent.setEnterQueueTime(System.currentTimeMillis());
                queuingStudent.setQueueSuccessful(true);
                exerciseQueuingSuccessful.add(queuingStudent);
            }
        }
        if (exerciseStudentQueuing.isEmpty()) {
            return;
        }

        List<Long> existTimes = new ArrayList<>();
        exerciseQueue.forEach(baseQueueStudent -> existTimes.add(baseQueueStudent.getEnterQueueTime() + EXERCISE_TIME_LIMIT));
        exerciseQueuingSuccessful.forEach(baseQueueStudent -> existTimes.add(baseQueueStudent.getEnterQueueTime() + LOGIN_CONFIRM_DURATION + EXERCISE_TIME_LIMIT));

        for (int i = 0; i < exerciseStudentQueuing.size(); i++) {
            // 计算轮次和余数
            int round = i / existTimes.size();
            int remainder = i % existTimes.size();

            long roundOffsetTime = round * (LOGIN_CONFIRM_DURATION + EXERCISE_TIME_LIMIT);
            Long existTime = existTimes.get(remainder);
            QueuingStudent student = exerciseStudentQueuing.get(i);
            student.setTotalWaitingCount(exerciseStudentQueuing.size());
            student.setExpectedLoginTime(existTime + roundOffsetTime);
            student.setWaitingCount(i + 1);
        }
    }

    /**
     * 移除队列中超时的元素
     * 超时条件：当前时间 - 入队时间
     */
    public List<BaseQueueStudent> removeTimeoutElements(CustomPriorityQueue<? extends BaseQueueStudent> queue, long timeout, Consumer<List<BaseQueueStudent>> consumer) {
        List<BaseQueueStudent> removeStudents = ListUtil.zero();
        final long currentTime = System.currentTimeMillis();
        while (!queue.isEmpty()) {
            BaseQueueStudent headStudent = queue.peek();
            if (currentTime - headStudent.getEnterQueueTime() < timeout) {
                break;
            }
            queue.poll();
            removeStudents.add(headStudent);
        }
        if (consumer != null && CollUtil.isNotEmpty(removeStudents)) {
            consumer.accept(removeStudents);
        }
        return removeStudents;
    }

    /**
     * 获取学生排队信息
     * @param userId
     * @return
     */
    public QueuingStudent getQueueInformation(String userId) {
        if (ongoingUsers.contains(userId)) {
            // refreshOngoingStudentQueuing();
            List<QueuingStudent> queuingStudents = ongoingStudentQueuing.stream().filter(user -> user.getId().equals(userId)).toList();
            if (CollUtil.isNotEmpty(queuingStudents)) {
                return queuingStudents.get(0);
            }
            queuingStudents = ongoingQueuingSuccessful.stream().filter(user -> user.getId().equals(userId)).toList();
            if (CollUtil.isNotEmpty(queuingStudents)) {
                return queuingStudents.get(0);
            }
            return null;
        }
        // refreshExerciseStudentQueuing();

        List<QueuingStudent> queuingStudents = exerciseStudentQueuing.stream().filter(user -> user.getId().equals(userId)).toList();
        if (CollUtil.isNotEmpty(queuingStudents)) {
            return queuingStudents.get(0);
        }
        queuingStudents = exerciseQueuingSuccessful.stream().filter(user -> user.getId().equals(userId)).toList();
        if (CollUtil.isNotEmpty(queuingStudents)) {
            return queuingStudents.get(0);
        }
        return null;
    }

    /**
     * 取消排队
     * @param userId
     * @return
     */
    public void cancelQueuing(String userId) {
        CustomPriorityQueue<QueuingStudent> queuingStudents;
        CustomPriorityQueue<QueuingStudent> queueSuccessful;
        if (ongoingUsers.contains(userId)) {
            queuingStudents = ongoingStudentQueuing;
            queueSuccessful = ongoingQueuingSuccessful;
        } else {
            queuingStudents = exerciseStudentQueuing;
            queueSuccessful = exerciseQueuingSuccessful;
        }

        int index = queuingStudents.indexOf(user -> user.getId().equals(userId));
        if (index != -1) {
            List<String> userIds = new ArrayList<>();
            if (index < queuingStudents.size() - 1) {
                for (int i = index + 1; i < queuingStudents.size(); i++) {
                    userIds.add(queuingStudents.get(i).getId());
                }
            }
            queuingStudents.removeAt(index);
            notifyStudentRefreshQueue(userIds, ongoingUsers.contains(userId));
            return;
        }
        boolean res = queueSuccessful.removeIf(user -> user.getId().equals(userId));
        if (res && CollUtil.isNotEmpty(queuingStudents)) {
            List<String> userIds = queuingStudents.stream().map(BaseQueueStudent::getId).toList();
            notifyStudentRefreshQueue(userIds, ongoingUsers.contains(userId));
        }
    }


    /**
     * 通知学生刷新排队信息
     * @param userIds
     */
    public void notifyStudentRefreshQueue(List<String> userIds, boolean isRefreshOngoingQueue) {
        if (CollUtil.isEmpty(userIds)) return;
        if (isRefreshOngoingQueue) {
            refreshOngoingStudentQueuing();
        } else {
            refreshExerciseStudentQueuing();
        }
        notifyStudent(userIds, WebsocketMessage.create(WebSocketConstants.REFRESH_QUEUE_EVENT));
    }

    /**
     * 通知学生
     * @param userId
     * @param websocketMessage
     */
    public void notifyStudent(String userId, WebsocketMessage websocketMessage) {
        notifyStudent(Collections.singletonList(userId), websocketMessage);
    }

    /**
     * 通知多个学生
     * @param userIds
     * @param websocketMessage
     */
    public void notifyStudent(List<String> userIds, WebsocketMessage websocketMessage) {
        if (CollUtil.isEmpty(userIds)) return;
        String str = JSONUtil.toJsonStr(websocketMessage);
        for (String userId : userIds) {
            WebSocketHandler.sendMsgToUser(userId, str);
        }
    }

    /**
     * 获取在线学生列表
     * @return
     */
    public List<String> getOnlineStudentIds() {
        List<String> onlineUsers = new ArrayList<>();
        try {
            // 获取JWT缓存中的所有Token
            List<String> allTokens = ehcacheService.getAllValues(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, String.class);
            for (String token : allTokens) {
                if (JwtUtil.validateToken(token)) {
                    Optional.ofNullable(JwtUtil.getUserId(token))
                            .ifPresent(onlineUsers::add);
                }
            }
        } catch (Exception e) {
            log.error("获取在线用户列表失败", e);
        }
        if (CollUtil.isEmpty(onlineUsers)) {
            return onlineUsers;
        }
        List<User> userList = userDao.selectByIds(onlineUsers);
        return userList.stream()
                .filter(user -> UserType.STUDENT == user.getUserType())
                .map(User::getId)
                .toList();
    }

    /**
     * 强制退出学生
     * @param
     */
    public void forceLogout(String userId) {
        // 从缓存中删除jwt令牌
        ehcacheService.evict(CacheConstants.JWT_TOKEN_CACHE_NO_PERSISTENCE, userId);
        // 通知前端退出
        notifyStudent(userId, WebsocketMessage.create(WebSocketConstants.LOGOUT_EVENT));
    }

}
