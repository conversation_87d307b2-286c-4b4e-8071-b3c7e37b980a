package com.ruijie.nse.mgr.common.service;

import com.ruijie.nse.mgr.common.dto.EventMessage;
import com.ruijie.nse.mgr.repository.entity.enums.EventTypeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.integration.support.MessageBuilder;
import org.springframework.messaging.MessageChannel;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * 事件发布服务
 * 用于发布事件消息到Spring Integration消息通道
 *
 * <AUTHOR> Team
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class EventPublishService {

    private final MessageChannel eventInputChannel;

    /**
     * 发布事件消息
     *
     * @param eventType    事件类型
     * @param eventDetails 事件详情
     * @param eventLevel   事件级别
     * @param eventMessage 事件消息
     * @param remark       备注
     */
    public void publishEvent(EventTypeEnum eventType, String eventDetails, String eventLevel, String eventMessage, String remark) {
        EventMessage event = EventMessage.builder()
                .eventType(eventType)
                .eventDetails(eventDetails)
                .eventLevel(eventLevel)
                .eventMessage(eventMessage)
                .remark(remark)
                .createdAt(LocalDateTime.now())
                .status("NEW")
                .build();

        try {
            boolean sent = eventInputChannel.send(
                MessageBuilder.withPayload(event)
                    .setHeader("eventType", eventType.name())
                    .setHeader("eventLevel", eventLevel)
                    .setHeader("eventTimestamp", System.currentTimeMillis())
                    .build()
            );

            if (sent) {
                log.info("事件消息发布成功: type={}, message={}", eventType, eventMessage);
            } else {
                log.error("事件消息发布失败: type={}, message={}", eventType, eventMessage);
            }
        } catch (Exception e) {
            log.error("事件消息发布异常: type={}, message={}, error={}", eventType, eventMessage, e.getMessage(), e);
        }
    }

    /**
     * 发布简单事件消息
     *
     * @param eventType    事件类型
     * @param eventMessage 事件消息
     */
    public void publishEvent(EventTypeEnum eventType, String eventMessage, String eventDetails) {
        publishEvent(eventType, eventDetails, eventType.getLevel(), eventMessage, null);
    }

    /**
     * 发布错误事件消息
     *
     * @param eventType    事件类型
     * @param eventMessage 事件消息
     * @param errorDetails 错误详情
     */
    public void publishErrorEvent(EventTypeEnum eventType, String eventMessage, String errorDetails) {
        publishEvent(eventType, errorDetails, "ERROR", eventMessage, null);
    }

    /**
     * 发布警告事件消息
     *
     * @param eventType    事件类型
     * @param eventMessage 事件消息
     * @param warningDetails 警告详情
     */
    public void publishWarningEvent(EventTypeEnum eventType, String eventMessage, String warningDetails) {
        publishEvent(eventType, warningDetails, "WARN", eventMessage, null);
    }

    /**
     *
     * @param eventMessage
     * @param eventDetails
     */
    public void publishLicenseVerifyErrorEvent(String eventMessage, String eventDetails) {
        publishEvent(EventTypeEnum.EVT_LIC_VERIFY_FAILED, eventDetails, EventTypeEnum.EVT_LIC_VERIFY_FAILED.getLevel(), eventMessage, null);
    }
}